<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录/注册 - Materials Studio 答疑平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #0d47a1;
            --accent-color: #03a9f4;
            --light-bg: #f5f5f5;
            --member-color: #ffc107;
            --border-radius: 8px;
            --box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 4px 8px rgba(0,0,0,0.06);
            --transition-speed: 0.3s;
        }
        
        body {
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
            background-color: var(--light-bg);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 600;
        }
        
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.85);
            transition: all var(--transition-speed);
        }
        
        .navbar-dark .navbar-nav .nav-link:hover {
            color: rgba(255,255,255,1);
        }
        
        .auth-container {
            max-width: 500px;
            margin: 50px auto;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
        }
        
        .form-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .form-tab {
            padding: 12px 24px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 500;
            color: #6c757d;
            background: none;
            border: none;
            outline: none;
            position: relative;
        }
        
        .form-tab.active {
            color: var(--primary-color);
        }
        
        .form-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
        }
        
        .form-tab-content {
            display: none;
        }
        
        .form-tab-content.active {
            display: block;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: #6c757d;
        }
        
        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background-color: #dee2e6;
        }
        
        .divider span {
            padding: 0 10px;
        }
        
        .social-login {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .social-login button {
            margin: 0 10px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            transition: all 0.3s ease;
        }
        
        .social-login button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-control {
            padding: 12px 15px;
            border-radius: var(--border-radius);
            border: 1px solid #ced4da;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(25, 118, 210, 0.25);
        }
        
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 12px 20px;
            font-weight: 500;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-text {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .auth-message {
            padding: 15px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            display: none;
        }
        
        .auth-message.success {
            background-color: #d1e7dd;
            color: #0f5132;
            border: 1px solid #badbcc;
            display: block;
        }
        
        .auth-message.error {
            background-color: #f8d7da;
            color: #842029;
            border: 1px solid #f5c2c7;
            display: block;
        }
        
        footer {
            background-color: #212121;
            color: white;
            padding: 20px 0;
            margin-top: auto;
        }

        /* 加载指示器样式 */
        .spinner-container {
            display: none;
            text-align: center;
            margin: 10px 0;
        }
        
        .spinner-container.show {
            display: block;
        }
        
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">Materials Studio 答疑平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="qa.html"><i class="fas fa-question-circle"></i> 问答</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html"><i class="fas fa-book"></i> 学习资源</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container">
        <div class="auth-container">
            <div id="auth-message" class="auth-message"></div>
            
            <div class="form-tabs">
                <button class="form-tab active" data-tab="login">登录</button>
                <button class="form-tab" data-tab="register">注册</button>
            </div>
            
            <!-- 登录表单 -->
            <div id="login-form" class="form-tab-content active">
                <form id="login-form-element">
                    <div class="mb-3">
                        <label for="login-username" class="form-label">用户名或邮箱</label>
                        <input type="text" class="form-control" id="login-username" name="username" required>
                    </div>
                    <div class="mb-3 position-relative">
                        <label for="login-password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="login-password" name="password" required>
                        <span class="password-toggle" onclick="togglePasswordVisibility('login-password', this)">
                            <i class="far fa-eye"></i>
                        </span>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="login-remember">
                        <label class="form-check-label" for="login-remember">记住我</label>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </div>
                    <div class="text-center">
                        <a href="#" class="text-decoration-none">忘记密码？</a>
                    </div>
                    <div class="spinner-container" id="login-spinner">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p>正在登录，请稍候...</p>
                    </div>
                </form>
                
                <div class="divider">
                    <span>或使用以下方式登录</span>
                </div>
                
                <div class="social-login">
                    <button class="btn btn-outline-primary">
                        <i class="fab fa-weixin"></i>
                    </button>
                    <button class="btn btn-outline-danger">
                        <i class="fab fa-weibo"></i>
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fab fa-qq"></i>
                    </button>
                </div>
            </div>
            
            <!-- 注册表单 -->
            <div id="register-form" class="form-tab-content">
                <form id="register-form-element">
                    <div class="mb-3">
                        <label for="register-username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="register-username" name="username" required>
                        <div class="form-text">用户名将用于登录和展示</div>
                    </div>
                    <div class="mb-3">
                        <label for="register-email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="register-email" name="email" required>
                        <div class="form-text">用于接收验证码和通知</div>
                    </div>
                    <div class="mb-3">
                        <label for="register-phone" class="form-label">手机号（选填）</label>
                        <input type="tel" class="form-control" id="register-phone" name="phone">
                        <div class="form-text">用于接收验证码和通知</div>
                    </div>
                    <div class="mb-3 position-relative">
                        <label for="register-password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="register-password" name="password" required>
                        <span class="password-toggle" onclick="togglePasswordVisibility('register-password', this)">
                            <i class="far fa-eye"></i>
                        </span>
                        <div class="form-text">密码至少包含8个字符</div>
                    </div>
                    <div class="mb-3 position-relative">
                        <label for="register-confirm-password" class="form-label">确认密码</label>
                        <input type="password" class="form-control" id="register-confirm-password" name="confirm_password" required>
                        <span class="password-toggle" onclick="togglePasswordVisibility('register-confirm-password', this)">
                            <i class="far fa-eye"></i>
                        </span>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="register-terms" required>
                        <label class="form-check-label" for="register-terms">我已阅读并同意<a href="#" class="text-decoration-none">服务条款</a>和<a href="#" class="text-decoration-none">隐私政策</a></label>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">注册</button>
                    <div class="spinner-container" id="register-spinner">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p>正在注册，请稍候...</p>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    <p class="mb-0">© 2023 Materials Studio 答疑平台 版权所有</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- API请求脚本 -->
    <script src="js/api/config.js"></script>
    <script src="js/api/utils.js"></script>
    <script src="js/api/auth.js"></script>
    <script src="js/api/index.js"></script>

    <script>
        // 初始化表单切换
        const formTabs = document.querySelectorAll('.form-tab');
        const formContents = document.querySelectorAll('.form-tab-content');
        
        formTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                
                // 切换标签激活状态
                formTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // 切换表单内容
                formContents.forEach(content => {
                    if (content.id === `${tabId}-form`) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
                
                // 清除消息
                clearAuthMessage();
            });
        });
        
        // 切换密码可见性
        function togglePasswordVisibility(inputId, toggleIcon) {
            const input = document.getElementById(inputId);
            const icon = toggleIcon.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        // 显示认证消息
        function showAuthMessage(message, type = 'error') {
            const messageElement = document.getElementById('auth-message');
            messageElement.textContent = message;
            messageElement.className = `auth-message ${type}`;
        }
        
        // 清除认证消息
        function clearAuthMessage() {
            const messageElement = document.getElementById('auth-message');
            messageElement.textContent = '';
            messageElement.className = 'auth-message';
        }

        // 显示加载指示器
        function showSpinner(formId) {
            document.getElementById(`${formId}-spinner`).classList.add('show');
        }

        // 隐藏加载指示器
        function hideSpinner(formId) {
            document.getElementById(`${formId}-spinner`).classList.remove('show');
        }
        
        // 登录表单提交
        document.getElementById('login-form-element').addEventListener('submit', async function(event) {
            event.preventDefault();
            
            // 清除之前的消息
            clearAuthMessage();
            
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;
            
            if (!username || !password) {
                showAuthMessage('请输入用户名和密码');
                return;
            }
            
            // 显示加载指示器
            showSpinner('login');
            
            try {
                // 调用登录API
                const response = await window.api.auth.login(username, password);
                
                // 显示成功消息
                showAuthMessage('登录成功，正在跳转...', 'success');
                
                // 记住登录状态（如果选中）
                const rememberMe = document.getElementById('login-remember').checked;
                if (rememberMe) {
                    localStorage.setItem('remember_login', 'true');
                }
                
                // 跳转到首页
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } catch (error) {
                // 显示错误消息
                showAuthMessage(error.message || '登录失败，请检查用户名和密码');
            } finally {
                // 隐藏加载指示器
                hideSpinner('login');
            }
        });
        
        // 注册表单提交
        document.getElementById('register-form-element').addEventListener('submit', async function(event) {
            event.preventDefault();
            
            // 清除之前的消息
            clearAuthMessage();
            
            const username = document.getElementById('register-username').value;
            const email = document.getElementById('register-email').value;
            const phone = document.getElementById('register-phone').value;
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;
            const termsAccepted = document.getElementById('register-terms').checked;
            
            // 表单验证
            if (!username || !email || !password || !confirmPassword) {
                showAuthMessage('请填写所有必填字段');
                return;
            }
            
            if (password.length < 8) {
                showAuthMessage('密码至少需要8个字符');
                return;
            }
            
            if (password !== confirmPassword) {
                showAuthMessage('两次输入的密码不一致');
                return;
            }
            
            if (!termsAccepted) {
                showAuthMessage('请阅读并同意服务条款和隐私政策');
                return;
            }
            
            // 显示加载指示器
            showSpinner('register');
            
            try {
                // 调用注册API
                const userData = {
                    username,
                    email,
                    password,
                    phone: phone || undefined
                };
                
                const response = await window.api.auth.register(userData);
                
                // 显示成功消息
                showAuthMessage('注册成功，请登录', 'success');
                
                // 清空表单
                event.target.reset();
                
                // 切换到登录表单
                setTimeout(() => {
                    document.querySelector('.form-tab[data-tab="login"]').click();
                }, 1500);
            } catch (error) {
                // 显示错误消息
                showAuthMessage(error.message || '注册失败，请稍后重试');
            } finally {
                // 隐藏加载指示器
                hideSpinner('register');
            }
        });

        // 页面加载时检查是否已登录
        document.addEventListener('api:ready', async () => {
            try {
                if (window.api.auth.isLoggedIn()) {
                    try {
                        // 尝试获取当前用户信息，验证token是否有效
                        await window.api.auth.getCurrentUser();
                        
                        // token有效，显示已登录消息
                        showAuthMessage('您已登录，正在跳转到首页...', 'success');
                        
                        // 跳转到首页
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1500);
                    } catch (error) {
                        // token无效，不做任何操作，让用户重新登录
                        console.log('登录状态已过期，需要重新登录');
                    }
                }
            } catch (error) {
                console.error('检查登录状态错误:', error);
            }
        });
    </script>
</body>
</html> 