package com.materialsstudio.qa.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 分页结果视图对象
 *
 * @param <T> 数据类型
 */
@Data
public class PageVO<T> {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 当前页码
     */
    private Integer current;
    
    /**
     * 每页记录数
     */
    private Integer size;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
} 