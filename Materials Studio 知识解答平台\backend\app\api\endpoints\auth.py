from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.core.security import create_access_token
from app.core.deps import get_current_user
from app.db.session import get_db
from app.models.user import User
from app.schemas.user import User as UserSchema, UserCreate, Token
from app.services import user_service

router = APIRouter()


@router.post("/login", response_model=Token)
def login(
    db: Session = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    用户登录
    
    Args:
        db: 数据库会话
        form_data: 表单数据，包含用户名和密码
        
    Returns:
        Token: 访问令牌
        
    Raises:
        HTTPException: 如果用户名或密码错误
    """
    # 尝试认证用户
    user = user_service.authenticate(db, email=form_data.username, password=form_data.password)
    
    # 如果没有找到匹配的用户，尝试使用用户名登录
    if not user:
        user = user_service.get_by_username(db, username=form_data.username)
        if user and user_service.verify_password(form_data.password, user.hashed_password):
            # 验证密码成功
            pass
        else:
            # 验证失败
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=60 * 24 * 8)  # 8天
    token = create_access_token(
        subject=user.id, expires_delta=access_token_expires
    )
    
    return {"access_token": token, "token_type": "bearer"}


@router.post("/register", response_model=UserSchema)
def register(
    *,
    db: Session = Depends(get_db),
    user_in: UserCreate
) -> Any:
    """
    用户注册
    
    Args:
        db: 数据库会话
        user_in: 用户创建模式
        
    Returns:
        User: 创建的用户
        
    Raises:
        HTTPException: 如果用户名或邮箱已存在
    """
    # 检查邮箱是否已存在
    user = user_service.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已注册",
        )
    
    # 检查用户名是否已存在
    user = user_service.get_by_username(db, username=user_in.username)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )
    
    # 创建用户
    user = user_service.create(db, obj_in=user_in)
    return user


@router.get("/me", response_model=UserSchema)
def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    获取当前用户信息
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前用户信息
    """
    return current_user 