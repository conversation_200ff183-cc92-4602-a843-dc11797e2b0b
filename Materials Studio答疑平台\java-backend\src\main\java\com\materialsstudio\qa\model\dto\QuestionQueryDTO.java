package com.materialsstudio.qa.model.dto;

import lombok.Data;

/**
 * 问题分页查询DTO
 */
@Data
public class QuestionQueryDTO {
    
    /**
     * 页码
     */
    private Integer page = 1;
    
    /**
     * 每页数量
     */
    private Integer size = 10;
    
    /**
     * 标题关键字
     */
    private String keyword;
    
    /**
     * 标签ID
     */
    private Long tagId;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 是否为会员专属
     */
    private Boolean isPremium;
    
    /**
     * 状态：0-未解决，1-已解决
     */
    private Integer status;
    
    /**
     * 排序字段
     */
    private String sortField = "create_time";
    
    /**
     * 排序方式：asc-升序，desc-降序
     */
    private String sortOrder = "desc";
} 