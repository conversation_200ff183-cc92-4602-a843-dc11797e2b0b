from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.config.settings import settings

# 创建SQLAlchemy引擎
engine = create_engine(
    settings.SQLALCHEMY_DATABASE_URI, 
    connect_args={"check_same_thread": False}  # 仅用于SQLite
)

# 创建SessionLocal类，每个实例将是一个数据库会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建Base类，所有ORM模型将继承此类
Base = declarative_base()


# 依赖项函数
def get_db():
    """
    获取数据库会话
    
    Yields:
        SQLAlchemy会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 