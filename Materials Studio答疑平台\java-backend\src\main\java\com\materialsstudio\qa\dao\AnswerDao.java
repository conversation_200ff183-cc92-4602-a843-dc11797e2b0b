package com.materialsstudio.qa.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.materialsstudio.qa.entity.Answer;
import com.materialsstudio.qa.model.vo.AnswerVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 回答DAO接口
 */
public interface AnswerDao extends BaseMapper<Answer> {
    
    /**
     * 根据问题ID获取回答列表
     *
     * @param questionId 问题ID
     * @return 回答列表
     */
    @Select("SELECT a.*, u.username AS author_name, u.avatar AS author_avatar " +
            "FROM answer a " +
            "LEFT JOIN user u ON a.author_id = u.id " +
            "WHERE a.question_id = #{questionId} AND a.deleted = 0 " +
            "ORDER BY a.is_accepted DESC, a.votes DESC, a.create_time ASC")
    List<AnswerVO> getAnswersByQuestionId(@Param("questionId") Long questionId);
    
    /**
     * 采纳回答
     *
     * @param id 回答ID
     * @return 影响行数
     */
    @Update("UPDATE answer SET is_accepted = 1 WHERE id = #{id}")
    int acceptAnswer(@Param("id") Long id);
    
    /**
     * 取消所有采纳（同一问题下）
     *
     * @param questionId 问题ID
     * @return 影响行数
     */
    @Update("UPDATE answer SET is_accepted = 0 WHERE question_id = (SELECT question_id FROM answer WHERE id = #{answerId})")
    int cancelAllAccepted(@Param("answerId") Long answerId);
    
    /**
     * 点赞回答
     *
     * @param id 回答ID
     * @return 影响行数
     */
    @Update("UPDATE answer SET votes = votes + 1 WHERE id = #{id}")
    int voteAnswer(@Param("id") Long id);
} 