-- 创建数据库
CREATE DATABASE IF NOT EXISTS `materials_studio_qa` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `materials_studio_qa`;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `email` varchar(100) NOT NULL COMMENT '电子邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
    `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像URL',
    `role` varchar(20) NOT NULL DEFAULT 'user' COMMENT '用户角色：admin-管理员，member-会员，user-普通用户',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户状态：0-禁用，1-正常',
    `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 会员表
CREATE TABLE IF NOT EXISTS `membership` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `level` varchar(20) NOT NULL COMMENT '会员等级：standard-标准会员，premium-高级会员',
    `level_name` varchar(50) NOT NULL COMMENT '会员等级名称',
    `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '会员状态：0-过期，1-有效',
    `start_date` datetime NOT NULL COMMENT '开始时间',
    `end_date` datetime NOT NULL COMMENT '结束时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表';

-- 问题表
CREATE TABLE IF NOT EXISTS `question` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '问题ID',
    `title` varchar(255) NOT NULL COMMENT '问题标题',
    `content` text NOT NULL COMMENT '问题内容',
    `author_id` bigint(20) NOT NULL COMMENT '作者ID',
    `is_premium` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为会员专属：0-否，1-是',
    `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-未解决，1-已解决',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_author_id` (`author_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题表';

-- 回答表
CREATE TABLE IF NOT EXISTS `answer` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '回答ID',
    `question_id` bigint(20) NOT NULL COMMENT '问题ID',
    `content` text NOT NULL COMMENT '回答内容',
    `author_id` bigint(20) NOT NULL COMMENT '作者ID',
    `is_accepted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被采纳：0-否，1-是',
    `is_premium` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为会员专属：0-否，1-是',
    `votes` int(11) NOT NULL DEFAULT '0' COMMENT '点赞数量',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_question_id` (`question_id`),
    KEY `idx_author_id` (`author_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回答表';

-- 标签表
CREATE TABLE IF NOT EXISTS `tag` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `name` varchar(50) NOT NULL COMMENT '标签名称',
    `description` varchar(255) DEFAULT NULL COMMENT '标签描述',
    `count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 问题标签关联表
CREATE TABLE IF NOT EXISTS `question_tag` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `question_id` bigint(20) NOT NULL COMMENT '问题ID',
    `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_question_tag` (`question_id`,`tag_id`),
    KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题标签关联表';

-- 评论表
CREATE TABLE IF NOT EXISTS `comment` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
    `content` varchar(500) NOT NULL COMMENT '评论内容',
    `author_id` bigint(20) NOT NULL COMMENT '作者ID',
    `question_id` bigint(20) DEFAULT NULL COMMENT '问题ID',
    `answer_id` bigint(20) DEFAULT NULL COMMENT '回答ID',
    `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_question_id` (`question_id`),
    KEY `idx_answer_id` (`answer_id`),
    KEY `idx_author_id` (`author_id`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 资源表
CREATE TABLE IF NOT EXISTS `resource` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '资源ID',
    `title` varchar(255) NOT NULL COMMENT '资源标题',
    `description` text COMMENT '资源描述',
    `type` varchar(20) NOT NULL COMMENT '资源类型：video-视频，document-文档',
    `url` varchar(255) NOT NULL COMMENT '资源URL',
    `author_id` bigint(20) NOT NULL COMMENT '上传者ID',
    `is_premium` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为会员专属：0-否，1-是',
    `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_author_id` (`author_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源表';

-- 收藏表
CREATE TABLE IF NOT EXISTS `favorite` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `question_id` bigint(20) DEFAULT NULL COMMENT '问题ID',
    `answer_id` bigint(20) DEFAULT NULL COMMENT '回答ID',
    `resource_id` bigint(20) DEFAULT NULL COMMENT '资源ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_question` (`user_id`,`question_id`),
    UNIQUE KEY `uk_user_answer` (`user_id`,`answer_id`),
    UNIQUE KEY `uk_user_resource` (`user_id`,`resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏表';

-- 初始化数据
INSERT INTO `user` (`username`, `password`, `email`, `role`) VALUES
('admin', '$2a$10$FO/4lZIcXQQq1YnFgIQ7B.MfnQ2/tEYLuXc7Dz.VHbYMGXNB2CZkS', '<EMAIL>', 'admin'),
('member', '$2a$10$FO/4lZIcXQQq1YnFgIQ7B.MfnQ2/tEYLuXc7Dz.VHbYMGXNB2CZkS', '<EMAIL>', 'member'),
('user', '$2a$10$FO/4lZIcXQQq1YnFgIQ7B.MfnQ2/tEYLuXc7Dz.VHbYMGXNB2CZkS', '<EMAIL>', 'user');

INSERT INTO `membership` (`user_id`, `level`, `level_name`, `is_active`, `start_date`, `end_date`) VALUES
(1, 'premium', '高级会员', 1, '2023-01-01 00:00:00', '2024-01-01 00:00:00'),
(2, 'standard', '标准会员', 1, '2023-01-02 00:00:00', '2023-07-02 00:00:00');

INSERT INTO `tag` (`name`, `description`, `count`) VALUES
('分子动力学', '分子动力学模拟相关', 120),
('吸附', '吸附相关研究', 85),
('模拟', '各类计算模拟', 200),
('表面', '表面科学', 65),
('DFT', '密度泛函理论', 150);

INSERT INTO `question` (`title`, `content`, `author_id`, `is_premium`, `view_count`) VALUES
('Materials Studio中如何进行分子动力学模拟？', '我正在尝试使用Materials Studio进行分子动力学模拟，但不太清楚具体的步骤和参数设置。请问有谁能提供详细的操作指南吗？', 3, 0, 120),
('如何在Materials Studio中计算吸附能？', '我需要计算一个分子在表面上的吸附能，请问在Materials Studio中应该如何设置计算参数？', 2, 1, 85),
('Materials Studio中CASTEP模块的使用问题', '在使用CASTEP模块进行DFT计算时遇到了收敛问题，请问应该如何调整参数以提高计算效率和准确性？', 3, 0, 62);

INSERT INTO `question_tag` (`question_id`, `tag_id`) VALUES
(1, 1), (1, 3),
(2, 2), (2, 4),
(3, 1), (3, 5);

INSERT INTO `answer` (`question_id`, `content`, `author_id`, `is_accepted`, `is_premium`, `votes`) VALUES
(1, '分子动力学模拟的基本步骤如下：\n1. 创建或导入分子结构\n2. 设置力场参数\n3. 设置模拟条件（温度、压力等）\n4. 运行模拟\n5. 分析结果\n\n详细步骤可以参考官方文档...', 2, 0, 0, 5),
(1, '补充一点，在进行分子动力学模拟时，选择合适的力场非常重要。对于不同类型的体系，推荐的力场如下：\n- 有机分子：COMPASS、PCFF\n- 生物分子：CHARMM、AMBER\n- 无机材料：CLAYFF、INTERFACE\n\n另外，模拟前的结构优化也是必不可少的步骤...', 1, 1, 1, 12),
(2, '计算吸附能的步骤：\n1. 分别优化吸附剂和吸附质\n2. 构建吸附构型并优化\n3. 计算吸附能：E_ads = E_complex - (E_adsorbent + E_adsorbate)\n\n注意要使用相同的计算方法和参数...', 1, 1, 1, 8); 