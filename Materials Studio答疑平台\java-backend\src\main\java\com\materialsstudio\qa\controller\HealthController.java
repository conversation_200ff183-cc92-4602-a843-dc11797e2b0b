package com.materialsstudio.qa.controller;

import com.materialsstudio.qa.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
public class HealthController {
    
    /**
     * 健康检查
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    public Result<Map<String, String>> health() {
        Map<String, String> healthInfo = new HashMap<>();
        healthInfo.put("status", "ok");
        healthInfo.put("service", "Materials Studio QA Platform");
        healthInfo.put("timestamp", String.valueOf(System.currentTimeMillis()));
        return Result.success(healthInfo);
    }
} 