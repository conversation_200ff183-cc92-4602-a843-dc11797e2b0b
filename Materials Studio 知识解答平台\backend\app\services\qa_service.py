from typing import List, Optional

from sqlalchemy import func
from sqlalchemy.orm import Session, joinedload

from app.models.qa import Question, Answer, Comment, Tag, QuestionAttachment, AnswerAttachment
from app.models.user import User, Favorite
from app.schemas.qa import (
    QuestionCreate, QuestionUpdate, 
    AnswerCreate, AnswerUpdate, 
    CommentCreate, CommentUpdate,
    TagCreate, TagUpdate,
    QuestionAttachmentCreate,
    AnswerAttachmentCreate
)


# 问题相关操作
def get_question_by_id(db: Session, question_id: int) -> Optional[Question]:
    """
    根据ID获取问题
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        
    Returns:
        问题对象，如果不存在则为None
    """
    return db.query(Question).filter(Question.id == question_id).first()


def get_question_with_details(db: Session, question_id: int) -> Optional[Question]:
    """
    获取带有详细信息的问题（包括作者、标签、回答等）
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        
    Returns:
        问题对象，如果不存在则为None
    """
    return db.query(Question)\
        .options(
            joinedload(Question.author),
            joinedload(Question.tags),
            joinedload(Question.attachments),
            joinedload(Question.answers).joinedload(Answer.author),
            joinedload(Question.answers).joinedload(Answer.comments).joinedload(Comment.author),
            joinedload(Question.answers).joinedload(Answer.attachments)
        )\
        .filter(Question.id == question_id)\
        .first()


def get_questions(
    db: Session, 
    skip: int = 0, 
    limit: int = 10, 
    tag_id: Optional[int] = None,
    author_id: Optional[int] = None,
    search: Optional[str] = None,
    only_premium: bool = False,
    only_solved: bool = False
) -> List[Question]:
    """
    获取问题列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        tag_id: 标签ID过滤
        author_id: 作者ID过滤
        search: 搜索关键词
        only_premium: 是否只返回会员内容
        only_solved: 是否只返回已解决的问题
        
    Returns:
        问题列表
    """
    query = db.query(Question)\
        .options(
            joinedload(Question.author),
            joinedload(Question.tags)
        )
    
    # 应用过滤条件
    if tag_id:
        query = query.join(Question.tags).filter(Tag.id == tag_id)
    
    if author_id:
        query = query.filter(Question.author_id == author_id)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(Question.title.ilike(search_term) | Question.content.ilike(search_term))
    
    if only_premium:
        query = query.filter(Question.is_premium == True)
    
    if only_solved:
        query = query.filter(Question.is_solved == True)
    
    # 按创建时间降序排序
    query = query.order_by(Question.created_at.desc())
    
    return query.offset(skip).limit(limit).all()


def get_questions_count(
    db: Session,
    tag_id: Optional[int] = None,
    author_id: Optional[int] = None,
    search: Optional[str] = None,
    only_premium: bool = False,
    only_solved: bool = False
) -> int:
    """
    获取问题总数
    
    Args:
        db: 数据库会话
        tag_id: 标签ID过滤
        author_id: 作者ID过滤
        search: 搜索关键词
        only_premium: 是否只返回会员内容
        only_solved: 是否只返回已解决的问题
        
    Returns:
        问题总数
    """
    query = db.query(func.count(Question.id))
    
    # 应用过滤条件
    if tag_id:
        query = query.join(Question.tags).filter(Tag.id == tag_id)
    
    if author_id:
        query = query.filter(Question.author_id == author_id)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(Question.title.ilike(search_term) | Question.content.ilike(search_term))
    
    if only_premium:
        query = query.filter(Question.is_premium == True)
    
    if only_solved:
        query = query.filter(Question.is_solved == True)
    
    return query.scalar()


def create_question(db: Session, obj_in: QuestionCreate) -> Question:
    """
    创建问题
    
    Args:
        db: 数据库会话
        obj_in: 问题创建模式
        
    Returns:
        创建的问题对象
    """
    # 从输入数据中提取标签ID
    tag_ids = obj_in.tag_ids
    obj_data = obj_in.dict(exclude={"tag_ids"})
    
    # 创建问题对象
    db_obj = Question(**obj_data)
    
    # 添加标签
    if tag_ids:
        tags = db.query(Tag).filter(Tag.id.in_(tag_ids)).all()
        db_obj.tags = tags
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_question(db: Session, *, db_obj: Question, obj_in: QuestionUpdate) -> Question:
    """
    更新问题
    
    Args:
        db: 数据库会话
        db_obj: 要更新的问题对象
        obj_in: 问题更新模式
        
    Returns:
        更新后的问题对象
    """
    update_data = obj_in.dict(exclude_unset=True, exclude={"tag_ids"})
    
    # 更新基本字段
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    # 更新标签
    if obj_in.tag_ids is not None:
        tags = db.query(Tag).filter(Tag.id.in_(obj_in.tag_ids)).all()
        db_obj.tags = tags
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_question(db: Session, question_id: int) -> None:
    """
    删除问题
    
    Args:
        db: 数据库会话
        question_id: 问题ID
    """
    question = db.query(Question).filter(Question.id == question_id).first()
    if question:
        db.delete(question)
        db.commit()


def increment_question_view(db: Session, db_obj: Question) -> Question:
    """
    增加问题浏览量
    
    Args:
        db: 数据库会话
        db_obj: 问题对象
        
    Returns:
        更新后的问题对象
    """
    db_obj.views += 1
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


# 回答相关操作
def get_answer_by_id(db: Session, answer_id: int) -> Optional[Answer]:
    """
    根据ID获取回答
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        
    Returns:
        回答对象，如果不存在则为None
    """
    return db.query(Answer).filter(Answer.id == answer_id).first()


def get_answers_by_question(db: Session, question_id: int) -> List[Answer]:
    """
    获取问题的所有回答
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        
    Returns:
        回答列表
    """
    return db.query(Answer)\
        .options(
            joinedload(Answer.author),
            joinedload(Answer.comments).joinedload(Comment.author),
            joinedload(Answer.attachments)
        )\
        .filter(Answer.question_id == question_id)\
        .order_by(Answer.is_accepted.desc(), Answer.upvotes.desc(), Answer.created_at.desc())\
        .all()


def create_answer(db: Session, obj_in: AnswerCreate) -> Answer:
    """
    创建回答
    
    Args:
        db: 数据库会话
        obj_in: 回答创建模式
        
    Returns:
        创建的回答对象
    """
    db_obj = Answer(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_answer(db: Session, *, db_obj: Answer, obj_in: AnswerUpdate) -> Answer:
    """
    更新回答
    
    Args:
        db: 数据库会话
        db_obj: 要更新的回答对象
        obj_in: 回答更新模式
        
    Returns:
        更新后的回答对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_answer(db: Session, answer_id: int) -> None:
    """
    删除回答
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
    """
    answer = db.query(Answer).filter(Answer.id == answer_id).first()
    if answer:
        db.delete(answer)
        db.commit()


def accept_answer(db: Session, answer_id: int, question_id: int) -> Answer:
    """
    接受回答
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        question_id: 问题ID
        
    Returns:
        接受的回答对象
    """
    # 先将所有回答设为未接受
    db.query(Answer).filter(Answer.question_id == question_id).update({"is_accepted": False})
    
    # 接受指定回答
    answer = db.query(Answer).filter(Answer.id == answer_id).first()
    if answer:
        answer.is_accepted = True
        
        # 将问题标记为已解决
        question = db.query(Question).filter(Question.id == question_id).first()
        if question:
            question.is_solved = True
            db.add(question)
        
        db.add(answer)
        db.commit()
        db.refresh(answer)
    
    return answer


def vote_answer(db: Session, answer_id: int, vote_type: str) -> Answer:
    """
    对回答进行投票
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        vote_type: 投票类型（upvote 或 downvote）
        
    Returns:
        投票后的回答对象
    """
    answer = db.query(Answer).filter(Answer.id == answer_id).first()
    if answer:
        if vote_type == "upvote":
            answer.upvotes += 1
        elif vote_type == "downvote":
            answer.downvotes += 1
        
        db.add(answer)
        db.commit()
        db.refresh(answer)
    
    return answer


# 评论相关操作
def get_comment_by_id(db: Session, comment_id: int) -> Optional[Comment]:
    """
    根据ID获取评论
    
    Args:
        db: 数据库会话
        comment_id: 评论ID
        
    Returns:
        评论对象，如果不存在则为None
    """
    return db.query(Comment).filter(Comment.id == comment_id).first()


def get_comments_by_answer(db: Session, answer_id: int) -> List[Comment]:
    """
    获取回答的所有评论
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        
    Returns:
        评论列表
    """
    return db.query(Comment)\
        .options(joinedload(Comment.author))\
        .filter(Comment.answer_id == answer_id)\
        .order_by(Comment.created_at)\
        .all()


def create_comment(db: Session, obj_in: CommentCreate) -> Comment:
    """
    创建评论
    
    Args:
        db: 数据库会话
        obj_in: 评论创建模式
        
    Returns:
        创建的评论对象
    """
    db_obj = Comment(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_comment(db: Session, *, db_obj: Comment, obj_in: CommentUpdate) -> Comment:
    """
    更新评论
    
    Args:
        db: 数据库会话
        db_obj: 要更新的评论对象
        obj_in: 评论更新模式
        
    Returns:
        更新后的评论对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_comment(db: Session, comment_id: int) -> None:
    """
    删除评论
    
    Args:
        db: 数据库会话
        comment_id: 评论ID
    """
    comment = db.query(Comment).filter(Comment.id == comment_id).first()
    if comment:
        db.delete(comment)
        db.commit()


# 标签相关操作
def get_tag_by_id(db: Session, tag_id: int) -> Optional[Tag]:
    """
    根据ID获取标签
    
    Args:
        db: 数据库会话
        tag_id: 标签ID
        
    Returns:
        标签对象，如果不存在则为None
    """
    return db.query(Tag).filter(Tag.id == tag_id).first()


def get_tag_by_name(db: Session, name: str) -> Optional[Tag]:
    """
    根据名称获取标签
    
    Args:
        db: 数据库会话
        name: 标签名称
        
    Returns:
        标签对象，如果不存在则为None
    """
    return db.query(Tag).filter(Tag.name == name).first()


def get_all_tags(db: Session) -> List[Tag]:
    """
    获取所有标签
    
    Args:
        db: 数据库会话
        
    Returns:
        标签列表
    """
    return db.query(Tag).order_by(Tag.name).all()


def create_tag(db: Session, obj_in: TagCreate) -> Tag:
    """
    创建标签
    
    Args:
        db: 数据库会话
        obj_in: 标签创建模式
        
    Returns:
        创建的标签对象
    """
    db_obj = Tag(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_tag(db: Session, *, db_obj: Tag, obj_in: TagUpdate) -> Tag:
    """
    更新标签
    
    Args:
        db: 数据库会话
        db_obj: 要更新的标签对象
        obj_in: 标签更新模式
        
    Returns:
        更新后的标签对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_tag(db: Session, tag_id: int) -> None:
    """
    删除标签
    
    Args:
        db: 数据库会话
        tag_id: 标签ID
    """
    tag = db.query(Tag).filter(Tag.id == tag_id).first()
    if tag:
        db.delete(tag)
        db.commit()


# 附件相关操作
def create_question_attachment(db: Session, obj_in: QuestionAttachmentCreate) -> QuestionAttachment:
    """
    创建问题附件
    
    Args:
        db: 数据库会话
        obj_in: 问题附件创建模式
        
    Returns:
        创建的问题附件对象
    """
    db_obj = QuestionAttachment(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def create_answer_attachment(db: Session, obj_in: AnswerAttachmentCreate) -> AnswerAttachment:
    """
    创建回答附件
    
    Args:
        db: 数据库会话
        obj_in: 回答附件创建模式
        
    Returns:
        创建的回答附件对象
    """
    db_obj = AnswerAttachment(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_question_attachment(db: Session, attachment_id: int) -> None:
    """
    删除问题附件
    
    Args:
        db: 数据库会话
        attachment_id: 附件ID
    """
    attachment = db.query(QuestionAttachment).filter(QuestionAttachment.id == attachment_id).first()
    if attachment:
        db.delete(attachment)
        db.commit()


def delete_answer_attachment(db: Session, attachment_id: int) -> None:
    """
    删除回答附件
    
    Args:
        db: 数据库会话
        attachment_id: 附件ID
    """
    attachment = db.query(AnswerAttachment).filter(AnswerAttachment.id == attachment_id).first()
    if attachment:
        db.delete(attachment)
        db.commit()


# 收藏相关操作
def add_to_favorites(db: Session, user_id: int, question_id: Optional[int] = None, resource_id: Optional[int] = None) -> Favorite:
    """
    添加到收藏
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        question_id: 问题ID（可选）
        resource_id: 资源ID（可选）
        
    Returns:
        创建的收藏对象
    """
    # 检查是否已经收藏
    query = db.query(Favorite).filter(Favorite.user_id == user_id)
    if question_id:
        query = query.filter(Favorite.question_id == question_id)
    if resource_id:
        query = query.filter(Favorite.resource_id == resource_id)
    
    existing = query.first()
    if existing:
        return existing
    
    # 创建新收藏
    db_obj = Favorite(
        user_id=user_id,
        question_id=question_id,
        resource_id=resource_id
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def remove_from_favorites(db: Session, user_id: int, question_id: Optional[int] = None, resource_id: Optional[int] = None) -> None:
    """
    从收藏中移除
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        question_id: 问题ID（可选）
        resource_id: 资源ID（可选）
    """
    query = db.query(Favorite).filter(Favorite.user_id == user_id)
    if question_id:
        query = query.filter(Favorite.question_id == question_id)
    if resource_id:
        query = query.filter(Favorite.resource_id == resource_id)
    
    favorite = query.first()
    if favorite:
        db.delete(favorite)
        db.commit()


def get_user_favorites(db: Session, user_id: int) -> List[Favorite]:
    """
    获取用户的所有收藏
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        收藏列表
    """
    return db.query(Favorite)\
        .options(
            joinedload(Favorite.question),
            joinedload(Favorite.resource)
        )\
        .filter(Favorite.user_id == user_id)\
        .order_by(Favorite.created_at.desc())\
        .all()


def is_favorite(db: Session, user_id: int, question_id: Optional[int] = None, resource_id: Optional[int] = None) -> bool:
    """
    检查是否已收藏
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        question_id: 问题ID（可选）
        resource_id: 资源ID（可选）
        
    Returns:
        是否已收藏
    """
    query = db.query(Favorite).filter(Favorite.user_id == user_id)
    if question_id:
        query = query.filter(Favorite.question_id == question_id)
    if resource_id:
        query = query.filter(Favorite.resource_id == resource_id)
    
    return query.first() is not None 