package com.materialsstudio.qa.service;

import com.materialsstudio.qa.model.dto.QuestionPostDTO;
import com.materialsstudio.qa.model.dto.QuestionQueryDTO;
import com.materialsstudio.qa.model.vo.PageVO;
import com.materialsstudio.qa.model.vo.QuestionDetailVO;
import com.materialsstudio.qa.model.vo.QuestionVO;

/**
 * 问题服务接口
 */
public interface QuestionService {
    
    /**
     * 分页查询问题列表
     *
     * @param queryDTO 查询条件
     * @return 分页问题列表
     */
    PageVO<QuestionVO> pageQuestions(QuestionQueryDTO queryDTO);
    
    /**
     * 获取问题详情
     *
     * @param id 问题ID
     * @return 问题详情
     */
    QuestionDetailVO getQuestionDetail(Long id);
    
    /**
     * 发布问题
     *
     * @param questionPostDTO 问题发布信息
     * @return 问题ID
     */
    Long postQuestion(QuestionPostDTO questionPostDTO);
    
    /**
     * 增加问题浏览量
     *
     * @param id 问题ID
     * @return 是否成功
     */
    boolean incrementViewCount(Long id);
    
    /**
     * 更新问题状态
     *
     * @param id     问题ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Long id, Integer status);
} 