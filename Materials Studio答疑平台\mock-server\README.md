# Materials Studio答疑平台模拟后端服务器

这是一个用Node.js和Express框架实现的模拟后端服务器，用于为Materials Studio答疑平台前端提供API支持。这个模拟服务器实现了前端所需的所有API接口，并使用JSON文件来模拟数据库存储。

## 前置条件

- 安装Node.js (建议使用v14.0.0或更高版本)
- npm或yarn包管理器

## 如何安装

1. 进入项目目录
   ```
   cd "Materials Studio答疑平台/mock-server"
   ```

2. 安装依赖
   ```
   npm install
   ```
   或使用yarn
   ```
   yarn install
   ```

## 如何启动服务器

```
npm start
```
或使用yarn
```
yarn start
```

服务器将在 http://localhost:8000 上运行，API基础路径为 http://localhost:8000/api/v1

## 可用测试账号

- 管理员: admin / admin123
- 会员: member / member123
- 普通用户: user / user123

## API接口文档

### 健康检查

- GET /api/v1/health - 检查服务器是否正常运行

### 认证相关

- POST /api/v1/auth/login - 用户登录
- GET /api/v1/auth/me - 获取当前登录用户信息

### 问答相关

- GET /api/v1/qa/questions - 获取问题列表
- GET /api/v1/qa/questions/:id - 获取问题详情
- POST /api/v1/qa/questions - 创建新问题
- GET /api/v1/qa/tags - 获取标签列表
- POST /api/v1/qa/questions/:id/answers - 为问题添加回答

### 用户相关

- GET /api/v1/users/membership - 获取当前用户的会员信息
- GET /api/v1/users/:id - 获取用户详情

### 内容相关

- GET /api/v1/content/resources - 获取资源列表
- GET /api/v1/content/categories - 获取资源分类

## 数据存储

数据存储在 `data` 目录下的JSON文件中：
- users.json - 用户数据
- questions.json - 问题数据
- answers.json - 回答数据
- tags.json - 标签数据
- memberships.json - 会员信息
- categories.json - 资源分类
- resources.json - 资源数据

## 注意事项

1. 这是一个用于前端开发和测试的模拟服务器，不适合生产环境使用。
2. 所有数据都存储在本地JSON文件中，重启服务器后更改会保留。
3. 密码以明文形式存储，这在实际生产环境中是不安全的。
4. 对于实际的生产环境，应当按照后端开发需求使用Java/SpringBoot来实现完整的后端服务。 