package com.materialsstudio.qa.service.impl;

import com.materialsstudio.qa.dao.TagDao;
import com.materialsstudio.qa.model.vo.TagVO;
import com.materialsstudio.qa.service.TagService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签服务实现类
 */
@Service
public class TagServiceImpl implements TagService {
    
    @Resource
    private TagDao tagDao;
    
    @Override
    public List<TagVO> getAllTags() {
        return tagDao.getAllTags();
    }
    
    @Override
    public List<TagVO> getHotTags(Integer limit) {
        return tagDao.getHotTags(limit);
    }
    
    @Override
    public List<TagVO> getTagsByQuestionId(Long questionId) {
        return tagDao.getTagsByQuestionId(questionId);
    }
} 