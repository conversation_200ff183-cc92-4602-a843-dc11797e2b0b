<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.materialsstudio.qa.dao.QuestionDao">

    <!-- 分页查询问题列表 -->
    <select id="pageQuestions" resultType="com.materialsstudio.qa.model.vo.QuestionVO">
        SELECT
            q.*,
            u.username AS author_name,
            u.avatar AS author_avatar,
            (SELECT COUNT(*) FROM answer a WHERE a.question_id = q.id AND a.deleted = 0) AS answer_count
        FROM
            question q
        LEFT JOIN
            user u ON q.author_id = u.id
        <where>
            q.deleted = 0
            <if test="keyword != null and keyword != ''">
                AND q.title LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="tagId != null">
                AND q.id IN (SELECT question_id FROM question_tag WHERE tag_id = #{tagId})
            </if>
            <if test="authorId != null">
                AND q.author_id = #{authorId}
            </if>
            <if test="isPremium != null">
                AND q.is_premium = #{isPremium}
            </if>
            <if test="status != null">
                AND q.status = #{status}
            </if>
        </where>
        <choose>
            <when test="sortField != null and sortField != '' and sortOrder != null and sortOrder != ''">
                ORDER BY ${sortField} ${sortOrder}
            </when>
            <otherwise>
                ORDER BY q.create_time DESC
            </otherwise>
        </choose>
    </select>

</mapper> 