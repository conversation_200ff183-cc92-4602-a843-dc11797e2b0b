package com.materialsstudio.qa.controller;

import com.materialsstudio.qa.common.Result;
import com.materialsstudio.qa.model.vo.TagVO;
import com.materialsstudio.qa.service.TagService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 标签控制器
 */
@RestController
@RequestMapping("/tags")
public class TagController {
    
    @Resource
    private TagService tagService;
    
    /**
     * 获取所有标签
     *
     * @return 标签列表
     */
    @GetMapping
    public Result<List<TagVO>> getAllTags() {
        try {
            List<TagVO> tags = tagService.getAllTags();
            return Result.success(tags);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 获取热门标签
     *
     * @param limit 数量限制
     * @return 热门标签列表
     */
    @GetMapping("/hot")
    public Result<List<TagVO>> getHotTags(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<TagVO> tags = tagService.getHotTags(limit);
            return Result.success(tags);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 根据问题ID获取标签列表
     *
     * @param questionId 问题ID
     * @return 标签列表
     */
    @GetMapping("/question/{questionId}")
    public Result<List<TagVO>> getTagsByQuestionId(@PathVariable Long questionId) {
        try {
            List<TagVO> tags = tagService.getTagsByQuestionId(questionId);
            return Result.success(tags);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
} 