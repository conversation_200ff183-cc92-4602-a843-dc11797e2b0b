from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.config.settings import settings
from app.db.session import SessionLocal
from app.models.user import User
from app.core.security import ALGORITHM
from app.schemas.token import TokenPayload

# OAuth2 配置
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)


def get_db() -> Generator:
    """
    获取数据库会话依赖
    
    Yields:
        数据库会话
    """
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> User:
    """
    获取当前用户依赖
    
    Args:
        db: 数据库会话
        token: JWT令牌
        
    Returns:
        当前用户对象
        
    Raises:
        HTTPException: 认证失败时
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证凭据无效",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = db.query(User).filter(User.id == token_data.sub).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户未激活"
        )
    return user


def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前超级用户依赖
    
    Args:
        current_user: 当前用户
        
    Returns:
        当前超级用户
        
    Raises:
        HTTPException: 权限不足时
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前活跃用户
    
    Args:
        current_user: 当前用户
        
    Returns:
        当前活跃用户
        
    Raises:
        HTTPException: 如果用户不活跃
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用",
        )
    return current_user


def check_premium_access(
    current_user: User = Depends(get_current_active_user),
) -> bool:
    """
    检查用户是否有会员访问权限
    
    Args:
        current_user: 当前活跃用户
        
    Returns:
        是否有会员访问权限
    """
    if current_user.is_superuser:
        return True
    
    # 检查用户会员状态
    if current_user.membership and current_user.membership.is_active:
        if current_user.membership.plan in ["basic", "premium", "enterprise"]:
            return True
    
    return False


def check_premium_content_access(
    current_user: Optional[User] = Depends(get_current_active_user),
    is_premium_content: bool = False,
) -> bool:
    """
    检查用户是否有权限访问会员内容
    
    Args:
        current_user: 当前活跃用户
        is_premium_content: 是否为会员内容
        
    Returns:
        是否有权限访问
        
    Raises:
        HTTPException: 如果没有权限访问
    """
    # 如果不是会员内容，所有用户都可以访问
    if not is_premium_content:
        return True
    
    # 检查用户会员权限
    if not check_premium_access(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要会员权限才能访问此内容",
        )
    
    return True 