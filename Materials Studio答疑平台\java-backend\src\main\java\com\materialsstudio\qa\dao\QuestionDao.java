package com.materialsstudio.qa.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.materialsstudio.qa.entity.Question;
import com.materialsstudio.qa.model.vo.QuestionVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 问题DAO接口
 */
public interface QuestionDao extends BaseMapper<Question> {
    
    /**
     * 分页查询问题列表
     *
     * @param page      分页参数
     * @param keyword   关键字
     * @param tagId     标签ID
     * @param authorId  作者ID
     * @param isPremium 是否为会员专属
     * @param status    状态
     * @param sortField 排序字段
     * @param sortOrder 排序方式
     * @return 分页问题列表
     */
    IPage<QuestionVO> pageQuestions(Page<QuestionVO> page,
                                    @Param("keyword") String keyword,
                                    @Param("tagId") Long tagId,
                                    @Param("authorId") Long authorId,
                                    @Param("isPremium") Boolean isPremium,
                                    @Param("status") Integer status,
                                    @Param("sortField") String sortField,
                                    @Param("sortOrder") String sortOrder);
    
    /**
     * 获取问题详情
     *
     * @param id 问题ID
     * @return 问题详情
     */
    @Select("SELECT q.*, u.username AS author_name, u.avatar AS author_avatar, " +
            "(SELECT COUNT(*) FROM answer a WHERE a.question_id = q.id AND a.deleted = 0) AS answer_count " +
            "FROM question q " +
            "LEFT JOIN user u ON q.author_id = u.id " +
            "WHERE q.id = #{id} AND q.deleted = 0")
    QuestionVO getQuestionDetail(@Param("id") Long id);
    
    /**
     * 增加问题浏览量
     *
     * @param id 问题ID
     * @return 影响行数
     */
    @Update("UPDATE question SET view_count = view_count + 1 WHERE id = #{id}")
    int incrementViewCount(@Param("id") Long id);
    
    /**
     * 更新问题状态
     *
     * @param id     问题ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("UPDATE question SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 查询相关问题
     *
     * @param id    问题ID
     * @param limit 数量限制
     * @return 相关问题列表
     */
    @Select("SELECT q.*, u.username AS author_name, u.avatar AS author_avatar, " +
            "(SELECT COUNT(*) FROM answer a WHERE a.question_id = q.id AND a.deleted = 0) AS answer_count " +
            "FROM question q " +
            "INNER JOIN question_tag qt1 ON q.id = qt1.question_id " +
            "INNER JOIN question_tag qt2 ON qt1.tag_id = qt2.tag_id " +
            "LEFT JOIN user u ON q.author_id = u.id " +
            "WHERE qt2.question_id = #{id} AND q.id != #{id} AND q.deleted = 0 " +
            "GROUP BY q.id " +
            "ORDER BY COUNT(DISTINCT qt1.tag_id) DESC, q.view_count DESC " +
            "LIMIT #{limit}")
    List<QuestionVO> getRelatedQuestions(@Param("id") Long id, @Param("limit") Integer limit);
} 