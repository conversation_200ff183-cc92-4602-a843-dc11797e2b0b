package com.materialsstudio.qa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 问题标签关联实体类
 */
@Data
@TableName("question_tag")
public class QuestionTag implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 关联ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 问题ID
     */
    private Long questionId;
    
    /**
     * 标签ID
     */
    private Long tagId;
} 