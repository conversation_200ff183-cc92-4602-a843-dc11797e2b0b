# Materials Studio 答疑平台部署指南

本文档提供 Materials Studio 答疑平台的完整部署流程，包括前端和后端的部署步骤。

## 一、准备工作

### 1.1 环境要求

- **Java 环境**：JDK 1.8
- **数据库**：MySQL 8.0+
- **缓存**：Redis 6.0+
- **Web 服务器**：Nginx 或 Apache
- **Node.js**：v14.0.0+ (仅用于前端打包，非必需服务器安装)

### 1.2 服务器规划

建议至少准备两台服务器：
- **应用服务器**：部署 Java 后端应用
- **数据库服务器**：部署 MySQL 和 Redis（小规模可与应用服务器合并）

## 二、后端部署

### 2.1 数据库准备

1. 安装 MySQL 数据库
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

2. 创建数据库和用户
```sql
CREATE DATABASE materials_studio_qa DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'msqa_user'@'%' IDENTIFIED BY '安全密码';
GRANT ALL PRIVILEGES ON materials_studio_qa.* TO 'msqa_user'@'%';
FLUSH PRIVILEGES;
```

3. 导入初始化数据
```bash
mysql -u msqa_user -p materials_studio_qa < schema.sql
```

### 2.2 Redis 安装

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# CentOS
sudo yum install epel-release
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

### 2.3 后端应用部署

1. 设置环境变量（用于生产环境配置）
```bash
export MYSQL_USERNAME=msqa_user
export MYSQL_PASSWORD=安全密码
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=redis密码
export JWT_SECRET=生成一个安全的随机密钥
export FILE_UPLOAD_DIR=/app/uploads/
```

2. 编译打包应用
```bash
cd java-backend
mvn clean package -P prod -DskipTests
```

3. 部署应用
```bash
mkdir -p /app/materials-studio-qa
cp target/qa-platform-0.0.1-SNAPSHOT.jar /app/materials-studio-qa/
mkdir -p /app/uploads
```

4. 创建启动脚本
```bash
cat > /app/materials-studio-qa/start.sh << 'EOF'
#!/bin/bash
cd /app/materials-studio-qa
nohup java -Dspring.profiles.active=prod -jar qa-platform-0.0.1-SNAPSHOT.jar > app.log 2>&1 &
echo $! > app.pid
EOF

chmod +x /app/materials-studio-qa/start.sh
```

5. 创建停止脚本
```bash
cat > /app/materials-studio-qa/stop.sh << 'EOF'
#!/bin/bash
cd /app/materials-studio-qa
if [ -f app.pid ]; then
    kill $(cat app.pid)
    rm app.pid
    echo "Application stopped"
else
    echo "No pid file found"
fi
EOF

chmod +x /app/materials-studio-qa/stop.sh
```

6. 启动应用
```bash
/app/materials-studio-qa/start.sh
```

## 三、前端部署

### 3.1 前端打包

1. 更新配置
```bash
cd "Materials Studio答疑平台"
node switch-to-prod.js
```

2. 压缩前端资源（可选）
```bash
# 安装压缩工具
npm install -g html-minifier uglify-js clean-css-cli

# 压缩HTML
find . -name "*.html" -exec html-minifier --collapse-whitespace --remove-comments --remove-optional-tags --remove-redundant-attributes --remove-script-type-attributes --remove-tag-whitespace --use-short-doctype {} -o {} \;

# 压缩CSS
find ./css -name "*.css" -exec cleancss -o {} {} \;

# 压缩JS
find ./js -name "*.js" -not -path "*/node_modules/*" -exec uglifyjs {} -o {} \;
```

3. 将前端文件复制到Web服务器根目录
```bash
mkdir -p /var/www/materials-studio-qa
cp -r * /var/www/materials-studio-qa/
```

### 3.2 配置Web服务器

#### Nginx配置示例

```
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    root /var/www/materials-studio-qa;
    index index.html;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }
    
    # API代理转发
    location /api/v1/ {
        proxy_pass http://localhost:8080/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 其他请求转发到index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 四、安全配置

### 4.1 防火墙设置

```bash
# Ubuntu/Debian
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# CentOS
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 4.2 SSL证书

1. 使用Let's Encrypt获取免费SSL证书：
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 五、系统监控

### 5.1 日志管理

创建日志轮转配置：
```bash
cat > /etc/logrotate.d/materials-studio-qa << 'EOF'
/app/materials-studio-qa/app.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 0640 root root
}
EOF
```

### 5.2 监控脚本

创建监控脚本：
```bash
cat > /app/materials-studio-qa/monitor.sh << 'EOF'
#!/bin/bash
if ! pgrep -f "qa-platform-0.0.1-SNAPSHOT.jar" > /dev/null; then
    echo "Application is down, restarting..."
    /app/materials-studio-qa/start.sh
    echo "Restarted at $(date)" >> /app/materials-studio-qa/restart.log
fi
EOF

chmod +x /app/materials-studio-qa/monitor.sh
```

添加到crontab：
```bash
(crontab -l ; echo "*/5 * * * * /app/materials-studio-qa/monitor.sh") | crontab -
```

## 六、部署后检查清单

### 6.1 系统功能检查

- [ ] 用户注册和登录
- [ ] 会员权限控制
- [ ] 问答功能
- [ ] 文件上传功能
- [ ] 移动端适配
- [ ] 管理功能

### 6.2 性能检查

- [ ] 页面加载速度
- [ ] API响应速度
- [ ] 系统负载
- [ ] 内存使用情况

## 七、故障排除

### 7.1 常见问题

1. **应用无法启动**
   - 检查日志文件：`tail -f /app/materials-studio-qa/app.log`
   - 确认数据库连接配置是否正确
   - 确认环境变量是否设置正确

2. **API调用失败**
   - 检查Nginx配置是否正确转发API请求
   - 确认API服务是否正常运行：`curl -I http://localhost:8080/api/v1/health`

3. **文件上传失败**
   - 检查上传目录权限：`chmod -R 755 /app/uploads`
   - 确认目录所有者：`chown -R www-data:www-data /app/uploads`

## 八、备份策略

### 8.1 数据库备份

创建自动备份脚本：
```bash
cat > /app/backups/backup-db.sh << 'EOF'
#!/bin/bash
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/app/backups/db"
mkdir -p $BACKUP_DIR
mysqldump -u msqa_user -p'安全密码' materials_studio_qa | gzip > $BACKUP_DIR/materials_studio_qa_$TIMESTAMP.sql.gz
find $BACKUP_DIR -type f -mtime +7 -delete
EOF

chmod +x /app/backups/backup-db.sh
```

添加到crontab：
```bash
(crontab -l ; echo "0 2 * * * /app/backups/backup-db.sh") | crontab -
```

### 8.2 文件备份

创建文件备份脚本：
```bash
cat > /app/backups/backup-files.sh << 'EOF'
#!/bin/bash
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/app/backups/files"
mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/uploads_$TIMESTAMP.tar.gz /app/uploads
find $BACKUP_DIR -type f -mtime +7 -delete
EOF

chmod +x /app/backups/backup-files.sh
```

添加到crontab：
```bash
(crontab -l ; echo "0 3 * * * /app/backups/backup-files.sh") | crontab -
```

## 九、更新策略

### 9.1 前端更新

1. 准备新版本前端代码
2. 切换到生产环境配置：`node switch-to-prod.js`
3. 构建和优化前端资源（如需要）
4. 备份当前版本：`cp -r /var/www/materials-studio-qa /var/www/materials-studio-qa.bak$(date +%Y%m%d)`
5. 部署新版本：`cp -r * /var/www/materials-studio-qa/`

### 9.2 后端更新

1. 准备新版本JAR包
2. 停止当前服务：`/app/materials-studio-qa/stop.sh`
3. 备份当前版本：`cp /app/materials-studio-qa/qa-platform-0.0.1-SNAPSHOT.jar /app/materials-studio-qa/qa-platform-0.0.1-SNAPSHOT.jar.bak$(date +%Y%m%d)`
4. 部署新版本：`cp qa-platform-0.0.1-SNAPSHOT.jar /app/materials-studio-qa/`
5. 启动服务：`/app/materials-studio-qa/start.sh`

## 十、联系与支持

如遇部署问题，请联系：
- 技术支持邮箱：<EMAIL>
- 技术支持电话：XXX-XXXX-XXXX 