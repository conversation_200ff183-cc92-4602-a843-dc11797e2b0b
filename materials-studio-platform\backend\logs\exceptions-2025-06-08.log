2025-06-08 11:16:04 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Sun Jun 08 2025 11:16:04 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 39652,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 112427008,
      "heapTotal": 74903552,
      "heapUsed": 45803832,
      "external": 2292368,
      "arrayBuffers": 18815
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1644160.375
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 153,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-08 11:17:23 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Sun Jun 08 2025 11:17:23 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 26556,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 116117504,
      "heapTotal": 74903552,
      "heapUsed": 45635672,
      "external": 2292368,
      "arrayBuffers": 18815
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1644238.906
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 153,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-08 11:17:43 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Sun Jun 08 2025 11:17:43 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 29284,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 116211712,
      "heapTotal": 74903552,
      "heapUsed": 45592264,
      "external": 2292368,
      "arrayBuffers": 18815
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1644259.734
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 153,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
2025-06-08 11:18:09 [ERROR]: uncaughtException: app.use() requires a middleware function
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
TypeError: app.use() requires a middleware function
    at Function.use (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\node_modules\express\lib\application.js:217:11)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\webpage\materials-studio-platform\backend\src\app.js:153:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
{
  "error": {},
  "exception": true,
  "date": "Sun Jun 08 2025 11:18:09 GMT+0800 (中国标准时间)",
  "process": {
    "pid": 40804,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend",
    "execPath": "D:\\Soft\\Node.js\\node.exe",
    "version": "v22.15.1",
    "argv": [
      "D:\\Soft\\Node.js\\node.exe",
      "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js"
    ],
    "memoryUsage": {
      "rss": 116424704,
      "heapTotal": 75427840,
      "heapUsed": 45526528,
      "external": 2292368,
      "arrayBuffers": 18815
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1644285.015
  },
  "trace": [
    {
      "column": 11,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.use",
      "line": 217,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "C:\\Users\\<USER>\\Desktop\\webpage\\materials-studio-platform\\backend\\src\\app.js",
      "function": null,
      "line": 153,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1730,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1895,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1465,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1282,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 235,
      "method": null,
      "native": false
    },
    {
      "column": 5,
      "file": "node:internal/modules/run_main",
      "function": "Function.executeUserEntryPoint [as runMain]",
      "line": 170,
      "method": "executeUserEntryPoint [as runMain]",
      "native": false
    },
    {
      "column": 49,
      "file": "node:internal/main/run_main_module",
      "function": null,
      "line": 36,
      "method": null,
      "native": false
    }
  ]
}
