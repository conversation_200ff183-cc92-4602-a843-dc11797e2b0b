const mysql = require('mysql2/promise');
const config = require('../config');
const logger = require('../utils/logger');

class Database {
  constructor() {
    this.pool = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      this.pool = mysql.createPool({
        host: config.database.host,
        port: config.database.port,
        user: config.database.user,
        password: config.database.password,
        database: config.database.name,
        connectionLimit: config.database.connectionLimit,
        acquireTimeout: config.database.acquireTimeout,
        timeout: config.database.timeout,
        reconnect: config.database.reconnect,
        charset: config.database.charset,
        timezone: '+08:00',
        dateStrings: false,
        supportBigNumbers: true,
        bigNumberStrings: false,
        multipleStatements: false,
        namedPlaceholders: true
      });

      // 测试连接
      await this.testConnection();
      this.isConnected = true;
      logger.info('数据库连接池创建成功');
      
      return this.pool;
    } catch (error) {
      logger.error('数据库连接失败:', error);
      throw error;
    }
  }

  async testConnection() {
    try {
      if (!this.pool) {
        await this.connect();
      }
      
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      
      logger.info('数据库连接测试成功');
      return true;
    } catch (error) {
      logger.error('数据库连接测试失败:', error);
      throw error;
    }
  }

  async query(sql, params = []) {
    try {
      if (!this.pool) {
        await this.connect();
      }

      const [rows, fields] = await this.pool.execute(sql, params);
      return { rows, fields };
    } catch (error) {
      logger.error('数据库查询错误:', {
        sql,
        params,
        error: error.message
      });
      throw error;
    }
  }

  async queryOne(sql, params = []) {
    try {
      const { rows } = await this.query(sql, params);
      return rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  async insert(table, data) {
    try {
      const keys = Object.keys(data);
      const values = Object.values(data);
      const placeholders = keys.map(() => '?').join(', ');
      
      const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`;
      const { rows } = await this.query(sql, values);
      
      return {
        insertId: rows.insertId,
        affectedRows: rows.affectedRows
      };
    } catch (error) {
      throw error;
    }
  }

  async update(table, data, where, whereParams = []) {
    try {
      const keys = Object.keys(data);
      const values = Object.values(data);
      const setClause = keys.map(key => `${key} = ?`).join(', ');
      
      const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;
      const { rows } = await this.query(sql, [...values, ...whereParams]);
      
      return {
        affectedRows: rows.affectedRows,
        changedRows: rows.changedRows
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(table, where, whereParams = []) {
    try {
      const sql = `DELETE FROM ${table} WHERE ${where}`;
      const { rows } = await this.query(sql, whereParams);
      
      return {
        affectedRows: rows.affectedRows
      };
    } catch (error) {
      throw error;
    }
  }

  async transaction(callback) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      const result = await callback(connection);
      
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  async paginate(sql, params = [], page = 1, limit = 10) {
    try {
      // 计算总数
      const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_table`;
      const { rows: countRows } = await this.query(countSql, params);
      const total = countRows[0].total;

      // 计算分页
      const offset = (page - 1) * limit;
      const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
      const { rows } = await this.query(paginatedSql, [...params, limit, offset]);

      return {
        data: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(total),
          pages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw error;
    }
  }

  async close() {
    try {
      if (this.pool) {
        await this.pool.end();
        this.isConnected = false;
        logger.info('数据库连接池已关闭');
      }
    } catch (error) {
      logger.error('关闭数据库连接池失败:', error);
      throw error;
    }
  }

  getPool() {
    return this.pool;
  }

  isHealthy() {
    return this.isConnected && this.pool;
  }
}

// 创建单例实例
const database = new Database();

module.exports = database;
