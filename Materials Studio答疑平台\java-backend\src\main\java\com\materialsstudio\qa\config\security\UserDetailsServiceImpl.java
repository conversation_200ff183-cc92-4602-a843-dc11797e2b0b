package com.materialsstudio.qa.config.security;

import com.materialsstudio.qa.dao.UserDao;
import com.materialsstudio.qa.entity.User;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 用户详情服务实现类
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    
    @Resource
    private UserDao userDao;
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 根据用户名查询用户
        User user = userDao.selectByUsername(username);
        
        // 如果用户不存在，尝试通过邮箱查询
        if (user == null) {
            user = userDao.selectByEmail(username);
        }
        
        // 如果用户仍不存在，抛出异常
        if (user == null) {
            throw new UsernameNotFoundException("用户名或邮箱不存在：" + username);
        }
        
        // 如果用户被禁用，抛出异常
        if (user.getStatus() == 0) {
            throw new UsernameNotFoundException("用户已被禁用：" + username);
        }
        
        // 创建用户详情对象
        return new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                user.getPassword(),
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + user.getRole().toUpperCase()))
        );
    }
} 