const { createClient } = require('redis');
const config = require('../config');
const logger = require('./logger');

class RedisClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      this.client = createClient({
        socket: {
          host: config.redis.host,
          port: config.redis.port,
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('Redis重连次数超过限制，停止重连');
              return new Error('Redis重连失败');
            }
            return Math.min(retries * 50, 500);
          }
        },
        password: config.redis.password || undefined,
        database: config.redis.db
      });

      this.client.on('error', (error) => {
        logger.error('Redis连接错误:', error);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        logger.info('Redis连接成功');
        this.isConnected = true;
      });

      this.client.on('reconnecting', () => {
        logger.info('Redis重新连接中...');
        this.isConnected = false;
      });

      this.client.on('ready', () => {
        logger.info('Redis客户端就绪');
        this.isConnected = true;
      });

      await this.client.connect();
      return this.client;
    } catch (error) {
      logger.error('Redis连接失败:', error);
      throw error;
    }
  }

  async ping() {
    try {
      if (!this.client) {
        await this.connect();
      }
      
      const result = await this.client.ping();
      logger.info('Redis ping测试成功:', result);
      return result;
    } catch (error) {
      logger.error('Redis ping测试失败:', error);
      throw error;
    }
  }

  async set(key, value, ttl = null) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const serializedValue = JSON.stringify(value);

      if (ttl) {
        await this.client.setEx(prefixedKey, ttl, serializedValue);
      } else {
        await this.client.set(prefixedKey, serializedValue);
      }

      return true;
    } catch (error) {
      logger.error('Redis设置值失败:', { key, error: error.message });
      return false;
    }
  }

  async get(key) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const value = await this.client.get(prefixedKey);

      if (value === null) {
        return null;
      }

      return JSON.parse(value);
    } catch (error) {
      logger.error('Redis获取值失败:', { key, error: error.message });
      return null;
    }
  }

  async del(key) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const result = await this.client.del(prefixedKey);
      return result > 0;
    } catch (error) {
      logger.error('Redis删除值失败:', { key, error: error.message });
      return false;
    }
  }

  async exists(key) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const result = await this.client.exists(prefixedKey);
      return result === 1;
    } catch (error) {
      logger.error('Redis检查键存在失败:', { key, error: error.message });
      return false;
    }
  }

  async expire(key, ttl) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const result = await this.client.expire(prefixedKey, ttl);
      return result === 1;
    } catch (error) {
      logger.error('Redis设置过期时间失败:', { key, ttl, error: error.message });
      return false;
    }
  }

  async incr(key, ttl = null) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const result = await this.client.incr(prefixedKey);

      if (ttl && result === 1) {
        await this.client.expire(prefixedKey, ttl);
      }

      return result;
    } catch (error) {
      logger.error('Redis递增失败:', { key, error: error.message });
      return 0;
    }
  }

  async hSet(key, field, value) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const serializedValue = JSON.stringify(value);
      const result = await this.client.hSet(prefixedKey, field, serializedValue);
      return result;
    } catch (error) {
      logger.error('Redis哈希设置失败:', { key, field, error: error.message });
      return false;
    }
  }

  async hGet(key, field) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const value = await this.client.hGet(prefixedKey, field);

      if (value === null) {
        return null;
      }

      return JSON.parse(value);
    } catch (error) {
      logger.error('Redis哈希获取失败:', { key, field, error: error.message });
      return null;
    }
  }

  async hGetAll(key) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const hash = await this.client.hGetAll(prefixedKey);

      const result = {};
      for (const [field, value] of Object.entries(hash)) {
        try {
          result[field] = JSON.parse(value);
        } catch {
          result[field] = value;
        }
      }

      return result;
    } catch (error) {
      logger.error('Redis哈希获取全部失败:', { key, error: error.message });
      return {};
    }
  }

  async hDel(key, field) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedKey = `${config.cache.prefix}:${key}`;
      const result = await this.client.hDel(prefixedKey, field);
      return result > 0;
    } catch (error) {
      logger.error('Redis哈希删除失败:', { key, field, error: error.message });
      return false;
    }
  }

  async keys(pattern) {
    try {
      if (!this.client) {
        await this.connect();
      }

      const prefixedPattern = `${config.cache.prefix}:${pattern}`;
      const keys = await this.client.keys(prefixedPattern);
      
      // 移除前缀
      return keys.map(key => key.replace(`${config.cache.prefix}:`, ''));
    } catch (error) {
      logger.error('Redis获取键列表失败:', { pattern, error: error.message });
      return [];
    }
  }

  async flushPattern(pattern) {
    try {
      const keys = await this.keys(pattern);
      if (keys.length === 0) {
        return 0;
      }

      const prefixedKeys = keys.map(key => `${config.cache.prefix}:${key}`);
      const result = await this.client.del(prefixedKeys);
      return result;
    } catch (error) {
      logger.error('Redis批量删除失败:', { pattern, error: error.message });
      return 0;
    }
  }

  async quit() {
    try {
      if (this.client) {
        await this.client.quit();
        this.isConnected = false;
        logger.info('Redis连接已关闭');
      }
    } catch (error) {
      logger.error('关闭Redis连接失败:', error);
      throw error;
    }
  }

  getClient() {
    return this.client;
  }

  isHealthy() {
    return this.isConnected && this.client;
  }
}

// 创建单例实例
const redis = new RedisClient();

module.exports = redis;
