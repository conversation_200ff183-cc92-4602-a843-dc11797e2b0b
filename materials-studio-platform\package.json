{"name": "materials-studio-platform", "version": "1.0.0", "description": "Materials Studio答疑平台 - 专业的分子模拟软件问答社区", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "start": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset"}, "keywords": ["materials-studio", "molecular-simulation", "qa-platform", "vue", "nodejs", "express", "mysql"], "author": "Materials Studio Platform Team", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/materials-studio-platform.git"}, "bugs": {"url": "https://github.com/your-username/materials-studio-platform/issues"}, "homepage": "https://github.com/your-username/materials-studio-platform#readme"}