package com.materialsstudio.qa.service;

import com.materialsstudio.qa.model.vo.TagVO;

import java.util.List;

/**
 * 标签服务接口
 */
public interface TagService {
    
    /**
     * 获取所有标签
     *
     * @return 标签列表
     */
    List<TagVO> getAllTags();
    
    /**
     * 获取热门标签
     *
     * @param limit 数量限制
     * @return 热门标签列表
     */
    List<TagVO> getHotTags(Integer limit);
    
    /**
     * 根据问题ID获取标签列表
     *
     * @param questionId 问题ID
     * @return 标签列表
     */
    List<TagVO> getTagsByQuestionId(Long questionId);
} 