# Materials Studio答疑平台数据库设计

## 数据库选择
- 主数据库：MySQL 8.0
- 缓存数据库：Redis 6.0
- 搜索引擎：Elasticsearch 7.x

## 数据库架构

```
┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │
│   应用服务器    │◄────►│   主数据库      │
│                 │      │  MySQL Master   │
└────────┬────────┘      └────────┬────────┘
         │                        │
         │                        │ 主从复制
         │                        ▼
         │               ┌─────────────────┐
         │               │                 │
         │               │   从数据库      │
         │               │  MySQL Slaves   │
         │               └─────────────────┘
         │
         │               ┌─────────────────┐
         │               │                 │
         └──────────────►│   缓存服务      │
                         │    Redis        │
                         └─────────────────┘
```

## 数据表清单

### 1. 用户管理相关表

#### `users` 表 - 用户基本信息
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 用户ID |
| username | VARCHAR(50) | NOT NULL, UNIQUE | 用户名 |
| email | VARCHAR(100) | UNIQUE | 电子邮箱 |
| phone | VARCHAR(20) | UNIQUE | 手机号码 |
| password | VARCHAR(255) | NOT NULL | 加密密码 |
| avatar | VARCHAR(255) | | 头像URL |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| last_login | TIMESTAMP | | 最后登录时间 |
| status | TINYINT | DEFAULT 1 | 状态(1-正常,0-禁用) |
| login_attempts | TINYINT | DEFAULT 0 | 登录尝试次数 |
| locked_until | TIMESTAMP | NULL | 账户锁定截止时间 |

#### `user_profiles` 表 - 用户详细资料
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 资料ID |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 用户ID |
| real_name | VARCHAR(50) | | 真实姓名 |
| gender | TINYINT | | 性别(1-男,2-女,0-未知) |
| birthday | DATE | | 出生日期 |
| education | VARCHAR(50) | | 学历 |
| institution | VARCHAR(100) | | 所属机构 |
| research_field | VARCHAR(100) | | 研究领域 |
| bio | TEXT | | 个人简介 |
| website | VARCHAR(255) | | 个人网站 |
| points | INT | DEFAULT 0 | 积分 |
| contributions | INT | DEFAULT 0 | 贡献度 |

#### `memberships` 表 - 会员信息
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 会员记录ID |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 用户ID |
| level | TINYINT | NOT NULL, DEFAULT 0 | 会员等级(0-非会员,1-初级,2-高级) |
| start_date | DATE | | 开始日期 |
| end_date | DATE | | 结束日期 |
| payment_id | INT | | 支付记录ID |
| auto_renew | TINYINT | DEFAULT 0 | 是否自动续费(0-否,1-是) |
| status | TINYINT | DEFAULT 1 | 状态(1-有效,0-无效) |
| source | VARCHAR(20) | | 来源(register-注册赠送,purchase-购买,admin-管理员设置) |

#### `verification_codes` 表 - 验证码记录
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 记录ID |
| user_id | INT | | 用户ID(未注册时为NULL) |
| type | TINYINT | NOT NULL | 类型(1-注册,2-登录,3-找回密码) |
| target | VARCHAR(100) | NOT NULL | 目标(邮箱或手机) |
| code | VARCHAR(20) | NOT NULL | 验证码内容 |
| expires_at | TIMESTAMP | NOT NULL | 过期时间 |
| used | TINYINT | DEFAULT 0 | 是否已使用(0-未使用,1-已使用) |
| send_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 发送时间 |
| send_ip | VARCHAR(50) | | 发送IP |

#### `user_logs` 表 - 用户行为日志
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 日志ID |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 用户ID |
| action | VARCHAR(50) | NOT NULL | 操作类型 |
| action_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 操作时间 |
| ip_address | VARCHAR(50) | | IP地址 |
| user_agent | VARCHAR(255) | | 浏览器信息 |
| details | TEXT | | 详细信息(JSON格式) |

### 2. 内容管理相关表

#### `questions` 表 - 问题记录
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 问题ID |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 提问用户ID |
| title | VARCHAR(255) | NOT NULL | 问题标题 |
| content | TEXT | NOT NULL | 问题内容 |
| content_html | TEXT | | HTML格式内容 |
| category_id | INT | FOREIGN KEY REFERENCES categories(id) | 分类ID |
| view_count | INT | DEFAULT 0 | 查看次数 |
| answer_count | INT | DEFAULT 0 | 回答数量 |
| favorite_count | INT | DEFAULT 0 | 收藏数量 |
| status | TINYINT | DEFAULT 1 | 状态(0-待审核,1-已发布,2-已关闭,3-已解决) |
| is_top | TINYINT | DEFAULT 0 | 是否置顶(0-否,1-是) |
| is_essence | TINYINT | DEFAULT 0 | 是否精华(0-否,1-是) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| last_active | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 最后活跃时间 |

#### `question_attachments` 表 - 问题附件
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 附件ID |
| question_id | INT | FOREIGN KEY REFERENCES questions(id) | 问题ID |
| file_name | VARCHAR(255) | NOT NULL | 文件名 |
| file_path | VARCHAR(255) | NOT NULL | 文件路径 |
| file_size | INT | NOT NULL | 文件大小(KB) |
| file_type | VARCHAR(50) | | 文件类型 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 上传时间 |
| download_count | INT | DEFAULT 0 | 下载次数 |

#### `answers` 表 - 答案记录
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 答案ID |
| question_id | INT | FOREIGN KEY REFERENCES questions(id) | 问题ID |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 回答用户ID |
| content | TEXT | NOT NULL | 回答内容 |
| content_html | TEXT | | HTML格式内容 |
| basic_content | TEXT | | 基础内容(非会员可见) |
| premium_content | TEXT | | 高级内容(仅会员可见) |
| premium_content_html | TEXT | | 高级内容HTML格式 |
| visibility_level | TINYINT | DEFAULT 0 | 可见级别(0-全部用户,1-初级会员,2-高级会员) |
| is_accepted | TINYINT | DEFAULT 0 | 是否为采纳答案 |
| upvote_count | INT | DEFAULT 0 | 点赞数量 |
| downvote_count | INT | DEFAULT 0 | 踩数量 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

#### `answer_videos` 表 - 答案视频
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 视频ID |
| answer_id | INT | FOREIGN KEY REFERENCES answers(id) | 答案ID |
| title | VARCHAR(255) | NOT NULL | 视频标题 |
| description | TEXT | | 视频描述 |
| video_url | VARCHAR(255) | NOT NULL | 视频URL |
| cover_url | VARCHAR(255) | | 封面图URL |
| duration | INT | | 视频时长(秒) |
| visibility_level | TINYINT | DEFAULT 0 | 可见级别(0-全部用户,1-初级会员,2-高级会员) |
| view_count | INT | DEFAULT 0 | 观看次数 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### `comments` 表 - 评论记录
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 评论ID |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 用户ID |
| content_type | TINYINT | NOT NULL | 内容类型(1-问题,2-答案) |
| content_id | INT | NOT NULL | 内容ID |
| content | TEXT | NOT NULL | 评论内容 |
| parent_id | INT | | 父评论ID(用于回复) |
| like_count | INT | DEFAULT 0 | 点赞数 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| status | TINYINT | DEFAULT 1 | 状态(0-已删除,1-正常) |

#### `categories` 表 - 分类信息
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 分类ID |
| name | VARCHAR(50) | NOT NULL | 分类名称 |
| parent_id | INT | | 父分类ID |
| description | VARCHAR(255) | | 分类描述 |
| icon | VARCHAR(255) | | 分类图标 |
| sort_order | INT | DEFAULT 0 | 排序值 |
| question_count | INT | DEFAULT 0 | 问题数量 |
| status | TINYINT | DEFAULT 1 | 状态(0-禁用,1-启用) |

#### `tags` 表 - 标签信息
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 标签ID |
| name | VARCHAR(50) | NOT NULL, UNIQUE | 标签名称 |
| description | VARCHAR(255) | | 标签描述 |
| question_count | INT | DEFAULT 0 | 问题数量 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| status | TINYINT | DEFAULT 1 | 状态(0-禁用,1-启用) |

#### `question_tags` 表 - 问题标签关联表
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 关联ID |
| question_id | INT | FOREIGN KEY REFERENCES questions(id) | 问题ID |
| tag_id | INT | FOREIGN KEY REFERENCES tags(id) | 标签ID |

#### `resources` 表 - 资源记录
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 资源ID |
| title | VARCHAR(255) | NOT NULL | 资源标题 |
| description | TEXT | | 资源描述 |
| content | TEXT | | 资源内容 |
| type | TINYINT | NOT NULL | 类型(1-文档,2-视频,3-模板,4-软件) |
| url | VARCHAR(255) | | 资源URL |
| cover_image | VARCHAR(255) | | 封面图片 |
| file_size | INT | | 文件大小(KB) |
| category_id | INT | FOREIGN KEY REFERENCES categories(id) | 分类ID |
| visibility_level | TINYINT | DEFAULT 0 | 可见级别(0-全部用户,1-初级会员,2-高级会员) |
| download_count | INT | DEFAULT 0 | 下载次数 |
| view_count | INT | DEFAULT 0 | 查看次数 |
| like_count | INT | DEFAULT 0 | 点赞数量 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| status | TINYINT | DEFAULT 1 | 状态(0-禁用,1-启用) |

#### `user_favorites` 表 - 用户收藏
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 收藏ID |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 用户ID |
| content_type | TINYINT | NOT NULL | 内容类型(1-问题,2-资源) |
| content_id | INT | NOT NULL | 内容ID |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 收藏时间 |

### 3. 支付与交易相关表

#### `orders` 表 - 订单信息
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 订单ID |
| order_no | VARCHAR(64) | NOT NULL, UNIQUE | 订单号，全局唯一 |
| user_id | INT | FOREIGN KEY REFERENCES users(id) | 下单用户ID |
| product_type | TINYINT | NOT NULL | 商品类型 (1-会员) |
| product_id | INT | NOT NULL | 商品ID (例如会员套餐ID) |
| product_description | VARCHAR(255) | | 商品描述 |
| amount | DECIMAL(10, 2) | NOT NULL | 订单金额 |
| status | TINYINT | NOT NULL, DEFAULT 0 | 订单状态 (0-待支付, 1-已支付, 2-已关闭, 3-已退款) |
| payment_method | VARCHAR(20) | | 支付方式 (alipay, wechat_pay) |
| paid_at | TIMESTAMP | NULL | 支付时间 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

#### `transactions` 表 - 交易流水
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 交易ID |
| transaction_no | VARCHAR(128) | NOT NULL, UNIQUE | 支付网关交易号 |
| order_no | VARCHAR(64) | NOT NULL | 关联的商户订单号 |
| user_id | INT | NOT NULL | 用户ID |
| amount | DECIMAL(10, 2) | NOT NULL | 交易金额 |
| status | TINYINT | NOT NULL | 交易状态 (0-处理中, 1-成功, 2-失败) |
| payment_channel | VARCHAR(20) | | 支付渠道 |
| response_data | TEXT | | 支付网关回调数据 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 4. 系统管理相关表

#### `admin_users` 表 - 管理员信息
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 管理员ID |
| username | VARCHAR(50) | NOT NULL, UNIQUE | 用户名 |
| password | VARCHAR(255) | NOT NULL | 加密密码 |
| real_name | VARCHAR(50) | | 真实姓名 |
| role_id | INT | FOREIGN KEY REFERENCES admin_roles(id) | 角色ID |
| email | VARCHAR(100) | | 电子邮箱 |
| last_login | TIMESTAMP | | 最后登录时间 |
| status | TINYINT | DEFAULT 1 | 状态(1-正常,0-禁用) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| login_ip | VARCHAR(50) | | 最后登录IP |
| google_auth_secret | VARCHAR(50) | | 谷歌验证器密钥 |

#### `admin_roles` 表 - 管理员角色
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 角色ID |
| name | VARCHAR(50) | NOT NULL | 角色名称 |
| permissions | TEXT | | 权限列表(JSON格式) |
| description | VARCHAR(255) | | 角色描述 |
| status | TINYINT | DEFAULT 1 | 状态(1-正常,0-禁用) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

#### `admin_operation_logs` 表 - 管理员操作日志
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 日志ID |
| admin_id | INT | FOREIGN KEY REFERENCES admin_users(id) | 管理员ID |
| action | VARCHAR(100) | NOT NULL | 操作描述 |
| ip | VARCHAR(50) | | IP地址 |
| method | VARCHAR(10) | | 请求方法(GET,POST等) |
| url | VARCHAR(255) | | 请求URL |
| data | TEXT | | 操作数据(JSON格式) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 操作时间 |

## 数据库关系图

```
users 1 ------ 1 user_profiles
   |           |
   | 1         | 1
   |           |
   ∨ n         ∨ 1
memberships    user_logs
   |
   | n
   |
   ∨
orders ----- n membership_packages
   
users 1 ------ n questions ----- n question_tags ----- n tags
                 |                 |
                 | 1               | n
                 |                 |
                 ∨ n               ∨ 1
               answers           question_attachments
                 |
                 | 1
                 |
                 ∨ n
               comments
                 |
                 | 1
                 |
                 ∨ n
             answer_videos

categories 1 --- n questions
         1
         |
         |
         ∨ n
       resources

admin_roles 1 --- n admin_users --- 1 admin_operation_logs

users 1 --- n user_favorites
```

## 索引设计

1. `users` 表：
   - 主键索引：`id`
   - 唯一索引：`username`, `email`, `phone`
   - 普通索引：`status`, `created_at`, `last_login`

2. `user_profiles` 表：
   - 主键索引：`id`
   - 唯一索引：`user_id`
   - 普通索引：`institution`, `research_field`

3. `memberships` 表：
   - 主键索引：`id`
   - 普通索引：`user_id`, `level`, `end_date`, `status`

4. `questions` 表：
   - 主键索引：`id`
   - 普通索引：`user_id`, `category_id`, `created_at`, `status`, `is_essence`
   - 组合索引：`(status, category_id, created_at)` - 用于首页问题查询
   - 全文索引：`(title, content)` - 用于全文搜索

5. `answers` 表：
   - 主键索引：`id`
   - 普通索引：`question_id`, `user_id`, `is_accepted`
   - 组合索引：`(question_id, created_at)` - 用于问题页答案排序

6. `comments` 表：
   - 主键索引：`id`
   - 联合索引：`(content_type, content_id, created_at)` - 用于评论排序
   - 普通索引：`user_id`, `parent_id`

7. `categories` 表：
   - 主键索引：`id`
   - 普通索引：`parent_id`, `sort_order`

8. `tags` 表：
   - 主键索引：`id`
   - 唯一索引：`name`
   - 普通索引：`question_count` - 用于热门标签

9. `resources` 表：
   - 主键索引：`id`
   - 普通索引：`visibility_level`, `category_id`, `type`, `created_at`
   - 全文索引：`(title, description)` - 用于资源搜索

10. `orders` 表：
    - 主键索引：`id`
    - 唯一索引：`order_no`
    - 普通索引：`user_id`, `status`, `created_at`

## 数据校验规则

### 用户数据校验
1. **用户名校验**：
   - 长度：5-20个字符
   - 格式：字母、数字、下划线，字母开头
   - 唯一性校验
   - 敏感词过滤

2. **密码校验**：
   - 长度：8-20个字符
   - 复杂度：必须包含字母、数字和特殊字符
   - 不允许使用常见弱密码

3. **邮箱校验**：
   - 标准邮箱格式验证
   - 唯一性校验
   - 可选加入MX记录检查

4. **手机号校验**：
   - 格式校验：符合国内手机号规则
   - 唯一性校验

### 内容数据校验
1. **问题标题**：
   - 长度：10-100个字符
   - 敏感词过滤
   - 重复问题检测

2. **问题内容**：
   - 长度：至少50个字符
   - 敏感词过滤
   - HTML标签安全过滤

3. **答案内容**：
   - 长度：至少30个字符
   - 敏感词过滤
   - HTML标签安全过滤

4. **评论内容**：
   - 长度：1-500个字符
   - 敏感词过滤

5. **资源上传**：
   - 文件类型校验
   - 文件大小限制
   - 病毒扫描

## 存储优化策略

### 大字段存储优化
1. **文本数据分离**：
   - 将大型TEXT字段（如问题内容、答案内容）与基本信息分表存储
   - 仅在需要完整内容时才读取详情表

2. **图片与附件存储**：
   - 所有图片、视频、附件等媒体资源存储在对象存储（阿里云OSS）
   - 数据库只存储URL引用

3. **HTML与Markdown分离存储**：
   - 存储原始Markdown内容和渲染后的HTML内容
   - 读取时优先使用HTML内容，避免重复渲染

### 冷热数据分离
1. **历史问题归档**：
   - 超过1年无活动的问题移至历史表
   - 需要时再查询历史表

2. **日志数据分区**：
   - 按月分区存储日志数据
   - 定期归档和清理旧日志

## 分库分表设计

### 水平分表策略
对于未来可能产生大量数据的表，预先设计分表策略：

1. **问题表分表**：
   - 按ID范围分表：`questions_0`, `questions_1`, ... `questions_9`
   - 路由规则：question_id % 10

2. **答案表分表**：
   - 按问题ID分表：`answers_0`, `answers_1`, ... `answers_9`
   - 路由规则：question_id % 10

3. **评论表分表**：
   - 按内容类型和ID分表：`comments_q_0`, `comments_q_1` (问题评论)
   - 路由规则：content_id % 10

### 垂直分表策略
1. **用户信息拆分**：
   - 基本信息表：`users` (高频访问)
   - 详细资料表：`user_profiles` (低频访问)
   - 安全信息表：`user_securities` (敏感数据，加密存储)

2. **内容拆分**：
   - 问题基本信息：`questions`
   - 问题详细内容：`question_contents`
   - 答案基本信息：`answers`
   - 答案详细内容：`answer_contents`

## 数据库主从复制设计

### 主从架构
1. **一主多从架构**：
   - 1个主库：处理所有写操作
   - 2个从库：处理读操作
   - 1个备份从库：用于数据备份，不提供服务

2. **复制方式**：
   - 半同步复制：确保至少一个从库接收到binlog
   - 基于GTID的复制：便于故障恢复和主从切换

3. **读写分离策略**：
   - 写操作：路由至主库
   - 读操作：根据负载均衡算法路由至从库
   - 特定场景（如用户读取自己刚写入的数据）：强制路由至主库

### 灾备方案
1. **故障转移**：
   - 主库故障自动切换到从库
   - 使用MHA或Orchestrator管理主从切换
   - 切换后更新应用程序连接信息

2. **数据备份**：
   - 每日全量备份
   - 按小时增量备份
   - 实时binlog备份

## 性能优化建议

### 查询优化
1. **慢查询优化**：
   - 开启慢查询日志
   - 定期分析慢查询，优化索引和SQL
   - 复杂查询改为存储过程

2. **索引优化**：
   - 避免过度索引（每个表索引数控制在5个以内）
   - 定期分析索引使用情况，删除不常用索引
   - 优化组合索引顺序

3. **分页查询优化**：
   - 使用延迟关联优化大偏移分页
   - 基于上次结果的游标分页

### 缓存策略
1. **多级缓存**：
   - 应用级缓存：热点数据本地缓存
   - Redis缓存：分布式缓存，共享数据
   - CDN缓存：静态资源和API结果缓存

2. **缓存对象**：
   - 首页热门问题
   - 热门分类和标签
   - 用户基本信息
   - 问题详情和回答列表

3. **缓存更新策略**：
   - 写操作同步更新缓存
   - 设置合理的过期时间
   - 使用缓存锁避免缓存雪崩

### 连接池配置
- 最小连接数：10
- 最大连接数：100
- 连接空闲超时：60秒
- 最大生存时间：30分钟
- 验证周期：15秒

## 数据安全策略

1. **用户密码安全**：
   - 使用bcrypt算法加密存储，工作因子为12
   - 密码重置采用一次性令牌
   - 定期强制密码更新（管理员账户）

2. **敏感数据加密**：
   - 个人身份信息采用AES-256加密
   - 加密密钥与数据分离存储
   - 敏感操作日志完整记录

3. **数据备份策略**：
   - 每日自动全量备份
   - 每小时增量备份
   - 备份数据异地存储
   - 定期备份恢复测试

4. **访问控制**：
   - 数据库账户最小权限原则
   - IP白名单限制
   - 禁用远程root登录
   - 定期轮换数据库密码

5. **SQL注入防护**：
   - 使用参数化查询
   - ORM框架过滤输入
   - Web应用防火墙
   - 定期安全扫描