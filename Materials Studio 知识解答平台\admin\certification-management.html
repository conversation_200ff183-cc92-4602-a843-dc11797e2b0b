<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业认证管理 - Materials Studio 知识解答平台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --primary-color: #0056b3;
            --secondary-color: #004494;
            --border-radius: 10px;
            --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }
        
        .sidebar {
            background-color: #fff;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            padding: 20px;
        }
        
        .sidebar .nav-link {
            color: #333;
            border-radius: 5px;
            margin-bottom: 5px;
            padding: 10px 15px;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(0,86,179,0.1);
        }
        
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
        }
        
        .certification-level {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .level-1 { background-color: #17a2b8; }
        .level-2 { background-color: #28a745; }
        .level-3 { background-color: #fd7e14; }
        .level-4 { background-color: #dc3545; }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 3px 8px;
            border-radius: 10px;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 20px;
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            left: -30px;
            top: 0;
            width: 2px;
            height: 100%;
            background-color: #dee2e6;
        }
        
        .timeline-item:last-child:before {
            height: 0;
        }
        
        .timeline-badge {
            position: absolute;
            left: -39px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.7rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <img src="https://via.placeholder.com/150x40?text=MS+知识平台" alt="Materials Studio 知识解答平台">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 管理控制台</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="adminDropdown" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> 管理员
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog"></i> 个人设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar">
                    <h5 class="mb-3"><i class="fas fa-cogs"></i> 系统管理</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="user-roles.html">
                                <i class="fas fa-users-cog"></i> 用户角色与权限
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="user-management.html">
                                <i class="fas fa-user-edit"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="content-management.html">
                                <i class="fas fa-file-alt"></i> 内容管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="certification-management.html">
                                <i class="fas fa-certificate"></i> 专业认证管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="statistics.html">
                                <i class="fas fa-chart-bar"></i> 数据统计
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-lg-9">
                <h3 class="mb-4">专业认证管理</h3>
                
                <!-- 认证类型管理卡片 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-certificate me-2"></i>认证类型管理</span>
                        <button class="btn btn-sm btn-primary"><i class="fas fa-plus me-1"></i> 添加认证类型</button>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>专业认证需要进行资质审核，确保专家用户具备相应的专业背景和技能。
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>认证等级</th>
                                        <th>认证名称</th>
                                        <th>认证要求</th>
                                        <th>认证用户数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="certification-level level-1">1</div>
                                        </td>
                                        <td>Materials Studio 基础用户</td>
                                        <td>至少1年Materials Studio使用经验，通过基础知识测试</td>
                                        <td>124</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="certification-level level-2">2</div>
                                        </td>
                                        <td>Materials Studio 高级用户</td>
                                        <td>至少2年Materials Studio使用经验，完成至少5个研究项目</td>
                                        <td>86</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="certification-level level-3">3</div>
                                        </td>
                                        <td>Materials Studio 专业用户</td>
                                        <td>至少3年Materials Studio使用经验，有相关论文发表</td>
                                        <td>42</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="certification-level level-4">4</div>
                                        </td>
                                        <td>Materials Studio 专家</td>
                                        <td>5年以上Materials Studio使用经验，在材料模拟领域有突出贡献</td>
                                        <td>18</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <h6>认证用户分布</h6>
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar bg-info" role="progressbar" style="width: 46%;" aria-valuenow="46" aria-valuemin="0" aria-valuemax="100">基础用户 (46%)</div>
                                <div class="progress-bar bg-success" role="progressbar" style="width: 32%;" aria-valuenow="32" aria-valuemin="0" aria-valuemax="100">高级用户 (32%)</div>
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 15%;" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100">专业用户 (15%)</div>
                                <div class="progress-bar bg-danger" role="progressbar" style="width: 7%;" aria-valuenow="7" aria-valuemin="0" aria-valuemax="100">专家 (7%)</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 认证申请管理卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-clipboard-check me-2"></i>认证申请管理
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs mb-3" id="certificationTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="true">
                                    待审核 <span class="badge bg-danger">8</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" type="button" role="tab" aria-controls="approved" aria-selected="false">
                                    已通过 <span class="badge bg-success">15</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" type="button" role="tab" aria-controls="rejected" aria-selected="false">
                                    已拒绝 <span class="badge bg-secondary">4</span>
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="certificationTabsContent">
                            <div class="tab-pane fade show active" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>申请ID</th>
                                                <th>申请人</th>
                                                <th>申请类型</th>
                                                <th>申请时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#20231105-01</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="用户头像">
                                                        <div>
                                                            <div>张明</div>
                                                            <small class="text-muted"><EMAIL></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="certification-level level-2" style="display: inline-flex; width: 20px; height: 20px; font-size: 0.7rem;">2</div>
                                                    <span class="ms-1">高级用户</span>
                                                </td>
                                                <td>2023-11-05 14:30</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#reviewModal">审核</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#20231104-08</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="用户头像">
                                                        <div>
                                                            <div>李华</div>
                                                            <small class="text-muted"><EMAIL></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="certification-level level-3" style="display: inline-flex; width: 20px; height: 20px; font-size: 0.7rem;">3</div>
                                                    <span class="ms-1">专业用户</span>
                                                </td>
                                                <td>2023-11-04 09:45</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#reviewModal">审核</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="tab-pane fade" id="approved" role="tabpanel" aria-labelledby="approved-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>申请ID</th>
                                                <th>申请人</th>
                                                <th>认证类型</th>
                                                <th>审核时间</th>
                                                <th>审核人</th>
                                                <th>详情</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#20231103-05</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="用户头像">
                                                        <div>
                                                            <div>王军</div>
                                                            <small class="text-muted"><EMAIL></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="certification-level level-2" style="display: inline-flex; width: 20px; height: 20px; font-size: 0.7rem;">2</div>
                                                    <span class="ms-1">高级用户</span>
                                                </td>
                                                <td>2023-11-03 16:20</td>
                                                <td>管理员</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#detailModal">查看</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>#20231102-03</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="用户头像">
                                                        <div>
                                                            <div>赵敏</div>
                                                            <small class="text-muted"><EMAIL></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="certification-level level-1" style="display: inline-flex; width: 20px; height: 20px; font-size: 0.7rem;">1</div>
                                                    <span class="ms-1">基础用户</span>
                                                </td>
                                                <td>2023-11-02 10:15</td>
                                                <td>管理员</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#detailModal">查看</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="tab-pane fade" id="rejected" role="tabpanel" aria-labelledby="rejected-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>申请ID</th>
                                                <th>申请人</th>
                                                <th>申请类型</th>
                                                <th>拒绝原因</th>
                                                <th>审核时间</th>
                                                <th>详情</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#20231101-04</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="用户头像">
                                                        <div>
                                                            <div>刘强</div>
                                                            <small class="text-muted"><EMAIL></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="certification-level level-3" style="display: inline-flex; width: 20px; height: 20px; font-size: 0.7rem;">3</div>
                                                    <span class="ms-1">专业用户</span>
                                                </td>
                                                <td>资质证明不足</td>
                                                <td>2023-11-01 11:30</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#detailModal">查看</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 审核模态框 -->
    <div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewModalLabel">认证申请审核 - #20231105-01</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>申请信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">申请人</th>
                                    <td>张明 (<EMAIL>)</td>
                                </tr>
                                <tr>
                                    <th>申请类型</th>
                                    <td>
                                        <div class="certification-level level-2" style="display: inline-flex; width: 20px; height: 20px; font-size: 0.7rem;">2</div>
                                        <span class="ms-1">高级用户</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>申请时间</th>
                                    <td>2023-11-05 14:30</td>
                                </tr>
                                <tr>
                                    <th>当前状态</th>
                                    <td><span class="badge bg-warning">待审核</span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>用户信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">注册时间</th>
                                    <td>2022-03-15</td>
                                </tr>
                                <tr>
                                    <th>活跃度</th>
                                    <td>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted">75% (高)</small>
                                    </td>
                                </tr>
                                <tr>
                                    <th>回答数量</th>
                                    <td>28</td>
                                </tr>
                                <tr>
                                    <th>采纳率</th>
                                    <td>82%</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h6>专业背景</h6>
                        <div class="card">
                            <div class="card-body">
                                <p>北京科技大学材料科学与工程专业硕士，目前在中科院金属研究所从事材料模拟相关研究工作。擅长使用Materials Studio进行分子动力学模拟和第一性原理计算。</p>
                                <p>已使用Materials Studio软件3年，完成了多个材料模拟项目，包括金属合金结构优化、分子吸附行为模拟等。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h6>资质证明</h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <img src="https://via.placeholder.com/300x200?text=证书" class="card-img-top" alt="证书">
                                    <div class="card-body p-2">
                                        <small class="text-muted">Materials Studio培训证书</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <img src="https://via.placeholder.com/300x200?text=论文" class="card-img-top" alt="论文">
                                    <div class="card-body p-2">
                                        <small class="text-muted">使用Materials Studio的研究论文</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <img src="https://via.placeholder.com/300x200?text=项目" class="card-img-top" alt="项目">
                                    <div class="card-body p-2">
                                        <small class="text-muted">材料模拟项目案例</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reviewComment" class="form-label">审核意见</label>
                        <textarea class="form-control" id="reviewComment" rows="3" placeholder="请输入审核意见..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger me-2">拒绝</button>
                    <button type="button" class="btn btn-success">通过</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部版权信息 -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2023 Materials Studio 知识解答平台. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white-50 text-decoration-none me-3">隐私政策</a>
                    <a href="#" class="text-white-50 text-decoration-none me-3">使用条款</a>
                    <a href="#" class="text-white-50 text-decoration-none">联系我们</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 