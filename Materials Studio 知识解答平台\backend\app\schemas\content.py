from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from .user import User


# 资源分类基础模式
class ResourceCategoryBase(BaseModel):
    """资源分类基础模式"""
    name: str
    description: Optional[str] = None


# 创建资源分类请求模式
class ResourceCategoryCreate(ResourceCategoryBase):
    """创建资源分类请求模式"""
    pass


# 更新资源分类请求模式
class ResourceCategoryUpdate(ResourceCategoryBase):
    """更新资源分类请求模式"""
    name: Optional[str] = None


# 返回给API的资源分类模式
class ResourceCategory(ResourceCategoryBase):
    """返回给API的资源分类模式"""
    id: int
    created_at: datetime
    
    class Config:
        orm_mode = True


# 资源基础模式
class ResourceBase(BaseModel):
    """资源基础模式"""
    title: str
    description: Optional[str] = None
    file_path: str
    file_size: Optional[int] = None
    file_type: Optional[str] = None
    is_premium: bool = False


# 创建资源请求模式
class ResourceCreate(ResourceBase):
    """创建资源请求模式"""
    category_id: Optional[int] = None


# 更新资源请求模式
class ResourceUpdate(BaseModel):
    """更新资源请求模式"""
    title: Optional[str] = None
    description: Optional[str] = None
    category_id: Optional[int] = None
    is_premium: Optional[bool] = None


# 返回给API的资源模式
class Resource(ResourceBase):
    """返回给API的资源模式"""
    id: int
    uploader_id: int
    category_id: Optional[int] = None
    download_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 资源详情模式（包含上传者和分类信息）
class ResourceDetail(Resource):
    """资源详情模式"""
    uploader: User
    category: Optional[ResourceCategory] = None


# 术语词典基础模式
class GlossaryBase(BaseModel):
    """术语词典基础模式"""
    term: str
    definition: str
    example: Optional[str] = None
    related_terms: Optional[str] = None
    category: Optional[str] = None
    is_premium: bool = False


# 创建术语词典请求模式
class GlossaryCreate(GlossaryBase):
    """创建术语词典请求模式"""
    pass


# 更新术语词典请求模式
class GlossaryUpdate(BaseModel):
    """更新术语词典请求模式"""
    term: Optional[str] = None
    definition: Optional[str] = None
    example: Optional[str] = None
    related_terms: Optional[str] = None
    category: Optional[str] = None
    is_premium: Optional[bool] = None


# 返回给API的术语词典模式
class Glossary(GlossaryBase):
    """返回给API的术语词典模式"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 收藏基础模式
class FavoriteBase(BaseModel):
    """收藏基础模式"""
    user_id: int
    question_id: Optional[int] = None
    resource_id: Optional[int] = None
    glossary_id: Optional[int] = None


# 创建收藏请求模式
class FavoriteCreate(BaseModel):
    """创建收藏请求模式"""
    question_id: Optional[int] = None
    resource_id: Optional[int] = None
    glossary_id: Optional[int] = None


# 返回给API的收藏模式
class Favorite(FavoriteBase):
    """返回给API的收藏模式"""
    id: int
    created_at: datetime
    
    class Config:
        orm_mode = True


# 搜索历史基础模式
class SearchHistoryBase(BaseModel):
    """搜索历史基础模式"""
    user_id: int
    query: str
    search_type: str = "all"  # all, question, resource, glossary


# 创建搜索历史请求模式
class SearchHistoryCreate(BaseModel):
    """创建搜索历史请求模式"""
    query: str
    search_type: str = "all"


# 返回给API的搜索历史模式
class SearchHistory(SearchHistoryBase):
    """返回给API的搜索历史模式"""
    id: int
    created_at: datetime
    
    class Config:
        orm_mode = True


# 通知基础模式
class NotificationBase(BaseModel):
    """通知基础模式"""
    user_id: int
    title: str
    content: str
    notification_type: str  # answer, comment, system
    reference_id: Optional[int] = None
    is_read: bool = False


# 创建通知请求模式
class NotificationCreate(BaseModel):
    """创建通知请求模式"""
    user_id: int
    title: str
    content: str
    notification_type: str
    reference_id: Optional[int] = None


# 更新通知请求模式
class NotificationUpdate(BaseModel):
    """更新通知请求模式"""
    is_read: Optional[bool] = None


# 返回给API的通知模式
class Notification(NotificationBase):
    """返回给API的通知模式"""
    id: int
    created_at: datetime
    
    class Config:
        orm_mode = True


# 搜索结果模式
class SearchResult(BaseModel):
    """搜索结果模式"""
    questions: List[Resource] = []
    resources: List[Resource] = []
    glossary: List[Glossary] = []
    total_count: int = 0 