/**
 * user.js - 用户信息和会员管理相关的API交互
 * 处理用户资料、会员信息的获取和更新
 */

// API基础URL，从配置文件获取
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:8000/api/v1';

/**
 * 获取当前用户资料
 * @returns {Promise} - 包含用户资料的Promise
 */
async function getUserProfile() {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/users/me/profile`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取用户资料失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取用户资料错误:', error);
        throw error;
    }
}

/**
 * 更新用户资料
 * @param {Object} profileData - 要更新的用户资料数据
 * @param {string} profileData.full_name - 全名
 * @param {string} profileData.bio - 个人简介
 * @param {string} profileData.organization - 组织/单位
 * @param {string} profileData.location - 所在地
 * @param {string} profileData.avatar_url - 头像URL
 * @returns {Promise} - 包含更新后的用户资料的Promise
 */
async function updateUserProfile(profileData) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/users/me/profile`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(profileData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '更新用户资料失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('更新用户资料错误:', error);
        throw error;
    }
}

/**
 * 更新用户基本信息
 * @param {Object} userData - 要更新的用户基本信息
 * @param {string} userData.email - 邮箱
 * @param {string} userData.username - 用户名
 * @param {string} userData.phone - 手机号
 * @returns {Promise} - 包含更新后的用户信息的Promise
 */
async function updateUserInfo(userData) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/users/me`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(userData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '更新用户信息失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('更新用户信息错误:', error);
        throw error;
    }
}

/**
 * 更新用户密码
 * @param {Object} passwordData - 密码更新数据
 * @param {string} passwordData.current_password - 当前密码
 * @param {string} passwordData.new_password - 新密码
 * @returns {Promise} - 包含更新结果的Promise
 */
async function updatePassword(passwordData) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/users/me/password`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(passwordData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '更新密码失败');
        }
        
        return true; // 更新成功
    } catch (error) {
        console.error('更新密码错误:', error);
        throw error;
    }
}

/**
 * 获取当前用户的会员信息
 * @returns {Promise} - 包含会员信息的Promise
 */
async function getMembershipInfo() {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/users/me/membership`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取会员信息失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取会员信息错误:', error);
        throw error;
    }
}

/**
 * 上传用户头像
 * @param {File} file - 要上传的头像文件
 * @returns {Promise} - 包含上传结果的Promise
 */
async function uploadAvatar(file) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch(`${API_BASE_URL}/users/me/avatar`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '上传头像失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('上传头像错误:', error);
        throw error;
    }
}

/**
 * 获取用户活动历史
 * @param {number} page - 页码
 * @param {number} pageSize - 每页条数
 * @returns {Promise} - 包含用户活动历史的Promise
 */
async function getUserActivity(page = 1, pageSize = 10) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/users/me/activity?page=${page}&page_size=${pageSize}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取用户活动历史失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取用户活动历史错误:', error);
        throw error;
    }
}

/**
 * 获取用户问答统计信息
 * @returns {Promise} - 包含用户问答统计信息的Promise
 */
async function getUserStats() {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/users/me/stats`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取用户统计信息失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取用户统计信息错误:', error);
        throw error;
    }
}

// 导出API函数
window.userAPI = {
    getUserProfile,
    updateUserProfile,
    updateUserInfo,
    updatePassword,
    getMembershipInfo,
    uploadAvatar,
    getUserActivity,
    getUserStats
}; 