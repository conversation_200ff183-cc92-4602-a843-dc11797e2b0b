# Materials Studio答疑平台

## 前端和后端连接指南

本项目已实现前端与后端连接，当前端点击某些功能时，后端会自动响应请求。以下是使用说明和连接步骤。

### 先决条件

1. Node.js (建议使用v14.0.0或更高版本)
2. 浏览器 (推荐Chrome或Edge)

### 启动模拟后端服务器

1. 进入模拟服务器目录
   ```
   cd "Materials Studio答疑平台/mock-server"
   ```

2. 安装依赖
   ```
   npm install
   ```

3. 启动服务器
   ```
   npm start
   ```

   服务器将在 http://localhost:8000 上运行，API基础路径为 http://localhost:8000/api/v1

### 访问前端页面

前端页面已经配置为连接到后端API。您可以直接在浏览器中打开以下HTML文件：

1. **首页**: `index.html` - 平台主页
2. **登录页**: `login.html` - 用户登录页面
3. **问答页**: `qa.html` - 问答列表页面
4. **问题详情页**: `qa-detail.html` - 问题详情与回答页面
5. **会员页**: `membership.html` - 会员资格与权限页面
6. **API测试页**: `api-test.html` - 测试所有API功能的工具页面

### API测试页面

`api-test.html` 页面是一个特殊的工具页面，它允许您测试所有可用的API功能，查看请求和响应数据，以及检查API服务器状态。这对于开发和调试非常有用。

### 测试账号

在模拟服务器中已预设以下测试账号：

- 管理员: admin / admin123
- 会员: member / member123
- 普通用户: user / user123

### API集成原理

前端通过JavaScript API模块与后端进行通信：

1. **utils.js**: 提供基础HTTP请求功能和通用工具
2. **auth.js**: 处理用户认证相关功能
3. **qa.js**: 处理问答相关功能
4. **user.js**: 处理用户信息相关功能
5. **content.js**: 处理内容资源相关功能
6. **index.js**: 集成所有API模块，提供统一访问入口

所有API请求都使用JSON格式进行数据交换，遵循RESTful API设计原则。

### 注意事项

1. 确保模拟服务器在使用前端页面前已启动
2. 默认API基础URL为`http://localhost:8000/api/v1`，可在API测试页面中修改
3. 若需使用实际的Java/SpringBoot后端，只需将API基础URL指向实际后端地址即可 