package com.materialsstudio.qa.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 回答发布DTO
 */
@Data
public class AnswerPostDTO {
    
    /**
     * 问题ID
     */
    @NotNull(message = "问题ID不能为空")
    private Long questionId;
    
    /**
     * 回答内容
     */
    @NotBlank(message = "回答内容不能为空")
    @Size(min = 10, message = "回答内容长度不能少于10个字符")
    private String content;
    
    /**
     * 是否为会员专属
     */
    @NotNull(message = "请指定是否为会员专属")
    private Boolean isPremium;
} 