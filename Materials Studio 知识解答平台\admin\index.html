<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理控制台 - Materials Studio 知识解答平台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        
        .admin-header {
            background-color: #343a40;
            color: #fff;
            padding: 1rem 0;
        }
        
        .admin-sidebar {
            background-color: #343a40;
            min-height: calc(100vh - 56px);
            padding-top: 1rem;
            position: sticky;
            top: 56px;
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
            padding: 0.75rem 1.25rem;
            font-size: 0.9rem;
        }
        
        .admin-sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .admin-sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            font-weight: 500;
        }
        
        .admin-sidebar .nav-link i {
            margin-right: 0.5rem;
        }
        
        .admin-sidebar .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .admin-sidebar .nav-divider {
            height: 1px;
            background-color: rgba(255, 255, 255, 0.1);
            margin: 0.5rem 0;
        }
        
        .admin-sidebar .sidebar-heading {
            color: rgba(255, 255, 255, 0.5);
            padding: 0.75rem 1.25rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }
        
        .admin-content {
            padding: 2rem 1.5rem;
        }
        
        .stat-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .stat-card .card-body {
            padding: 1.5rem;
        }
        
        .stat-card .stat-icon {
            font-size: 2.5rem;
            opacity: 0.15;
            position: absolute;
            right: 1.5rem;
            bottom: 1.5rem;
        }
        
        .stat-card .stat-value {
            font-size: 2rem;
            font-weight: 700;
            line-height: 1.2;
        }
        
        .stat-card .stat-label {
            color: #6c757d;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .chart-container {
            height: 300px;
            margin-bottom: 1.5rem;
        }
        
        .recent-item {
            border-left: 4px solid transparent;
            padding: 10px 15px;
            margin-bottom: 10px;
            background-color: #fff;
            border-radius: 0 4px 4px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,.1);
            transition: all .2s;
        }
        
        .recent-item:hover {
            border-left-color: #0d6efd;
            background-color: #f8f9fa;
        }
        
        .recent-item-users {
            border-left-color: #0dcaf0;
        }
        
        .recent-item-questions {
            border-left-color: #198754;
        }
        
        .recent-item-answers {
            border-left-color: #ffc107;
        }
        
        .recent-item-resources {
            border-left-color: #6f42c1;
        }
        
        .recent-item .time {
            color: #6c757d;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- 管理员顶部导航栏 -->
    <nav class="navbar navbar-dark navbar-expand-md admin-header">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">Materials Studio 管理控制台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar" aria-controls="adminNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="adminNavbar">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i>
                            <span id="admin-name">管理员</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="../index.html">返回前台</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="admin-logout">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2 admin-sidebar d-md-block collapse" id="adminSidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">
                            <i class="bi bi-speedometer2"></i> 控制台
                        </a>
                    </li>
                    <li><div class="nav-divider"></div></li>
                    <li class="sidebar-heading">内容管理</li>
                    <li class="nav-item">
                        <a class="nav-link" href="questions.html">
                            <i class="bi bi-question-circle"></i> 问题管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="answers.html">
                            <i class="bi bi-chat-right-text"></i> 回答管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html">
                            <i class="bi bi-journal-richtext"></i> 资源管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="comments.html">
                            <i class="bi bi-chat-dots"></i> 评论管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.html">
                            <i class="bi bi-grid"></i> 分类管理
                        </a>
                    </li>
                    <li><div class="nav-divider"></div></li>
                    <li class="sidebar-heading">用户管理</li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="roles.html">
                            <i class="bi bi-person-badge"></i> 角色管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="permissions.html">
                            <i class="bi bi-shield-lock"></i> 权限管理
                        </a>
                    </li>
                    <li><div class="nav-divider"></div></li>
                    <li class="sidebar-heading">系统设置</li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.html">
                            <i class="bi bi-gear"></i> 系统设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logs.html">
                            <i class="bi bi-file-text"></i> 审计日志
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                    <h1 class="h2">控制台</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-download"></i> 导出
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-arrow-repeat"></i> 刷新
                            </button>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle">
                            <i class="bi bi-calendar3"></i> 今日
                        </button>
                    </div>
                </div>

                <!-- 统计数据卡片 -->
                <div class="row">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card bg-primary text-white h-100">
                            <div class="card-body">
                                <div class="stat-label">用户数</div>
                                <div class="stat-value" id="user-count">--</div>
                                <div class="d-flex align-items-center">
                                    <span id="user-increase" class="me-2">--</span>
                                    <small>今日新增</small>
                                </div>
                                <i class="bi bi-people stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card bg-success text-white h-100">
                            <div class="card-body">
                                <div class="stat-label">问题数</div>
                                <div class="stat-value" id="question-count">--</div>
                                <div class="d-flex align-items-center">
                                    <span id="question-increase" class="me-2">--</span>
                                    <small>今日新增</small>
                                </div>
                                <i class="bi bi-question-circle stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card bg-warning text-white h-100">
                            <div class="card-body">
                                <div class="stat-label">回答数</div>
                                <div class="stat-value" id="answer-count">--</div>
                                <div class="d-flex align-items-center">
                                    <span id="answer-increase" class="me-2">--</span>
                                    <small>今日新增</small>
                                </div>
                                <i class="bi bi-chat-right-text stat-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card bg-info text-white h-100">
                            <div class="card-body">
                                <div class="stat-label">资源数</div>
                                <div class="stat-value" id="resource-count">--</div>
                                <div class="d-flex align-items-center">
                                    <span id="resource-increase" class="me-2">--</span>
                                    <small>今日新增</small>
                                </div>
                                <i class="bi bi-journal-richtext stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 活跃度图表 -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">平台活跃度统计</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="activityChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">快捷操作</h5>
                            </div>
                            <div class="card-body">
                                <div class="list-group">
                                    <a href="users.html" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="badge bg-primary rounded-pill me-3">
                                            <i class="bi bi-person-plus"></i>
                                        </span>
                                        添加新用户
                                    </a>
                                    <a href="roles.html" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="badge bg-success rounded-pill me-3">
                                            <i class="bi bi-shield-plus"></i>
                                        </span>
                                        创建新角色
                                    </a>
                                    <a href="categories.html" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="badge bg-info rounded-pill me-3">
                                            <i class="bi bi-folder-plus"></i>
                                        </span>
                                        添加新分类
                                    </a>
                                    <a href="resources.html" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="badge bg-warning rounded-pill me-3">
                                            <i class="bi bi-cloud-upload"></i>
                                        </span>
                                        上传新资源
                                    </a>
                                    <a href="settings.html" class="list-group-item list-group-item-action d-flex align-items-center">
                                        <span class="badge bg-dark rounded-pill me-3">
                                            <i class="bi bi-gear"></i>
                                        </span>
                                        系统设置
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">最近活动</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-activities">
                                    <div class="text-center py-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-3">加载最近活动数据...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="../js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户是否登录且有管理权限
            if (!auth.isAuthenticated()) {
                window.location.href = '../login.html?redirect=admin/index.html';
                return;
            }
            
            if (!auth.hasRole(['admin', 'moderator'])) {
                alert('您没有管理权限');
                window.location.href = '../index.html';
                return;
            }
            
            // 设置管理员名称
            const adminNameEl = document.getElementById('admin-name');
            if (adminNameEl && auth.user) {
                adminNameEl.textContent = auth.user.name || auth.user.username;
            }
            
            // 登出功能
            const logoutBtn = document.getElementById('admin-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    auth.logout();
                    window.location.href = '../login.html';
                });
            }
            
            // 加载统计数据
            loadDashboardData();
            
            // 初始化图表
            initActivityChart();
            
            // 加载最近活动
            loadRecentActivities();
        });
        
        // 加载数据
        async function loadDashboardData() {
            try {
                // 在实际场景中，这些数据应该从API获取
                // 这里使用虚拟数据进行展示
                const stats = {
                    users: {
                        total: 5842,
                        increase: 128
                    },
                    questions: {
                        total: 12673,
                        increase: 247
                    },
                    answers: {
                        total: 34891,
                        increase: 468
                    },
                    resources: {
                        total: 3156,
                        increase: 57
                    }
                };
                
                // 更新统计数据
                document.getElementById('user-count').textContent = stats.users.total;
                document.getElementById('user-increase').textContent = `+${stats.users.increase}`;
                
                document.getElementById('question-count').textContent = stats.questions.total;
                document.getElementById('question-increase').textContent = `+${stats.questions.increase}`;
                
                document.getElementById('answer-count').textContent = stats.answers.total;
                document.getElementById('answer-increase').textContent = `+${stats.answers.increase}`;
                
                document.getElementById('resource-count').textContent = stats.resources.total;
                document.getElementById('resource-increase').textContent = `+${stats.resources.increase}`;
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }
        
        // 初始化活跃度图表
        function initActivityChart() {
            const ctx = document.getElementById('activityChart');
            
            // 样本数据（实际项目中应从API获取）
            const data = {
                labels: ['1日', '2日', '3日', '4日', '5日', '6日', '7日', '8日', '9日', '10日', '11日', '12日', '13日', '14日', '15日'],
                datasets: [
                    {
                        label: '用户注册',
                        data: [42, 38, 55, 48, 58, 43, 39, 47, 52, 57, 62, 55, 61, 45, 48],
                        borderColor: 'rgba(13, 110, 253, 0.8)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '问题提问',
                        data: [65, 59, 80, 81, 56, 55, 40, 61, 58, 75, 78, 71, 69, 83, 76],
                        borderColor: 'rgba(25, 135, 84, 0.8)',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '回答数量',
                        data: [28, 48, 40, 19, 86, 27, 90, 55, 60, 65, 45, 76, 80, 91, 88],
                        borderColor: 'rgba(255, 193, 7, 0.8)',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            };
            
            new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
        
        // 加载最近活动
        function loadRecentActivities() {
            // 实际项目中从API获取数据
            // 这里使用虚拟数据
            const activities = [
                {
                    type: 'users',
                    action: '注册了新账户',
                    user: '张三',
                    time: '10分钟前'
                },
                {
                    type: 'questions',
                    action: '发表了新问题',
                    user: '李四',
                    title: '关于Materials Studio中分子动力学模拟设置的问题',
                    time: '30分钟前'
                },
                {
                    type: 'answers',
                    action: '回答了问题',
                    user: '王五',
                    title: '如何在Materials Studio中设置CASTEP计算参数？',
                    time: '1小时前'
                },
                {
                    type: 'resources',
                    action: '上传了新资源',
                    user: '赵六',
                    title: 'Materials Studio使用教程PDF',
                    time: '2小时前'
                },
                {
                    type: 'users',
                    action: '被提升为专家用户',
                    user: '钱七',
                    time: '3小时前'
                },
                {
                    type: 'questions',
                    action: '问题被标记为已解决',
                    user: '孙八',
                    title: 'Materials Studio中如何导入晶体结构？',
                    time: '4小时前'
                }
            ];
            
            const activitiesContainer = document.getElementById('recent-activities');
            
            let html = '';
            activities.forEach(activity => {
                html += `
                    <div class="recent-item recent-item-${activity.type}">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <div><strong>${activity.user}</strong> ${activity.action}</div>
                            <div class="time">${activity.time}</div>
                        </div>
                        ${activity.title ? `<div>${activity.title}</div>` : ''}
                    </div>
                `;
            });
            
            activitiesContainer.innerHTML = html;
        }
    </script>
</body>
</html>