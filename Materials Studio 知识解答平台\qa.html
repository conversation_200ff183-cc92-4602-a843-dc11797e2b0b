<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问答中心 - Materials Studio 知识解答平台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入动画和可视化库 -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
    <style>
        :root {
            --primary-color: #0056b3;
            --secondary-color: #004494;
            --light-bg: #f8f9fa;
            --member-color: #ffc107;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: #333;
        }
        .navbar-brand img {
            max-height: 40px;
        }
        .navbar {
            background-color: var(--primary-color);
        }
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.8);
        }
        .navbar-dark .navbar-nav .nav-link:hover {
            color: rgba(255,255,255,1);
        }
        .member-badge {
            background-color: var(--member-color);
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 5px;
        }
        .page-header {
            background: linear-gradient(135deg, #0056b3 0%, #3498db 100%);
            color: white;
            padding: 30px 0;
        }
        .category-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s;
            margin-bottom: 20px;
        }
        .category-card:hover {
            transform: translateY(-5px);
        }
        .question-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            margin-bottom: 25px;
            overflow: hidden;
        }
        .question-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.12);
        }
        .question-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .member-content {
            background-color: #fff8e6;
            border: 1px dashed var(--member-color);
            border-radius: 5px;
            padding: 10px;
        }
        .question-tag {
            font-size: 0.75rem;
            padding: 3px 10px;
            border-radius: 15px;
            background-color: #e9ecef;
            color: #495057;
            margin-right: 5px;
            display: inline-block;
        }
        footer {
            background-color: #343a40;
            color: white;
        }
        .footer-links a {
            color: rgba(255,255,255,.7);
            text-decoration: none;
        }
        .footer-links a:hover {
            color: white;
        }
        .sidebar-heading {
            font-size: 1.1rem;
            font-weight: 600;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .nav-pills .nav-link.active {
            background-color: var(--primary-color);
        }
        .nav-pills .nav-link {
            color: #495057;
        }
        .ask-question-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }
        /* 问答页面增强样式 */
        .qa-hero {
            background: linear-gradient(120deg, #0056b3, #3498db);
            color: white;
            padding: 60px 0;
            position: relative;
            overflow: hidden;
        }
        
        .qa-hero:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://images.unsplash.com/photo-1557425529-b1ae9c141e7a?ixid=MnwxMjA3fDB8MHxzZWFyY2h8MXx8bW9sZWN1bGUlMjBzdHJ1Y3R1cmV8ZW58MHx8MHx8&auto=format&fit=crop&w=1200&q=60') center/cover no-repeat;
            opacity: 0.1;
            z-index: 0;
        }
        
        .qa-hero .container {
            position: relative;
            z-index: 1;
        }
        
        /* 问题卡片增强样式 */
        .question-header {
            padding: 15px 20px;
            background: linear-gradient(to right, rgba(0,86,179,0.05), rgba(0,86,179,0.01));
            border-bottom: 1px solid rgba(0,86,179,0.1);
        }
        
        /* 标签增强样式 */
        .tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s;
        }
        
        .tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .tag-method {
            background-color: rgba(52, 152, 219, 0.15);
            color: #2980b9;
        }
        
        .tag-parameter {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }
        
        .tag-error {
            background-color: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }
        
        .tag-analysis {
            background-color: rgba(155, 89, 182, 0.15);
            color: #8e44ad;
        }
        
        /* 代码块增强样式 */
        pre {
            border-radius: 8px;
            background: #282c34;
            padding: 15px;
            margin: 15px 0;
            overflow: auto;
        }
        
        code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
        }
        
        /* 分子结构查看器 */
        .molecule-viewer {
            width: 100%;
            height: 250px;
            background-color: rgba(0,0,0,0.03);
            border-radius: 8px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }
        
        /* 回答增强样式 */
        .answer-container {
            padding: 20px;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .expert-badge {
            display: inline-block;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 3px 10px;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 10px;
            vertical-align: middle;
        }
        
        /* 可视化工具栏 */
        .visualization-toolbar {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px 15px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .viz-btn {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            background: white;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .viz-btn:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,0.05);
        }
        
        .viz-btn.active {
            background: #0056b3;
            color: white;
            border-color: #0056b3;
        }
        
        /* 引用样式 */
        blockquote {
            border-left: 4px solid #0056b3;
            padding: 10px 20px;
            background: rgba(0,86,179,0.05);
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        
        blockquote p {
            margin: 0;
            font-style: italic;
            color: #555;
        }
        
        /* 数学公式容器 */
        .math-container {
            overflow-x: auto;
            margin: 15px 0;
            padding: 10px 0;
        }
        
        /* 搜索增强 */
        .enhanced-search {
            border-radius: 30px;
            padding: 15px 25px;
            border: none;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s;
            width: 100%;
        }
        
        .enhanced-search:focus {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .search-button {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: #0056b3;
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }
        
        .search-button:hover {
            background: #004494;
            transform: translateY(-50%) scale(1.05);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        
        /* 快捷按钮样式 */
        .action-btn-group {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .action-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.2);
        }
        
        .action-btn i {
            font-size: 1.5rem;
        }
        
        /* 模态框增强样式 */
        .custom-modal .modal-header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .custom-modal .modal-body {
            padding: 20px;
        }
        
        .tag-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .tag-selector .tag-item {
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tag-selector .tag-item:hover {
            transform: scale(1.05);
        }
        
        .tag-selector .tag-item.selected {
            border: 2px solid var(--primary-color);
        }

        /* 导航栏当前页面指示样式 */
        .navbar-dark .navbar-nav .nav-link.active {
            color: rgba(255,255,255,1);
            background-color: rgba(255,255,255,0.2);
            font-weight: 600;
            position: relative;
        }

        .navbar-dark .navbar-nav .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--member-color);
            border-radius: 3px;
        }

        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.8);
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .navbar-dark .navbar-nav .nav-link:hover {
            color: rgba(255,255,255,1);
            background-color: rgba(255,255,255,0.1);
        }

        /* 文件上传预览样式 */
        .file-preview-container {
            margin-top: 10px;
        }
        
        .file-preview-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .file-preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 5px;
            background-color: #f8f9fa;
        }
        
        .file-preview-image {
            max-width: 100%;
            max-height: 60px;
            object-fit: contain;
        }
        
        .file-name {
            font-size: 0.7rem;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
            margin-top: 5px;
        }
        
        .file-size {
            font-size: 0.65rem;
            color: #6c757d;
        }
        
        .remove-file {
            position: absolute;
            top: 0;
            right: 0;
            padding: 0;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0 0 0 5px;
            font-size: 0.7rem;
        }
        
        /* SimpleMDE编辑器自定义样式 */
        .editor-toolbar {
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            background-color: #f8f9fa;
        }
        
        .CodeMirror {
            border-bottom-left-radius: var(--border-radius);
            border-bottom-right-radius: var(--border-radius);
            height: 200px;
        }
        
        /* 草稿状态提示 */
        .draft-status {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .btn-save-draft {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="https://via.placeholder.com/150x40?text=MS+Platform" alt="Materials Studio 知识解答平台">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
                <div class="navbar-nav me-auto">
                    <a class="nav-link" href="index.html"><i class="fas fa-home"></i> 首页</a>
                    <a class="nav-link active" href="qa.html"><i class="fas fa-question-circle"></i> 问答中心</a>
                    <a class="nav-link" href="resources.html"><i class="fas fa-download"></i> 资源库</a>
                    <a class="nav-link" href="community.html"><i class="fas fa-users"></i> 社区互动</a>
                    <a class="nav-link" href="terminology.html"><i class="fas fa-book"></i> 术语表</a>
                </div>
                <div class="d-flex">
                    <button class="btn btn-outline-light me-2" data-bs-toggle="modal" data-bs-target="#loginModal">登录</button>
                    <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#registerModal">注册</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <header class="qa-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-7" data-aos="fade-right" data-aos-duration="800">
                    <h1 class="display-4 fw-bold">Materials Studio 问答中心</h1>
                    <p class="lead">获取专业解答，解决 Materials Studio 使用过程中的各类问题</p>
                </div>
                <div class="col-lg-5" data-aos="fade-left" data-aos-duration="800" data-aos-delay="200">
                    <div class="position-relative">
                        <input type="text" class="form-control enhanced-search" placeholder="搜索您的问题...">
                        <button type="button" class="search-button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 问答中心内容 -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- 左侧导航 -->
                <div class="col-lg-3">
                    <div class="mb-4">
                        <h5 class="sidebar-heading mb-3">问题分类</h5>
                        <div class="nav flex-column nav-pills">
                            <a class="nav-link active" href="#">所有问题</a>
                            <a class="nav-link" href="#">模拟方法</a>
                            <a class="nav-link" href="#">参数设置</a>
                            <a class="nav-link" href="#">结果分析</a>
                            <a class="nav-link" href="#">软件错误</a>
                            <a class="nav-link" href="#">脚本问题</a>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="sidebar-heading mb-3">模块导航</h5>
                        <div class="nav flex-column nav-pills">
                            <a class="nav-link" href="#">CASTEP</a>
                            <a class="nav-link" href="#">Forcite</a>
                            <a class="nav-link" href="#">GULP</a>
                            <a class="nav-link" href="#">Materials Visualizer</a>
                            <a class="nav-link" href="#">Amorphous Cell</a>
                            <a class="nav-link" href="#">DMol3</a>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="sidebar-heading mb-3">热门标签</h5>
                        <div class="d-flex flex-wrap">
                            <a href="#" class="question-tag mb-2">分子动力学</a>
                            <a href="#" class="question-tag mb-2">晶体结构</a>
                            <a href="#" class="question-tag mb-2">能量优化</a>
                            <a href="#" class="question-tag mb-2">电子结构</a>
                            <a href="#" class="question-tag mb-2">密度泛函</a>
                            <a href="#" class="question-tag mb-2">HOMO-LUMO</a>
                            <a href="#" class="question-tag mb-2">赝势</a>
                            <a href="#" class="question-tag mb-2">力场选择</a>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧内容 -->
                <div class="col-lg-9">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4>最新问题</h4>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown">
                                排序方式
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                <li><a class="dropdown-item" href="#">最新</a></li>
                                <li><a class="dropdown-item" href="#">热门</a></li>
                                <li><a class="dropdown-item" href="#">未回答</a></li>
                                <li><a class="dropdown-item" href="#">已解决</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 问题列表 -->
                    <div class="row">
                        <!-- 示例问题卡片1 - 添加3D分子结构 -->
                        <div class="col-lg-6" data-aos="fade-up" data-aos-duration="600" data-aos-delay="100">
                            <div class="question-card card">
                                <div class="question-header">
                                    <span class="tag tag-method">模拟方法</span>
                                    <span class="tag tag-parameter">参数设置</span>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">如何在CASTEP中设置适当的截断能？</h5>
                                    <p class="card-text">我在模拟ZnO体系时，不确定应该使用什么截断能值才能保证计算精度和效率...</p>
                                    
                                    <!-- 3D分子结构预览 -->
                                    <div class="molecule-viewer" id="molecule-viewer-1"></div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div>
                                            <span class="text-muted"><i class="fas fa-comment me-1"></i> 5个回答</span>
                                            <span class="ms-3 text-muted"><i class="fas fa-eye me-1"></i> 128次浏览</span>
                                        </div>
                                        <a href="#" class="btn btn-sm btn-primary">查看详情</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 示例问题卡片2 - 添加代码高亮 -->
                        <div class="col-lg-6" data-aos="fade-up" data-aos-duration="600" data-aos-delay="200">
                            <div class="question-card card">
                                <div class="question-header">
                                    <span class="tag tag-error">软件错误</span>
                                    <span class="tag tag-analysis">结果分析</span>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">Perl脚本在Materials Studio中无法运行</h5>
                                    <p class="card-text">我尝试运行以下Perl脚本来批量分析结构，但报错"未知函数"：</p>
                                    
                                    <pre><code class="language-perl"># 这是一个示例Perl脚本
sub AnalyzeStructure {
    my ($doc) = @_;
    my $atoms = $doc->UnitCell->Atoms;
    
    foreach my $atom (@$atoms) {
        if ($atom->ElementSymbol eq "O") {
            # 分析氧原子周围环境
            my @neighbors = $doc->NeighborAtoms($atom, 2.0);
            # 不能使用这个函数
            # 应该使用正确的函数，比如：
            # my @neighbors = $doc->CreateAtomicNeighbors($atom, 2.0);
        }
    }
}</code></pre>
                                    
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div>
                                            <span class="text-muted"><i class="fas fa-comment me-1"></i> 3个回答</span>
                                            <span class="ms-3 text-muted"><i class="fas fa-eye me-1"></i> 97次浏览</span>
                                        </div>
                                        <a href="#" class="btn btn-sm btn-primary">查看详情</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 示例问题卡片3 - 添加数学公式 -->
                        <div class="col-lg-6" data-aos="fade-up" data-aos-duration="600" data-aos-delay="300">
                            <div class="question-card card">
                                <div class="question-header">
                                    <span class="tag tag-method">模拟方法</span>
                                    <span class="tag tag-analysis">结果分析</span>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">如何正确解读CASTEP计算的态密度？</h5>
                                    <p class="card-text">我需要理解态密度图中的特征峰，特别是如何将它与能带结构关联起来...</p>
                                    
                                    <div class="math-container">
                                        <p>态密度计算公式：</p>
                                        <div class="text-center" id="density-formula">
                                            \[ D(E) = \sum_{n,\mathbf{k}} \delta(E - E_{n\mathbf{k}}) \]
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div>
                                            <span class="text-muted"><i class="fas fa-comment me-1"></i> 7个回答</span>
                                            <span class="ms-3 text-muted"><i class="fas fa-eye me-1"></i> 215次浏览</span>
                                        </div>
                                        <a href="#" class="btn btn-sm btn-primary">查看详情</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 示例回答展示 -->
                        <div class="col-12 mt-5" data-aos="fade-up" data-aos-duration="800">
                            <h3 class="mb-4">精选专家解答</h3>
                            
                            <div class="answer-container">
                                <div class="d-flex align-items-center mb-3">
                                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Expert Avatar" class="rounded-circle" width="50" height="50">
                                    <div class="ms-3">
                                        <h5 class="mb-0">张教授 <span class="expert-badge">认证专家</span></h5>
                                        <p class="text-muted mb-0">计算材料学博士，10年Materials Studio使用经验</p>
                                    </div>
                                </div>
                                
                                <h5>关于CASTEP截断能的设置问题</h5>
                                
                                <p>截断能(Cutoff Energy)是平面波基组中的重要参数，直接影响计算精度和效率。对于ZnO体系，我建议：</p>
                                
                                <ol>
                                    <li>从相对低的值开始（如340 eV），逐步增加到收敛</li>
                                    <li>观察总能量变化，当增加100 eV使总能量变化小于0.01 eV/atom时可认为收敛</li>
                                    <li>对于含Zn的体系，通常450-550 eV是合理范围</li>
                                </ol>
                                
                                <blockquote>
                                    <p>在研究中，我们发现对于ZnO体系，550 eV的截断能可以使计算结果与实验值很好地吻合，同时保持计算效率。</p>
                                </blockquote>
                                
                                <div class="visualization-toolbar">
                                    <span class="me-2">可视化工具：</span>
                                    <button class="viz-btn active">能量收敛</button>
                                    <button class="viz-btn">晶格参数</button>
                                    <button class="viz-btn">键长分布</button>
                                </div>
                                
                                <canvas id="convergence-chart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 悬浮提问按钮 -->
    <a href="ask-question.html" class="btn btn-primary btn-lg rounded-circle ask-question-btn">
        <i class="fas fa-plus"></i>
    </a>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="loginEmail" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="loginEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">记住我</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </form>
                    <div class="text-center mt-3">
                        <a href="#" class="text-decoration-none">忘记密码?</a>
                        <span class="mx-2">|</span>
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#registerModal" data-bs-dismiss="modal">注册新账号</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="registerName" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="registerName" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="userType" class="form-label">您的身份</label>
                            <select class="form-select" id="userType">
                                <option selected>请选择</option>
                                <option value="researcher">研究人员</option>
                                <option value="student">学生</option>
                                <option value="engineer">工程师</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">我同意<a href="#">服务条款</a>和<a href="#">隐私政策</a></label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                    <div class="text-center mt-3">
                        <span>已有账号?</span>
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#loginModal" data-bs-dismiss="modal">立即登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷操作按钮组 -->
    <div class="action-btn-group">
        <button type="button" class="action-btn btn btn-primary" data-bs-toggle="modal" data-bs-target="#quickAskModal">
            <i class="fas fa-question"></i>
        </button>
        <button type="button" class="action-btn btn btn-success" data-bs-toggle="modal" data-bs-target="#quickFeedbackModal">
            <i class="fas fa-comment-alt"></i>
        </button>
    </div>
    
    <!-- 浮动快速提问按钮 -->
    <div class="quick-fab" data-target="askQuestionModal" data-auth-required>
        <i class="fas fa-question"></i>
    </div>

    <!-- 快速提问模态框 -->
    <div class="modal fade quick-modal" id="quickAskModal" tabindex="-1" aria-labelledby="askQuestionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="askQuestionModalLabel">快速提问</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-question-form" class="needs-validation quick-form">
                        <div class="mb-3">
                            <label for="questionTitle" class="form-label">问题标题</label>
                            <input type="text" class="form-control" id="questionTitle" name="questionTitle" placeholder="简明扼要的描述您的问题" required minlength="5" maxlength="200" data-autosave="true">
                            <div class="invalid-feedback">
                                请输入5-200字符的问题标题
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="questionContent" class="form-label">问题详情</label>
                            <textarea class="form-control markdown-editor" id="questionContent" name="questionContent" rows="6" placeholder="详细描述您的问题，包括您已经尝试过的方法和遇到的具体困难" required minlength="10" data-autosave="true"></textarea>
                            <div class="invalid-feedback">
                                请详细描述您的问题（至少10个字符）
                            </div>
                            <div class="draft-status" id="question-draft-status"></div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="questionCategory" class="form-label">选择分类</label>
                                <select class="form-select" id="questionCategory" name="questionCategory" required data-autosave="true">
                                    <option value="">选择一个分类</option>
                                    <option value="modeling">分子建模</option>
                                    <option value="simulation">分子动力学模拟</option>
                                    <option value="quantum">量子计算</option>
                                    <option value="material">材料性质</option>
                                    <option value="analysis">数据分析</option>
                                    <option value="install">安装部署</option>
                                    <option value="other">其他问题</option>
                                </select>
                                <div class="invalid-feedback">
                                    请选择一个分类
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="questionTags" class="form-label">标签（用逗号分隔）</label>
                                <input type="text" class="form-control" id="questionTags" name="questionTags" placeholder="例如: castep, dft, 能带结构" data-autosave="true">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="fileUpload" class="form-label">附件上传（可选）</label>
                            <input type="file" class="form-control" id="fileUpload" name="fileUpload" multiple>
                            <div class="form-text">支持图片、PDF、Word、Excel等文件格式，单个文件不超过10MB</div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="questionPrivate" name="questionPrivate" data-autosave="true">
                            <label class="form-check-label" for="questionPrivate">设为私密问题（仅自己和管理员可见）</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitQuestion">提交问题</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速回答模态框 -->
    <div class="modal fade quick-modal" id="quickAnswerModal" tabindex="-1" aria-labelledby="quickAnswerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickAnswerModalLabel">回答问题</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="question-to-answer" class="border-bottom pb-3 mb-3">
                        <h5 id="answer-question-title">问题标题将显示在这里</h5>
                        <p id="answer-question-content" class="text-muted">问题内容将显示在这里...</p>
                    </div>
                    <form id="quick-answer-form" class="needs-validation quick-form">
                        <input type="hidden" id="answer-question-id" name="questionId">
                        <div class="mb-3">
                            <label for="answerContent" class="form-label">您的回答</label>
                            <textarea class="form-control markdown-editor" id="answerContent" name="answerContent" rows="8" placeholder="请输入您的详细回答..." required minlength="10" data-autosave="true"></textarea>
                            <div class="invalid-feedback">
                                请输入有效的回答内容（至少10个字符）
                            </div>
                            <div class="form-text">
                                清晰、详细的回答更容易被采纳。支持Markdown语法来格式化您的回答。
                            </div>
                            <div class="draft-status" id="answer-draft-status"></div>
                        </div>
                        <div class="mb-3">
                            <label for="answerAttachment" class="form-label">附件上传（可选）</label>
                            <input type="file" class="form-control" id="answerAttachment" name="answerAttachment" multiple>
                            <div class="form-text">支持图片、PDF、Word、Excel等文件格式，单个文件不超过10MB</div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="notifyMe" name="notifyMe" data-autosave="true">
                            <label class="form-check-label" for="notifyMe">有新回复时通知我</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitAnswer">提交回答</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速反馈模态框 -->
    <div class="modal fade quick-modal" id="quickFeedbackModal" tabindex="-1" aria-labelledby="quickFeedbackModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickFeedbackModalLabel">反馈</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-feedback-form" class="quick-form">
                        <input type="hidden" id="feedback-item-id">
                        <input type="hidden" id="feedback-item-type">
                        <div class="mb-3">
                            <label class="form-label">您对这个内容的评价</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="feedback-rating" id="feedback-helpful" value="helpful" autocomplete="off" checked>
                                <label class="btn btn-outline-success" for="feedback-helpful">
                                    <i class="fas fa-thumbs-up"></i> 有帮助
                                </label>
                                
                                <input type="radio" class="btn-check" name="feedback-rating" id="feedback-neutral" value="neutral" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="feedback-neutral">
                                    <i class="fas fa-minus-circle"></i> 一般
                                </label>
                                
                                <input type="radio" class="btn-check" name="feedback-rating" id="feedback-unhelpful" value="unhelpful" autocomplete="off">
                                <label class="btn btn-outline-danger" for="feedback-unhelpful">
                                    <i class="fas fa-thumbs-down"></i> 无帮助
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="feedback-comment" class="form-label">评论（可选）</label>
                            <textarea class="form-control" id="feedback-comment" rows="3" placeholder="请输入您的评论或建议..."></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="feedback-anonymous">
                            <label class="form-check-label" for="feedback-anonymous">匿名提交</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-quick-action="submit" data-target="quick-feedback-form">提交反馈</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="py-4">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <h5>Materials Studio 知识解答平台</h5>
                    <p>专注于解答 Materials Studio 软件用户问题的在线服务网站。</p>
                    <div class="social-links">
                        <a href="#" class="me-2"><i class="fab fa-weixin fa-lg"></i></a>
                        <a href="#" class="me-2"><i class="fab fa-weibo fa-lg"></i></a>
                        <a href="#" class="me-2"><i class="fab fa-github fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 mb-4 mb-lg-0">
                    <h5>平台</h5>
                    <ul class="list-unstyled footer-links">
                        <li><a href="index.html">首页</a></li>
                        <li><a href="qa.html">问答中心</a></li>
                        <li><a href="resources.html">资源库</a></li>
                        <li><a href="community.html">社区互动</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4 mb-lg-0">
                    <h5>支持</h5>
                    <ul class="list-unstyled footer-links">
                        <li><a href="#">常见问题</a></li>
                        <li><a href="#">使用指南</a></li>
                        <li><a href="#">联系我们</a></li>
                        <li><a href="#">问题反馈</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h5>联系我们</h5>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-comments me-2"></i> 微信群：MS-Knowledge-Group</p>
                    <form class="mt-3">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="订阅我们的最新资讯">
                            <button class="btn btn-primary" type="button">订阅</button>
                        </div>
                    </form>
                </div>
            </div>
            <hr class="my-4 bg-light">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">© 2023 Materials Studio 知识解答平台 | 版权所有</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#">隐私政策</a></li>
                        <li class="list-inline-item"><a href="#">服务条款</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 在页面底部添加必要的JavaScript库 -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/contrib/auto-render.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
    <!-- 快速交互脚本 -->
    <script src="js/quick-interaction.js"></script>
    
    <script>
        // 初始化AOS动画库
        AOS.init({
            once: true
        });
        
        // 初始化代码高亮
        document.addEventListener('DOMContentLoaded', function() {
            // 高亮代码块
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
            
            // 渲染数学公式
            renderMathInElement(document.body, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false},
                    {left: "\\[", right: "\\]", display: true},
                    {left: "\\(", right: "\\)", display: false}
                ]
            });
            
            // 初始化3D分子结构查看器
            initMoleculeViewer('molecule-viewer-1');
            
            // 初始化图表
            const ctx = document.getElementById('convergence-chart').getContext('2d');
            const convergenceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['300 eV', '350 eV', '400 eV', '450 eV', '500 eV', '550 eV', '600 eV'],
                    datasets: [{
                        label: '总能量 (eV/atom)',
                        data: [-7.65, -7.72, -7.81, -7.89, -7.91, -7.915, -7.918],
                        borderColor: 'rgba(0, 86, 179, 0.8)',
                        backgroundColor: 'rgba(0, 86, 179, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#0056b3',
                        pointBorderColor: '#fff',
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'ZnO体系截断能与总能量收敛关系'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `总能量: ${context.raw} eV/atom`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: '总能量 (eV/atom)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2);
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '截断能'
                            }
                        }
                    }
                }
            });
            
            // 可视化按钮交互
            const vizBtns = document.querySelectorAll('.viz-btn');
            vizBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    vizBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 这里可以根据按钮类型切换不同的图表
                    // 简化示例，实际应用中应加载不同数据
                });
            });
            
            // 过滤按钮交互
            const filterBtns = document.querySelectorAll('[data-filter]');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 这里可以添加实际的过滤逻辑
                });
            });
        });
        
        // 3D分子结构查看器初始化
        function initMoleculeViewer(containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf8f9fa);
            
            const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.z = 5;
            
            const renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            container.appendChild(renderer.domElement);
            
            // 创建ZnO晶体结构模型
            const znoGroup = new THREE.Group();
            
            // 创建原子
            function createAtom(x, y, z, color, size = 0.3) {
                const geometry = new THREE.SphereGeometry(size, 32, 32);
                const material = new THREE.MeshPhongMaterial({ color: color, shininess: 30 });
                const atom = new THREE.Mesh(geometry, material);
                atom.position.set(x, y, z);
                return atom;
            }
            
            // 创建键
            function createBond(start, end, color, radius = 0.08) {
                const direction = new THREE.Vector3().subVectors(end, start);
                const length = direction.length();
                
                const geometry = new THREE.CylinderGeometry(radius, radius, length, 16);
                const material = new THREE.MeshPhongMaterial({ color: color });
                const bond = new THREE.Mesh(geometry, material);
                
                bond.position.copy(start);
                bond.position.add(direction.multiplyScalar(0.5));
                bond.lookAt(end);
                bond.rotateX(Math.PI / 2);
                
                return bond;
            }
            
            // 添加ZnO的原子和键
            // Zn原子为灰色，O原子为红色
            const znAtoms = [
                createAtom(0, 0, 0, 0x909090, 0.4),
                createAtom(1, 1, 0, 0x909090, 0.4),
                createAtom(0, 2, 1, 0x909090, 0.4),
                createAtom(1, 0, 1, 0x909090, 0.4)
            ];
            
            const oAtoms = [
                createAtom(0, 1, 0.5, 0xd32f2f, 0.35),
                createAtom(1, 0, 0.5, 0xd32f2f, 0.35),
                createAtom(0.5, 1.5, 0, 0xd32f2f, 0.35),
                createAtom(0.5, 0.5, 1, 0xd32f2f, 0.35)
            ];
            
            // 添加所有原子到组
            znAtoms.forEach(atom => znoGroup.add(atom));
            oAtoms.forEach(atom => znoGroup.add(atom));
            
            // 添加Zn-O键
            for (let i = 0; i < znAtoms.length; i++) {
                for (let j = 0; j < oAtoms.length; j++) {
                    const distance = znAtoms[i].position.distanceTo(oAtoms[j].position);
                    if (distance < 1.2) {
                        const bond = createBond(znAtoms[i].position, oAtoms[j].position, 0xcccccc);
                        znoGroup.add(bond);
                    }
                }
            }
            
            // 调整组的位置
            znoGroup.position.set(-0.5, -1, -0.5);
            scene.add(znoGroup);
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
            scene.add(ambientLight);
            
            const pointLight = new THREE.PointLight(0xffffff, 0.8);
            pointLight.position.set(5, 5, 5);
            scene.add(pointLight);
            
            // 动画
            function animate() {
                requestAnimationFrame(animate);
                
                znoGroup.rotation.y += 0.01;
                znoGroup.rotation.x += 0.005;
                
                renderer.render(scene, camera);
            }
            
            animate();
            
            // 处理窗口大小变化
            window.addEventListener('resize', () => {
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            });
            
            // 添加鼠标交互
            let isDragging = false;
            let previousMousePosition = {
                x: 0,
                y: 0
            };
            
            container.addEventListener('mousedown', (e) => {
                isDragging = true;
            });
            
            container.addEventListener('mousemove', (e) => {
                const deltaMove = {
                    x: e.offsetX - previousMousePosition.x,
                    y: e.offsetY - previousMousePosition.y
                };
                
                if (isDragging) {
                    znoGroup.rotation.y += deltaMove.x * 0.01;
                    znoGroup.rotation.x += deltaMove.y * 0.01;
                }
                
                previousMousePosition = {
                    x: e.offsetX,
                    y: e.offsetY
                };
            });
            
            container.addEventListener('mouseup', () => {
                isDragging = false;
            });
            
            container.addEventListener('mouseleave', () => {
                isDragging = false;
            });
            
            // 添加鼠标滚轮缩放
            container.addEventListener('wheel', (e) => {
                e.preventDefault();
                
                camera.position.z += e.deltaY * 0.01;
                camera.position.z = Math.max(2, Math.min(10, camera.position.z));
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前页面导航高亮
            setActiveNavItem();
            
            // 其他初始化代码...
        });
        
        // 设置当前页面导航高亮
        function setActiveNavItem() {
            // 获取当前页面URL
            const currentPage = window.location.pathname.split('/').pop();
            
            // 移除所有导航链接的active类
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 设置当前页面对应的导航链接为active
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href === currentPage || (currentPage === '' && href === 'index.html')) {
                    link.classList.add('active');
                }
            });
        }
    </script>
</body>
</html> 