<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源库 - Materials Studio 知识解答平台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #0056b3;
            --secondary-color: #004494;
            --light-bg: #f8f9fa;
            --member-color: #ffc107;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: #333;
        }
        .navbar-brand img {
            max-height: 40px;
        }
        .navbar {
            background-color: var(--primary-color);
        }
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.8);
        }
        .navbar-dark .navbar-nav .nav-link:hover {
            color: rgba(255,255,255,1);
        }
        .member-badge {
            background-color: var(--member-color);
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 5px;
        }
        .page-header {
            background: linear-gradient(135deg, #0056b3 0%, #3498db 100%);
            color: white;
            padding: 30px 0;
        }
        .file-card {
            border-radius: 10px;
            border: 1px solid #eaeaea;
            transition: box-shadow 0.3s;
            margin-bottom: 20px;
        }
        .file-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .file-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .file-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .file-type-xsd { color: #28a745; }
        .file-type-mdf { color: #fd7e14; }
        .file-type-perl { color: #6f42c1; }
        .file-type-pdf { color: #dc3545; }
        .member-content {
            background-color: #fff8e6;
            border: 1px dashed var(--member-color);
            border-radius: 5px;
            padding: 10px;
        }
        .category-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s;
        }
        .category-card:hover {
            transform: translateY(-5px);
        }
        .upload-area {
            border: 2px dashed #d1d3e2;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background-color: #f8f9fc;
            transition: all 0.3s;
            cursor: pointer;
        }
        .upload-area:hover {
            background-color: #e8f0fe;
            border-color: #4e73df;
        }
        footer {
            background-color: #343a40;
            color: white;
        }
        .footer-links a {
            color: rgba(255,255,255,.7);
            text-decoration: none;
        }
        .footer-links a:hover {
            color: white;
        }
        /* 导航栏当前页面指示样式 */
        .navbar-dark .navbar-nav .nav-link.active {
            color: rgba(255,255,255,1);
            background-color: rgba(255,255,255,0.2);
            font-weight: 600;
            position: relative;
        }
        .navbar-dark .navbar-nav .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: var(--member-color);
            border-radius: 3px;
        }
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.8);
            position: relative;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.3s;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="https://via.placeholder.com/150x40?text=MS+知识平台" alt="Materials Studio 知识解答平台">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="qa.html"><i class="fas fa-question-circle"></i> 问答中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="resources.html"><i class="fas fa-folder-open"></i> 资源库</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.html"><i class="fas fa-users"></i> 社区互动</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html"><i class="fas fa-crown"></i> 会员权益</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <button class="btn btn-outline-light me-2" data-bs-toggle="modal" data-bs-target="#loginModal">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </button>
                    <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#registerModal">
                        <i class="fas fa-user-plus"></i> 注册
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <header class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1>资源库</h1>
                    <p class="lead">分享和下载 Materials Studio 相关资源</p>
                </div>
                <div class="col-lg-6">
                    <div class="card border-0">
                        <div class="card-body">
                            <form>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-lg" placeholder="搜索资源...">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 资源分类 -->
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">资源分类</h2>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="card category-card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-file-code fa-3x text-primary"></i>
                            </div>
                            <h4>项目文件</h4>
                            <p>包含.xsd、.xtd、.mdf等项目文件</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">浏览文件</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card category-card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-sliders-h fa-3x text-success"></i>
                            </div>
                            <h4>参数模板</h4>
                            <p>优化的计算参数和配置模板</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">浏览模板</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card category-card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-book fa-3x text-info"></i>
                            </div>
                            <h4>教程文档</h4>
                            <p>详细的使用指南和案例研究</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">浏览教程</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card category-card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-code fa-3x text-warning"></i>
                            </div>
                            <h4>脚本工具</h4>
                            <p>Perl、Python等自动化脚本</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">浏览脚本</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 文件上传区域 -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 mx-auto text-center mb-5">
                    <h2>贡献资源</h2>
                    <p class="lead">分享您的Materials Studio资源，帮助社区成员提升研究效率</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="upload-area" onclick="document.getElementById('fileUpload').click()">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h4>点击或拖放文件到此处上传</h4>
                        <p class="text-muted">支持的文件格式: .xsd, .xtd, .mdf, .txt, .perl, .py, .pdf 等</p>
                        <input type="file" id="fileUpload" class="d-none" multiple>
                    </div>
                    <div class="text-center mt-3">
                        <button class="btn btn-primary" disabled>
                            <i class="fas fa-upload"></i> 上传文件
                        </button>
                        <p class="small text-muted mt-2">请先登录后再上传文件</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 热门资源 -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>热门资源</h2>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown">
                        排序方式
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                        <li><a class="dropdown-item" href="#">最新上传</a></li>
                        <li><a class="dropdown-item" href="#">下载量</a></li>
                        <li><a class="dropdown-item" href="#">评分</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="row">
                <!-- 文件1 -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card file-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="file-icon file-type-xsd">
                                    <i class="fas fa-file-code"></i>
                                </div>
                                <div>
                                    <span class="badge bg-primary">项目文件</span>
                                </div>
                            </div>
                            <h5 class="card-title mb-1">
                                <a href="#" class="text-decoration-none">石墨烯结构优化模型.xsd</a>
                            </h5>
                            <p class="file-meta mb-2">
                                <span><i class="fas fa-user"></i> 纳米材料研究员</span>
                                <span class="ms-3"><i class="fas fa-calendar"></i> 2023-10-18</span>
                                <span class="ms-3"><i class="fas fa-download"></i> 132次下载</span>
                            </p>
                            <p class="card-text">优化后的石墨烯结构模型，适用于CASTEP计算和分子动力学模拟。</p>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                                <div>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star-half-alt text-warning"></i>
                                    <span>(4.5)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件2 (会员专享) -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card file-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="file-icon file-type-perl">
                                    <i class="fas fa-file-code"></i>
                                </div>
                                <div>
                                    <span class="badge bg-warning text-dark">脚本工具</span>
                                    <span class="member-badge">会员</span>
                                </div>
                            </div>
                            <h5 class="card-title mb-1">
                                <a href="#" class="text-decoration-none">批量结构分析脚本.perl</a>
                            </h5>
                            <p class="file-meta mb-2">
                                <span><i class="fas fa-user"></i> 计算化学专家</span>
                                <span class="ms-3"><i class="fas fa-calendar"></i> 2023-10-15</span>
                                <span class="ms-3"><i class="fas fa-download"></i> 87次下载</span>
                            </p>
                            <p class="card-text">自动化分析多个结构文件，计算键长、键角并生成统计报告。高效处理大批量结构数据。</p>
                            <div class="member-content mt-2 mb-2">
                                <p class="mb-0"><i class="fas fa-lock"></i> 会员专享资源 <a href="membership.html" class="text-decoration-none">升级会员</a> 即可下载</p>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-warning" disabled>
                                    <i class="fas fa-crown"></i> 会员下载
                                </button>
                                <div>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <span>(5.0)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件3 -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card file-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div class="file-icon file-type-pdf">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div>
                                    <span class="badge bg-info">教程文档</span>
                                </div>
                            </div>
                            <h5 class="card-title mb-1">
                                <a href="#" class="text-decoration-none">CASTEP高级参数优化指南.pdf</a>
                            </h5>
                            <p class="file-meta mb-2">
                                <span><i class="fas fa-user"></i> MS资深用户</span>
                                <span class="ms-3"><i class="fas fa-calendar"></i> 2023-10-10</span>
                                <span class="ms-3"><i class="fas fa-download"></i> 215次下载</span>
                            </p>
                            <p class="card-text">详细介绍CASTEP模块中各计算参数的选择策略和优化方法，包含多个案例分析和最佳实践。</p>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                                <div>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="far fa-star text-warning"></i>
                                    <span>(4.0)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 加载更多按钮 -->
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary">
                    <i class="fas fa-sync"></i> 加载更多
                </button>
            </div>
        </div>
    </section>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="loginEmail" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="loginEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">记住我</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </form>
                    <div class="text-center mt-3">
                        <a href="#" class="text-decoration-none">忘记密码?</a>
                        <span class="mx-2">|</span>
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#registerModal" data-bs-dismiss="modal">注册新账号</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="registerName" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="registerName" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="userType" class="form-label">您的身份</label>
                            <select class="form-select" id="userType">
                                <option selected>请选择</option>
                                <option value="researcher">研究人员</option>
                                <option value="student">学生</option>
                                <option value="engineer">工程师</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">我同意<a href="#">服务条款</a>和<a href="#">隐私政策</a></label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                    <div class="text-center mt-3">
                        <span>已有账号?</span>
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#loginModal" data-bs-dismiss="modal">立即登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="py-4">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <h5>Materials Studio 知识解答平台</h5>
                    <p>专注于解答 Materials Studio 软件用户问题的在线服务网站。</p>
                    <div class="social-links">
                        <a href="#" class="me-2"><i class="fab fa-weixin fa-lg"></i></a>
                        <a href="#" class="me-2"><i class="fab fa-weibo fa-lg"></i></a>
                        <a href="#" class="me-2"><i class="fab fa-github fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 mb-4 mb-lg-0">
                    <h5>平台</h5>
                    <ul class="list-unstyled footer-links">
                        <li><a href="index.html">首页</a></li>
                        <li><a href="qa.html">问答中心</a></li>
                        <li><a href="resources.html">资源库</a></li>
                        <li><a href="community.html">社区互动</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4 mb-lg-0">
                    <h5>支持</h5>
                    <ul class="list-unstyled footer-links">
                        <li><a href="#">常见问题</a></li>
                        <li><a href="#">使用指南</a></li>
                        <li><a href="#">联系我们</a></li>
                        <li><a href="#">问题反馈</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h5>联系我们</h5>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-comments me-2"></i> 微信群：MS-Knowledge-Group</p>
                    <form class="mt-3">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="订阅我们的最新资讯">
                            <button class="btn btn-primary" type="button">订阅</button>
                        </div>
                    </form>
                </div>
            </div>
            <hr class="my-4 bg-light">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">© 2023 Materials Studio 知识解答平台 | 版权所有</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#">隐私政策</a></li>
                        <li class="list-inline-item"><a href="#">服务条款</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 文件上传交互脚本 -->
    <script>
        document.getElementById('fileUpload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.querySelector('.upload-area').style.borderColor = '#28a745';
                document.querySelector('.upload-area h4').innerText = `已选择 ${e.target.files.length} 个文件`;
            }
        });

        // 添加到现有的页面加载事件中或创建新的DOMContentLoaded事件
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前页面导航高亮
            setActiveNavItem();
            
            // 其他已有的初始化代码...
        });
        
        // 设置当前页面导航高亮
        function setActiveNavItem() {
            // 获取当前页面URL
            const currentPage = window.location.pathname.split('/').pop();
            
            // 移除所有导航链接的active类
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 设置当前页面对应的导航链接为active
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href === currentPage || (currentPage === '' && href === 'index.html')) {
                    link.classList.add('active');
                }
            });
        }
    </script>
</body>
</html> 