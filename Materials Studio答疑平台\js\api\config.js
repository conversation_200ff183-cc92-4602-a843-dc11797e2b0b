/**
 * config.js - 前端环境配置
 * 包含不同环境（开发、生产）下的API地址及其他配置
 */

// 当前环境，可以通过构建工具设置
// 默认为开发环境，部署时需修改为'production'
const CURRENT_ENV = 'development';

// 各环境配置
const ENV_CONFIG = {
    development: {
        API_BASE_URL: 'http://localhost:8000/api/v1',
        TIMEOUT: 10000, // 请求超时时间（毫秒）
        DEBUG: true
    },
    production: {
        API_BASE_URL: '/api/v1', // 生产环境使用相对路径，由服务器反向代理处理
        TIMEOUT: 30000,
        DEBUG: false
    }
};

// 导出当前环境的配置
const CONFIG = ENV_CONFIG[CURRENT_ENV];

// 导出为全局变量，方便其他JS文件使用
window.CONFIG = CONFIG; 