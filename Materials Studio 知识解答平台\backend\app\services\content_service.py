from typing import List, Optional

from sqlalchemy import func
from sqlalchemy.orm import Session, joinedload

from app.models.content import Resource, ResourceCategory, Glossary, Notification, SearchHistory
from app.schemas.content import (
    ResourceCreate, ResourceUpdate,
    ResourceCategoryCreate, ResourceCategoryUpdate,
    GlossaryCreate, GlossaryUpdate,
    NotificationCreate, NotificationUpdate,
    SearchHistoryCreate
)


# 资源分类相关操作
def get_category_by_id(db: Session, category_id: int) -> Optional[ResourceCategory]:
    """
    根据ID获取资源分类
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        
    Returns:
        资源分类对象，如果不存在则为None
    """
    return db.query(ResourceCategory).filter(ResourceCategory.id == category_id).first()


def get_category_by_name(db: Session, name: str) -> Optional[ResourceCategory]:
    """
    根据名称获取资源分类
    
    Args:
        db: 数据库会话
        name: 分类名称
        
    Returns:
        资源分类对象，如果不存在则为None
    """
    return db.query(ResourceCategory).filter(ResourceCategory.name == name).first()


def get_all_categories(db: Session) -> List[ResourceCategory]:
    """
    获取所有资源分类
    
    Args:
        db: 数据库会话
        
    Returns:
        资源分类列表
    """
    return db.query(ResourceCategory).order_by(ResourceCategory.name).all()


def create_category(db: Session, obj_in: ResourceCategoryCreate) -> ResourceCategory:
    """
    创建资源分类
    
    Args:
        db: 数据库会话
        obj_in: 资源分类创建模式
        
    Returns:
        创建的资源分类对象
    """
    db_obj = ResourceCategory(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_category(db: Session, *, db_obj: ResourceCategory, obj_in: ResourceCategoryUpdate) -> ResourceCategory:
    """
    更新资源分类
    
    Args:
        db: 数据库会话
        db_obj: 要更新的资源分类对象
        obj_in: 资源分类更新模式
        
    Returns:
        更新后的资源分类对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_category(db: Session, category_id: int) -> None:
    """
    删除资源分类
    
    Args:
        db: 数据库会话
        category_id: 分类ID
    """
    category = db.query(ResourceCategory).filter(ResourceCategory.id == category_id).first()
    if category:
        db.delete(category)
        db.commit()


# 资源相关操作
def get_resource_by_id(db: Session, resource_id: int) -> Optional[Resource]:
    """
    根据ID获取资源
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        
    Returns:
        资源对象，如果不存在则为None
    """
    return db.query(Resource).filter(Resource.id == resource_id).first()


def get_resource_with_details(db: Session, resource_id: int) -> Optional[Resource]:
    """
    获取带有详细信息的资源（包括上传者、分类等）
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        
    Returns:
        资源对象，如果不存在则为None
    """
    return db.query(Resource)\
        .options(
            joinedload(Resource.uploader),
            joinedload(Resource.categories)
        )\
        .filter(Resource.id == resource_id)\
        .first()


def get_resources(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    category_id: Optional[int] = None,
    uploader_id: Optional[int] = None,
    search: Optional[str] = None,
    only_premium: bool = False,
    file_type: Optional[str] = None
) -> List[Resource]:
    """
    获取资源列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        category_id: 分类ID过滤
        uploader_id: 上传者ID过滤
        search: 搜索关键词
        only_premium: 是否只返回会员内容
        file_type: 文件类型过滤
        
    Returns:
        资源列表
    """
    query = db.query(Resource)\
        .options(
            joinedload(Resource.uploader),
            joinedload(Resource.categories)
        )
    
    # 应用过滤条件
    if category_id:
        query = query.join(Resource.categories).filter(ResourceCategory.id == category_id)
    
    if uploader_id:
        query = query.filter(Resource.uploader_id == uploader_id)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(Resource.title.ilike(search_term) | Resource.description.ilike(search_term))
    
    if only_premium:
        query = query.filter(Resource.is_premium == True)
    
    if file_type:
        query = query.filter(Resource.file_type == file_type)
    
    # 按创建时间降序排序
    query = query.order_by(Resource.created_at.desc())
    
    return query.offset(skip).limit(limit).all()


def get_resources_count(
    db: Session,
    category_id: Optional[int] = None,
    uploader_id: Optional[int] = None,
    search: Optional[str] = None,
    only_premium: bool = False,
    file_type: Optional[str] = None
) -> int:
    """
    获取资源总数
    
    Args:
        db: 数据库会话
        category_id: 分类ID过滤
        uploader_id: 上传者ID过滤
        search: 搜索关键词
        only_premium: 是否只返回会员内容
        file_type: 文件类型过滤
        
    Returns:
        资源总数
    """
    query = db.query(func.count(Resource.id))
    
    # 应用过滤条件
    if category_id:
        query = query.join(Resource.categories).filter(ResourceCategory.id == category_id)
    
    if uploader_id:
        query = query.filter(Resource.uploader_id == uploader_id)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(Resource.title.ilike(search_term) | Resource.description.ilike(search_term))
    
    if only_premium:
        query = query.filter(Resource.is_premium == True)
    
    if file_type:
        query = query.filter(Resource.file_type == file_type)
    
    return query.scalar()


def create_resource(db: Session, obj_in: ResourceCreate) -> Resource:
    """
    创建资源
    
    Args:
        db: 数据库会话
        obj_in: 资源创建模式
        
    Returns:
        创建的资源对象
    """
    # 从输入数据中提取分类ID
    category_ids = obj_in.category_ids
    obj_data = obj_in.dict(exclude={"category_ids"})
    
    # 创建资源对象
    db_obj = Resource(**obj_data)
    
    # 添加分类
    if category_ids:
        categories = db.query(ResourceCategory).filter(ResourceCategory.id.in_(category_ids)).all()
        db_obj.categories = categories
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_resource(db: Session, *, db_obj: Resource, obj_in: ResourceUpdate) -> Resource:
    """
    更新资源
    
    Args:
        db: 数据库会话
        db_obj: 要更新的资源对象
        obj_in: 资源更新模式
        
    Returns:
        更新后的资源对象
    """
    update_data = obj_in.dict(exclude_unset=True, exclude={"category_ids"})
    
    # 更新基本字段
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    # 更新分类
    if obj_in.category_ids is not None:
        categories = db.query(ResourceCategory).filter(ResourceCategory.id.in_(obj_in.category_ids)).all()
        db_obj.categories = categories
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_resource(db: Session, resource_id: int) -> None:
    """
    删除资源
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
    """
    resource = db.query(Resource).filter(Resource.id == resource_id).first()
    if resource:
        db.delete(resource)
        db.commit()


def increment_download_count(db: Session, db_obj: Resource) -> Resource:
    """
    增加资源下载次数
    
    Args:
        db: 数据库会话
        db_obj: 资源对象
        
    Returns:
        更新后的资源对象
    """
    db_obj.download_count += 1
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


# 术语词典相关操作
def get_glossary_by_id(db: Session, glossary_id: int) -> Optional[Glossary]:
    """
    根据ID获取术语词典条目
    
    Args:
        db: 数据库会话
        glossary_id: 术语词典条目ID
        
    Returns:
        术语词典条目对象，如果不存在则为None
    """
    return db.query(Glossary).filter(Glossary.id == glossary_id).first()


def get_glossary_by_term(db: Session, term: str) -> Optional[Glossary]:
    """
    根据术语获取术语词典条目
    
    Args:
        db: 数据库会话
        term: 术语
        
    Returns:
        术语词典条目对象，如果不存在则为None
    """
    return db.query(Glossary).filter(Glossary.term == term).first()


def get_all_glossary(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    search: Optional[str] = None
) -> List[Glossary]:
    """
    获取术语词典条目列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        category: 术语分类过滤
        search: 搜索关键词
        
    Returns:
        术语词典条目列表
    """
    query = db.query(Glossary).options(joinedload(Glossary.creator))
    
    # 应用过滤条件
    if category:
        query = query.filter(Glossary.category == category)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(Glossary.term.ilike(search_term) | Glossary.definition.ilike(search_term))
    
    # 按术语字母顺序排序
    query = query.order_by(Glossary.term)
    
    return query.offset(skip).limit(limit).all()


def get_glossary_categories(db: Session) -> List[str]:
    """
    获取所有术语分类
    
    Args:
        db: 数据库会话
        
    Returns:
        术语分类列表
    """
    result = db.query(Glossary.category)\
        .filter(Glossary.category != None)\
        .distinct()\
        .all()
    return [r[0] for r in result if r[0]]


def create_glossary(db: Session, obj_in: GlossaryCreate) -> Glossary:
    """
    创建术语词典条目
    
    Args:
        db: 数据库会话
        obj_in: 术语词典条目创建模式
        
    Returns:
        创建的术语词典条目对象
    """
    db_obj = Glossary(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_glossary(db: Session, *, db_obj: Glossary, obj_in: GlossaryUpdate) -> Glossary:
    """
    更新术语词典条目
    
    Args:
        db: 数据库会话
        db_obj: 要更新的术语词典条目对象
        obj_in: 术语词典条目更新模式
        
    Returns:
        更新后的术语词典条目对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_glossary(db: Session, glossary_id: int) -> None:
    """
    删除术语词典条目
    
    Args:
        db: 数据库会话
        glossary_id: 术语词典条目ID
    """
    glossary = db.query(Glossary).filter(Glossary.id == glossary_id).first()
    if glossary:
        db.delete(glossary)
        db.commit()


# 通知相关操作
def get_notification_by_id(db: Session, notification_id: int) -> Optional[Notification]:
    """
    根据ID获取通知
    
    Args:
        db: 数据库会话
        notification_id: 通知ID
        
    Returns:
        通知对象，如果不存在则为None
    """
    return db.query(Notification).filter(Notification.id == notification_id).first()


def get_user_notifications(
    db: Session,
    user_id: int,
    skip: int = 0,
    limit: int = 10,
    only_unread: bool = False
) -> List[Notification]:
    """
    获取用户的通知列表
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        skip: 跳过的记录数
        limit: 返回的记录数
        only_unread: 是否只返回未读通知
        
    Returns:
        通知列表
    """
    query = db.query(Notification).filter(Notification.user_id == user_id)
    
    if only_unread:
        query = query.filter(Notification.is_read == False)
    
    # 按创建时间降序排序
    query = query.order_by(Notification.created_at.desc())
    
    return query.offset(skip).limit(limit).all()


def get_unread_notifications_count(db: Session, user_id: int) -> int:
    """
    获取用户的未读通知数量
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        未读通知数量
    """
    return db.query(func.count(Notification.id))\
        .filter(Notification.user_id == user_id, Notification.is_read == False)\
        .scalar()


def create_notification(db: Session, obj_in: NotificationCreate) -> Notification:
    """
    创建通知
    
    Args:
        db: 数据库会话
        obj_in: 通知创建模式
        
    Returns:
        创建的通知对象
    """
    db_obj = Notification(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_notification(db: Session, *, db_obj: Notification, obj_in: NotificationUpdate) -> Notification:
    """
    更新通知
    
    Args:
        db: 数据库会话
        db_obj: 要更新的通知对象
        obj_in: 通知更新模式
        
    Returns:
        更新后的通知对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def mark_notification_as_read(db: Session, notification_id: int) -> Optional[Notification]:
    """
    将通知标记为已读
    
    Args:
        db: 数据库会话
        notification_id: 通知ID
        
    Returns:
        更新后的通知对象，如果不存在则为None
    """
    notification = get_notification_by_id(db, notification_id)
    if notification:
        notification.is_read = True
        db.add(notification)
        db.commit()
        db.refresh(notification)
    return notification


def mark_all_notifications_as_read(db: Session, user_id: int) -> int:
    """
    将用户的所有通知标记为已读
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        更新的通知数量
    """
    result = db.query(Notification)\
        .filter(Notification.user_id == user_id, Notification.is_read == False)\
        .update({"is_read": True})
    db.commit()
    return result


def delete_notification(db: Session, notification_id: int) -> None:
    """
    删除通知
    
    Args:
        db: 数据库会话
        notification_id: 通知ID
    """
    notification = db.query(Notification).filter(Notification.id == notification_id).first()
    if notification:
        db.delete(notification)
        db.commit()


# 搜索历史相关操作
def create_search_history(db: Session, obj_in: SearchHistoryCreate) -> SearchHistory:
    """
    创建搜索历史
    
    Args:
        db: 数据库会话
        obj_in: 搜索历史创建模式
        
    Returns:
        创建的搜索历史对象
    """
    db_obj = SearchHistory(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_user_search_history(
    db: Session,
    user_id: int,
    limit: int = 10
) -> List[SearchHistory]:
    """
    获取用户的搜索历史
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        limit: 返回的记录数
        
    Returns:
        搜索历史列表
    """
    return db.query(SearchHistory)\
        .filter(SearchHistory.user_id == user_id)\
        .order_by(SearchHistory.created_at.desc())\
        .limit(limit)\
        .all()


def delete_search_history(db: Session, history_id: int) -> None:
    """
    删除搜索历史
    
    Args:
        db: 数据库会话
        history_id: 搜索历史ID
    """
    history = db.query(SearchHistory).filter(SearchHistory.id == history_id).first()
    if history:
        db.delete(history)
        db.commit()


def clear_user_search_history(db: Session, user_id: int) -> int:
    """
    清空用户的搜索历史
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        删除的记录数
    """
    result = db.query(SearchHistory)\
        .filter(SearchHistory.user_id == user_id)\
        .delete()
    db.commit()
    return result 