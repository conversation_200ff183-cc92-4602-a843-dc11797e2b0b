# Materials Studio 答疑平台后端

本项目是 Materials Studio 答疑平台的后端服务，基于 Spring Boot 框架开发，提供问答平台核心功能的 API 接口。

## 项目结构

- `src/main/java`: Java 源代码
  - `com.materialsstudio.qa`: 根包
    - `controller`: 控制器层，处理 HTTP 请求
    - `service`: 服务层，处理业务逻辑
    - `dao`: 数据访问层，与数据库交互
    - `entity`: 实体类，对应数据库表
    - `model`: 模型类，包括 DTO 和 VO
    - `config`: 配置类
    - `common`: 公共类
    - `utils`: 工具类
- `src/main/resources`: 资源文件
  - `application.yml`: 应用程序配置
  - `mapper`: MyBatis 映射文件
  - `schema.sql`: 数据库初始化脚本
- `src/test`: 测试代码

## 技术栈

- Spring Boot 2.7.12
- Spring Security
- MyBatis-Plus *******
- MySQL 8
- Redis
- JWT

## 功能特性

- 用户认证与授权
- 问题管理
- 回答管理
- 标签管理
- 会员管理
- 点赞与收藏
- 评论管理

## 开发环境配置

1. 安装 JDK 8+
2. 安装 Maven 3.6+
3. 安装 MySQL 8+
4. 安装 Redis

## 编译与启动

1. 克隆项目

```bash
git clone <project-url>
cd java-backend
```

2. 编译项目

```bash
mvn clean package -DskipTests
```

3. 配置数据库

创建名为 `materials_studio_qa` 的数据库，然后导入 `src/main/resources/schema.sql` 初始化数据库。

4. 修改配置

根据实际情况修改 `src/main/resources/application.yml` 中的配置，尤其是数据库和 Redis 的连接信息。

5. 启动应用

```bash
java -jar target/qa-platform-0.0.1-SNAPSHOT.jar
```

或者使用 Maven 插件启动：

```bash
mvn spring-boot:run
```

应用启动后，API 接口将在 `http://localhost:8080/api/v1` 下可用。

## 生产环境部署

对于生产环境部署，请执行以下步骤：

1. 使用生产环境配置编译项目

```bash
mvn clean package -DskipTests -Pprod
```

2. 设置环境变量

在生产服务器上设置以下环境变量：

```bash
export MYSQL_USERNAME=生产环境用户名
export MYSQL_PASSWORD=生产环境密码
export REDIS_HOST=生产环境Redis主机
export REDIS_PORT=6379
export REDIS_PASSWORD=生产环境Redis密码
export JWT_SECRET=生成一个安全的随机密钥
export FILE_UPLOAD_DIR=/app/uploads/
```

3. 启动应用

```bash
java -Dspring.profiles.active=prod -jar qa-platform-0.0.1-SNAPSHOT.jar
```

4. 使用守护进程（如systemd）管理应用生命周期

创建systemd服务文件 `/etc/systemd/system/msqa.service`：

```
[Unit]
Description=Materials Studio QA Platform
After=network.target mysql.service redis.service

[Service]
User=msqa
Group=msqa
Environment="MYSQL_USERNAME=生产环境用户名"
Environment="MYSQL_PASSWORD=生产环境密码"
Environment="REDIS_HOST=生产环境Redis主机"
Environment="REDIS_PORT=6379"
Environment="REDIS_PASSWORD=生产环境Redis密码"
Environment="JWT_SECRET=生成一个安全的随机密钥"
Environment="FILE_UPLOAD_DIR=/app/uploads/"
WorkingDirectory=/app/materials-studio-qa
ExecStart=/usr/bin/java -Dspring.profiles.active=prod -jar /app/materials-studio-qa/qa-platform-0.0.1-SNAPSHOT.jar
SuccessExitStatus=143
TimeoutStopSec=10
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动并设置开机自启：

```bash
sudo systemctl daemon-reload
sudo systemctl start msqa
sudo systemctl enable msqa
```

详细的部署指南请参考项目根目录的 `部署指南.md` 文件。

## API 文档

主要 API 接口：

1. 用户认证
   - POST `/auth/login`: 用户登录
   - GET `/auth/me`: 获取当前用户信息

2. 问题管理
   - GET `/questions`: 获取问题列表
   - GET `/questions/{id}`: 获取问题详情
   - POST `/questions`: 发布问题

3. 回答管理
   - GET `/answers/question/{questionId}`: 获取问题的回答列表
   - POST `/answers`: 发布回答
   - PUT `/answers/{id}/accept`: 采纳回答
   - PUT `/answers/{id}/vote`: 点赞回答

4. 标签管理
   - GET `/tags`: 获取所有标签
   - GET `/tags/hot`: 获取热门标签
   - GET `/tags/question/{questionId}`: 获取问题的标签

## 默认账户

系统初始化后默认提供以下账户用于测试：

- 管理员：账号 `admin`，密码 `123456`
- 会员用户：账号 `member`，密码 `123456`
- 普通用户：账号 `user`，密码 `123456` 