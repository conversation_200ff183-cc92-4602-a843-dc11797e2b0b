from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field


# 用户基础模式
class UserBase(BaseModel):
    """用户基础模式"""
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    is_active: Optional[bool] = True
    is_superuser: bool = False


# 创建用户请求模式
class UserCreate(UserBase):
    """创建用户请求模式"""
    email: EmailStr
    username: str
    password: str


# 更新用户请求模式
class UserUpdate(UserBase):
    """更新用户请求模式"""
    password: Optional[str] = None


# 数据库中的用户模式
class UserInDBBase(UserBase):
    """数据库中的用户模式基类"""
    id: Optional[int] = None
    created_at: datetime
    
    class Config:
        orm_mode = True


# 返回给API的用户模式
class User(UserInDBBase):
    """返回给API的用户模式"""
    pass


# 存储在数据库中的用户模式（包含哈希密码）
class UserInDB(UserInDBBase):
    """存储在数据库中的用户模式"""
    hashed_password: str


# 用户个人资料基础模式
class UserProfileBase(BaseModel):
    """用户个人资料基础模式"""
    full_name: Optional[str] = None
    avatar: Optional[str] = None
    bio: Optional[str] = None
    organization: Optional[str] = None
    position: Optional[str] = None
    website: Optional[str] = None
    social_link: Optional[str] = None


# 创建用户个人资料请求模式
class UserProfileCreate(UserProfileBase):
    """创建用户个人资料请求模式"""
    user_id: int


# 更新用户个人资料请求模式
class UserProfileUpdate(UserProfileBase):
    """更新用户个人资料请求模式"""
    pass


# 数据库中的用户个人资料模式
class UserProfileInDBBase(UserProfileBase):
    """数据库中的用户个人资料模式基类"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 返回给API的用户个人资料模式
class UserProfile(UserProfileInDBBase):
    """返回给API的用户个人资料模式"""
    pass


# 会员信息基础模式
class MembershipBase(BaseModel):
    """会员信息基础模式"""
    is_premium: bool = False
    level: int = 0  # 会员等级：0-免费用户, 1-初级会员, 2-高级会员, 3-专业会员
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


# 创建会员信息请求模式
class MembershipCreate(MembershipBase):
    """创建会员信息请求模式"""
    user_id: int


# 更新会员信息请求模式
class MembershipUpdate(MembershipBase):
    """更新会员信息请求模式"""
    pass


# 数据库中的会员信息模式
class MembershipInDBBase(MembershipBase):
    """数据库中的会员信息模式基类"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 返回给API的会员信息模式
class Membership(MembershipInDBBase):
    """返回给API的会员信息模式"""
    pass


# 用户认证模式
class Token(BaseModel):
    access_token: str
    token_type: str


class TokenPayload(BaseModel):
    sub: Optional[int] = None 