{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "files": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:/aib<PERSON><PERSON><PERSON>"]}, "@wopal-mcp-server-hotnews": {"command": "npx", "args": ["-y", "@wopal/mcp-server-hotnews"]}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}}}