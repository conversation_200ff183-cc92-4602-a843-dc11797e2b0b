const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const config = require('./config');
const logger = require('./utils/logger');
const database = require('./database/connection');
const redis = require('./utils/redis');
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const questionRoutes = require('./routes/questions');
const answerRoutes = require('./routes/answers');
const categoryRoutes = require('./routes/categories');
const tagRoutes = require('./routes/tags');
const resourceRoutes = require('./routes/resources');
const membershipRoutes = require('./routes/memberships');
const orderRoutes = require('./routes/orders');
const adminRoutes = require('./routes/admin');
const uploadRoutes = require('./routes/upload');
const notificationRoutes = require('./routes/notifications');

const app = express();
const server = createServer(app);

// Socket.IO配置
const io = new Server(server, {
  cors: {
    origin: config.cors.origin,
    credentials: config.cors.credentials
  }
});

// 全局中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
}));

app.use(cors({
  origin: config.cors.origin,
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 日志中间件
if (config.app.env !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// 速率限制
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMaxRequests,
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const speedLimiter = slowDown({
  windowMs: config.security.slowDownWindowMs,
  delayAfter: config.security.slowDownDelayAfter,
  delayMs: config.security.slowDownDelayMs
});

app.use('/api/', limiter);
app.use('/api/', speedLimiter);

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.app.env,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/questions', questionRoutes);
app.use('/api/answers', answerRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/tags', tagRoutes);
app.use('/api/resources', resourceRoutes);
app.use('/api/memberships', membershipRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/notifications', notificationRoutes);

// Socket.IO事件处理
io.on('connection', (socket) => {
  logger.info(`用户连接: ${socket.id}`);

  // 用户加入房间
  socket.on('join', (data) => {
    if (data.userId) {
      socket.join(`user_${data.userId}`);
      logger.info(`用户 ${data.userId} 加入房间`);
    }
  });

  // 用户离开
  socket.on('disconnect', () => {
    logger.info(`用户断开连接: ${socket.id}`);
  });
});

// 将io实例添加到app中，供其他模块使用
app.set('io', io);

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

// 启动服务器
const PORT = config.app.port || 8000;

async function startServer() {
  try {
    // 测试数据库连接
    await database.testConnection();
    logger.info('数据库连接成功');

    // 测试Redis连接
    await redis.ping();
    logger.info('Redis连接成功');

    // 启动服务器
    server.listen(PORT, () => {
      logger.info(`服务器启动成功，端口: ${PORT}`);
      logger.info(`环境: ${config.app.env}`);
      logger.info(`API文档: http://localhost:${PORT}/api/docs`);
    });
  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  
  server.close(async () => {
    logger.info('HTTP服务器已关闭');
    
    try {
      await database.close();
      logger.info('数据库连接已关闭');
      
      await redis.quit();
      logger.info('Redis连接已关闭');
      
      process.exit(0);
    } catch (error) {
      logger.error('关闭过程中出现错误:', error);
      process.exit(1);
    }
  });
});

process.on('SIGINT', async () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  
  server.close(async () => {
    logger.info('HTTP服务器已关闭');
    
    try {
      await database.close();
      logger.info('数据库连接已关闭');
      
      await redis.quit();
      logger.info('Redis连接已关闭');
      
      process.exit(0);
    } catch (error) {
      logger.error('关闭过程中出现错误:', error);
      process.exit(1);
    }
  });
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

if (require.main === module) {
  startServer();
}

module.exports = app;
