/**
 * auth.js - 用户认证相关的API交互
 * 处理登录、注册和用户信息获取功能
 */

// API基础URL，从配置文件获取
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:8000/api/v1';

/**
 * 用户登录
 * @param {string} username - 用户名或邮箱
 * @param {string} password - 密码
 * @returns {Promise} - 包含登录结果的Promise
 */
async function login(username, password) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'username': username,
                'password': password
            })
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || '登录失败，请检查用户名和密码');
        }
        
        // 保存token到localStorage
        localStorage.setItem('token', data.access_token);
        localStorage.setItem('token_type', data.token_type);
        
        return data;
    } catch (error) {
        console.error('登录错误:', error);
        throw error;
    }
}

/**
 * 用户注册
 * @param {Object} userData - 用户注册数据
 * @param {string} userData.username - 用户名
 * @param {string} userData.email - 邮箱
 * @param {string} userData.password - 密码
 * @param {string} userData.phone - 电话号码(可选)
 * @returns {Promise} - 包含注册结果的Promise
 */
async function register(userData) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData)
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || '注册失败，请检查输入信息');
        }
        
        return data;
    } catch (error) {
        console.error('注册错误:', error);
        throw error;
    }
}

/**
 * 获取当前用户信息
 * @returns {Promise} - 包含用户信息的Promise
 */
async function getCurrentUser() {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();
        
        if (!response.ok) {
            if (response.status === 401) {
                // 如果是未授权，清除本地存储的token
                localStorage.removeItem('token');
                localStorage.removeItem('token_type');
            }
            throw new Error(data.detail || '获取用户信息失败');
        }
        
        return data;
    } catch (error) {
        console.error('获取用户信息错误:', error);
        throw error;
    }
}

/**
 * 退出登录
 */
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('token_type');
    
    // 可以在这里添加其他需要在退出登录时执行的操作
    // 例如重定向到登录页面
    window.location.href = '/login.html';
}

/**
 * 检查用户是否已登录
 * @returns {boolean} - 是否已登录
 */
function isLoggedIn() {
    return !!localStorage.getItem('token');
}

// 导出API函数
window.authAPI = {
    login,
    register,
    getCurrentUser,
    logout,
    isLoggedIn
};
