package com.materialsstudio.qa.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 问题视图对象
 */
@Data
public class QuestionVO {
    
    /**
     * 问题ID
     */
    private Long id;
    
    /**
     * 问题标题
     */
    private String title;
    
    /**
     * 问题内容
     */
    private String content;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 作者用户名
     */
    private String authorName;
    
    /**
     * 作者头像
     */
    private String authorAvatar;
    
    /**
     * 是否为会员专属
     */
    private Boolean isPremium;
    
    /**
     * 查看次数
     */
    private Integer viewCount;
    
    /**
     * 回答数量
     */
    private Integer answerCount;
    
    /**
     * 状态：0-未解决，1-已解决
     */
    private Integer status;
    
    /**
     * 标签列表
     */
    private List<TagVO> tags;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 