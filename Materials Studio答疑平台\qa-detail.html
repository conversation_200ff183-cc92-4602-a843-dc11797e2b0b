<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题详情 - Materials Studio 答疑平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <!-- Markdown编辑器 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
    
    <!-- 科技感效果库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #0d47a1;
            --accent-color: #03a9f4;
            --light-bg: #f5f5f5;
            --member-color: #ffc107;
            --success-color: #4caf50;
            --info-color: #03a9f4;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --dark-color: #212121;
            --text-color: #212121;
            --light-text: #ffffff;
            --border-radius: 8px;
            --box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 4px 8px rgba(0,0,0,0.06);
            --transition-speed: 0.3s;
        }
        
        body {
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        /* 导航栏 */
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.85);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all var(--transition-speed);
        }
        
        .navbar-dark .navbar-nav .nav-link:hover,
        .navbar-dark .navbar-nav .nav-link.active {
            color: rgba(255,255,255,1);
            background-color: rgba(255,255,255,0.2);
        }
        
        .member-badge {
            background-color: var(--member-color);
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        /* 页面标题区域 */
        .page-title-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px 0;
            position: relative;
        }
        
        .page-title-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://images.unsplash.com/photo-1557425529-b1ae9c141e7a?ixid=MnwxMjA3fDB8MHxzZWFyY2h8MXx8bW9sZWN1bGUlMjBzdHJ1Y3R1cmV8ZW58MHx8MHx8&auto=format&fit=crop&w=1200&q=60') center/cover no-repeat;
            opacity: 0.1;
            z-index: 0;
        }
        
        .page-title-section .container {
            position: relative;
            z-index: 1;
        }
        
        /* 问题详情 */
        .question-detail-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .question-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            background-color: rgba(0,0,0,0.02);
        }
        
        .question-body {
            padding: 25px;
        }
        
        .question-footer {
            padding: 15px 20px;
            background-color: rgba(0,0,0,0.02);
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .question-title {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .question-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            font-size: 0.95rem;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .question-tags {
            margin-top: 20px;
        }
        
        .question-tag {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            margin-right: 5px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }
        
        .tag-method {
            background-color: rgba(52, 152, 219, 0.15);
            color: #2980b9;
        }
        
        .tag-parameter {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }
        
        .tag-error {
            background-color: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }
        
        .tag-analysis {
            background-color: rgba(155, 89, 182, 0.15);
            color: #8e44ad;
        }
        
        /* 作者信息 */
        .author-info {
            display: flex;
            align-items: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        .author-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .author-role {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        /* 回答列表 */
        .answers-section {
            margin-top: 30px;
        }
        
        .answer-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .answer-header {
            padding: 15px 20px;
            background-color: rgba(0,0,0,0.02);
            border-bottom: 1px solid rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .answer-body {
            padding: 20px;
        }
        
        .answer-footer {
            padding: 15px 20px;
            background-color: rgba(0,0,0,0.02);
            border-top: 1px solid rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .best-answer-badge {
            background-color: var(--success-color);
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
        }
        
        /* 会员内容 */
        .member-content {
            background-color: #fff8e6;
            border: 1px dashed var(--member-color);
            border-radius: var(--border-radius);
            padding: 15px;
            margin: 15px 0;
            position: relative;
        }
        
        .member-content-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: var(--border-radius);
            z-index: 10;
        }
        
        .member-content-message {
            text-align: center;
            margin-bottom: 15px;
        }
        
        /* 评论 */
        .comments-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .comment {
            padding: 10px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .comment-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .comment-author {
            font-weight: 600;
        }
        
        /* 提交回答 */
        .submit-answer-section {
            margin-top: 30px;
        }
        
        /* 视频/图片展示 */
        .media-container {
            margin: 20px 0;
        }
        
        .media-container img {
            max-width: 100%;
            border-radius: var(--border-radius);
        }
        
        .media-container video {
            width: 100%;
            border-radius: var(--border-radius);
        }
        
        /* 代码块 */
        pre {
            background-color: #282c34;
            border-radius: var(--border-radius);
            padding: 15px;
            margin: 15px 0;
            overflow: auto;
        }
        
        code {
            font-family: 'Source Code Pro', monospace;
            color: #abb2bf;
        }
        
        /* 操作按钮 */
        .action-btn {
            color: #6c757d;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 10px;
        }
        
        .action-btn:hover {
            color: var(--primary-color);
        }
        
        .action-btn.active {
            color: var(--primary-color);
        }
        
        /* 侧边栏 */
        .sidebar-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        /* 相关问题 */
        .related-question {
            padding: 10px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .related-question:last-child {
            border-bottom: none;
        }
        
        .related-question-title {
            font-size: 1rem;
            margin-bottom: 5px;
        }
        
        .related-question-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        /* 页脚 */
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
            margin-top: 50px;
        }
        
        .footer-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
        }
        
        /* Markdown 编辑器自定义样式 */
        .CodeMirror {
            border-radius: var(--border-radius);
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .editor-toolbar {
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            border: 1px solid rgba(0,0,0,0.1);
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">Materials Studio 答疑平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="qa.html"><i class="fas fa-question-circle"></i> 问答</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html"><i class="fas fa-book"></i> 学习资源</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.html"><i class="fas fa-users"></i> 社区</a>
                    </li>
                </ul>
                <ul class="navbar-nav" id="user-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light" href="register.html"><i class="fas fa-user-plus"></i> 注册</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 页面标题区域 -->
    <section class="page-title-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-2">
                            <li class="breadcrumb-item"><a href="index.html" class="text-white">首页</a></li>
                            <li class="breadcrumb-item"><a href="qa.html" class="text-white">问答</a></li>
                            <li class="breadcrumb-item active text-white-50" aria-current="page">问题详情</li>
                        </ol>
                    </nav>
                    <h1 class="question-title-text mb-0">加载中...</h1>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要内容 -->
    <div class="container py-5">
        <div class="row">
            <!-- 问题详情 -->
            <div class="col-lg-8">
                <div id="question-detail" class="question-detail-card">
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-3">正在加载问题详情...</p>
                    </div>
                </div>
                
                <!-- 回答部分 -->
                <div class="answers-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 id="answers-count">回答 (0)</h3>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortAnswersDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                排序方式
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="sortAnswersDropdown">
                                <li><a class="dropdown-item active" href="#" data-sort="votes">按投票数</a></li>
                                <li><a class="dropdown-item" href="#" data-sort="newest">最新回答</a></li>
                                <li><a class="dropdown-item" href="#" data-sort="oldest">最早回答</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div id="answers-container">
                        <!-- 回答将通过JavaScript动态添加 -->
                    </div>
                </div>
                
                <!-- 提交回答 -->
                <div class="submit-answer-section">
                    <h3 class="mb-4">提交您的回答</h3>
                    <div id="submit-answer-login-message" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> 请先<a href="login.html" class="alert-link">登录</a>后再提交回答
                        </div>
                    </div>
                    <div id="submit-answer-form" style="display: none;">
                        <div class="mb-3">
                            <textarea id="answer-content" class="form-control" rows="6" placeholder="请输入您的回答..."></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is-premium-answer">
                            <label class="form-check-label" for="is-premium-answer">设为会员专享内容</label>
                        </div>
                        <button id="submit-answer-btn" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>提交回答
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 侧边栏 -->
            <div class="col-lg-4">
                <!-- 问题状态 -->
                <div class="sidebar-card">
                    <h5 class="sidebar-title">问题状态</h5>
                    <div class="d-flex justify-content-between mb-2">
                        <span>提问时间:</span>
                        <span id="question-date">-</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>浏览次数:</span>
                        <span id="question-views">-</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>回答数量:</span>
                        <span id="question-answer-count">-</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>状态:</span>
                        <span id="question-status">-</span>
                    </div>
                    <div class="mt-3">
                        <button id="favorite-btn" class="btn btn-outline-primary w-100 mb-2">
                            <i class="far fa-star me-2"></i>收藏问题
                        </button>
                        <button id="share-btn" class="btn btn-outline-info w-100">
                            <i class="fas fa-share-alt me-2"></i>分享问题
                        </button>
                    </div>
                </div>
                
                <!-- 作者信息 -->
                <div class="sidebar-card">
                    <h5 class="sidebar-title">提问者</h5>
                    <div class="d-flex align-items-center">
                        <div class="author-avatar me-3" id="author-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h6 class="mb-1" id="author-name">-</h6>
                            <p class="text-muted small mb-0" id="author-role">-</p>
                        </div>
                    </div>
                    <div class="mt-3 small">
                        <div class="d-flex justify-content-between mb-1">
                            <span>提问数:</span>
                            <span id="author-questions">-</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>回答数:</span>
                            <span id="author-answers">-</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>注册时间:</span>
                            <span id="author-joined">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- 相关问题 -->
                <div class="sidebar-card">
                    <h5 class="sidebar-title">相关问题</h5>
                    <div id="related-questions">
                        <p class="text-center text-muted">加载中...</p>
                    </div>
                </div>
                
                <!-- 热门标签 -->
                <div class="sidebar-card">
                    <h5 class="sidebar-title">热门标签</h5>
                    <div class="d-flex flex-wrap gap-2" id="popular-tags">
                        <span class="placeholder-glow">
                            <span class="placeholder col-4"></span>
                            <span class="placeholder col-3"></span>
                            <span class="placeholder col-5"></span>
                            <span class="placeholder col-4"></span>
                            <span class="placeholder col-6"></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h4 class="footer-title">Materials Studio 答疑平台</h4>
                    <p>专注于为材料科学研究者提供专业的 Materials Studio 软件使用指导、问题解答和资源共享。</p>
                    <div class="social-links mt-3">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">快速链接</h5>
                    <div class="footer-links">
                        <a href="index.html">首页</a>
                        <a href="qa.html">问答</a>
                        <a href="resources.html">资源</a>
                        <a href="community.html">社区</a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">帮助</h5>
                    <div class="footer-links">
                        <a href="#">常见问题</a>
                        <a href="#">使用指南</a>
                        <a href="#">联系我们</a>
                        <a href="#">意见反馈</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5 class="footer-title">联系我们</h5>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-phone me-2"></i> ************</p>
                    <p><i class="fas fa-map-marker-alt me-2"></i> 北京市海淀区中关村科技园</p>
                </div>
            </div>
            <hr class="mt-4 mb-3 bg-light">
            <div class="text-center py-3">
                <p class="mb-0">&copy; 2023 Materials Studio 答疑平台. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
    
    <!-- API请求脚本 -->
    <script src="js/api/config.js"></script>
    <script src="js/api/utils.js"></script>
    <script src="js/api/auth.js"></script>
    <script src="js/api/qa.js"></script>
    <script src="js/api/user.js"></script>
    <script src="js/api/content.js"></script>
    <!-- 代码高亮初始化 -->
    <script>hljs.highlightAll();</script>
    <script src="js/api/qa-detail.js"></script>
</body>
</html>
