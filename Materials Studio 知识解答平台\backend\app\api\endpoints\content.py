from typing import Any, List, Optional
import os
import shutil
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.core.deps import get_current_active_user, get_current_active_superuser, get_db, check_premium_content_access
from app.config.settings import settings
from app.models.user import User
from app.schemas.content import (
    Resource, ResourceCreate, ResourceUpdate, ResourceDetail,
    ResourceCategory, ResourceCategoryCreate, ResourceCategoryUpdate,
    Glossary, GlossaryCreate, GlossaryUpdate,
    Notification, NotificationCreate, NotificationUpdate,
    Favorite
)
from app.services import content_service

router = APIRouter()


# 资源分类相关路由
@router.get("/categories/", response_model=List[ResourceCategory])
def get_categories(
    db: Session = Depends(get_db),
):
    """
    获取所有资源分类
    
    Args:
        db: 数据库会话
        
    Returns:
        分类列表
    """
    categories = content_service.get_all_categories(db=db)
    return categories


@router.post("/categories/", response_model=ResourceCategory)
def create_category(
    *,
    db: Session = Depends(get_db),
    category_in: ResourceCategoryCreate,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    创建资源分类（仅管理员）
    
    Args:
        db: 数据库会话
        category_in: 分类创建模式
        current_user: 当前用户（必须是管理员）
        
    Returns:
        创建的分类
    """
    # 检查分类是否已存在
    existing_category = content_service.get_category_by_name(db=db, name=category_in.name)
    if existing_category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="分类已存在"
        )
    
    # 创建分类
    category = content_service.create_category(db=db, obj_in=category_in)
    
    return category


@router.put("/categories/{category_id}", response_model=ResourceCategory)
def update_category(
    *,
    db: Session = Depends(get_db),
    category_id: int,
    category_in: ResourceCategoryUpdate,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    更新资源分类（仅管理员）
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        category_in: 分类更新模式
        current_user: 当前用户（必须是管理员）
        
    Returns:
        更新后的分类
    """
    # 检查分类是否存在
    category = content_service.get_category_by_id(db=db, category_id=category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分类不存在"
        )
    
    # 如果更新名称，检查新名称是否已存在
    if category_in.name and category_in.name != category.name:
        existing_category = content_service.get_category_by_name(db=db, name=category_in.name)
        if existing_category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="分类名称已存在"
            )
    
    # 更新分类
    category = content_service.update_category(db=db, db_obj=category, obj_in=category_in)
    
    return category


@router.delete("/categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_category(
    *,
    db: Session = Depends(get_db),
    category_id: int,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    删除资源分类（仅管理员）
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        current_user: 当前用户（必须是管理员）
    """
    # 检查分类是否存在
    category = content_service.get_category_by_id(db=db, category_id=category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分类不存在"
        )
    
    # 删除分类
    content_service.delete_category(db=db, category_id=category_id)


# 资源相关路由
@router.get("/resources/", response_model=List[Resource])
def get_resources(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10,
    category_id: Optional[int] = None,
    uploader_id: Optional[int] = None,
    search: Optional[str] = None,
    only_premium: bool = False,
    file_type: Optional[str] = None,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取资源列表
    
    可以根据分类、上传者、搜索关键词、文件类型进行过滤
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        category_id: 分类ID过滤
        uploader_id: 上传者ID过滤
        search: 搜索关键词
        only_premium: 是否只返回会员内容
        file_type: 文件类型过滤
        current_user: 当前用户
        
    Returns:
        资源列表
    """
    if only_premium:
        # 检查用户是否有权限访问会员内容
        check_premium_content_access(current_user, is_premium_content=True)
    
    resources = content_service.get_resources(
        db=db, 
        skip=skip, 
        limit=limit,
        category_id=category_id,
        uploader_id=uploader_id,
        search=search,
        only_premium=only_premium,
        file_type=file_type
    )
    
    total = content_service.get_resources_count(
        db=db,
        category_id=category_id,
        uploader_id=uploader_id,
        search=search,
        only_premium=only_premium,
        file_type=file_type
    )
    
    return resources


@router.post("/resources/", response_model=Resource)
async def create_resource(
    *,
    db: Session = Depends(get_db),
    title: str = Form(...),
    description: Optional[str] = Form(None),
    category_id: Optional[int] = Form(None),
    is_premium: bool = Form(False),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
):
    """
    上传新资源
    
    Args:
        db: 数据库会话
        title: 资源标题
        description: 资源描述
        category_id: 分类ID
        is_premium: 是否为会员内容
        file: 上传的文件
        current_user: 当前用户
        
    Returns:
        创建的资源
    """
    # 检查分类是否存在
    if category_id:
        category = content_service.get_category_by_id(db=db, category_id=category_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="分类不存在"
            )
    
    # 创建资源目录
    os.makedirs(os.path.join(settings.UPLOAD_FOLDER, "resources"), exist_ok=True)
    
    # 保存文件
    file_path = os.path.join("resources", f"{current_user.id}_{file.filename}")
    full_path = os.path.join(settings.UPLOAD_FOLDER, file_path)
    
    with open(full_path, "wb") as f:
        shutil.copyfileobj(file.file, f)
    
    # 获取文件大小和类型
    file_size = os.path.getsize(full_path)
    file_type = file.content_type
    
    # 创建资源
    resource_in = ResourceCreate(
        title=title,
        description=description,
        file_path=file_path,
        file_size=file_size,
        file_type=file_type,
        is_premium=is_premium,
        category_id=category_id,
        uploader_id=current_user.id
    )
    
    resource = content_service.create_resource(db=db, obj_in=resource_in)
    
    return resource


@router.get("/resources/{resource_id}", response_model=ResourceDetail)
def get_resource(
    *,
    db: Session = Depends(get_db),
    resource_id: int,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取资源详情
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        current_user: 当前用户
        
    Returns:
        资源详情
    """
    resource = content_service.get_resource_with_details(db=db, resource_id=resource_id)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="资源不存在"
        )
    
    # 检查是否为会员内容，如果是则检查用户权限
    if resource.is_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    return resource


@router.put("/resources/{resource_id}", response_model=Resource)
def update_resource(
    *,
    db: Session = Depends(get_db),
    resource_id: int,
    resource_in: ResourceUpdate,
    current_user: User = Depends(get_current_active_user),
):
    """
    更新资源信息
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        resource_in: 资源更新模式
        current_user: 当前用户
        
    Returns:
        更新后的资源
    """
    resource = content_service.get_resource_by_id(db=db, resource_id=resource_id)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="资源不存在"
        )
    
    # 检查权限：只有上传者或管理员可以更新资源
    if resource.uploader_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 更新资源
    resource = content_service.update_resource(db=db, db_obj=resource, obj_in=resource_in)
    
    return resource


@router.delete("/resources/{resource_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_resource(
    *,
    db: Session = Depends(get_db),
    resource_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    删除资源
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        current_user: 当前用户
    """
    resource = content_service.get_resource_by_id(db=db, resource_id=resource_id)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="资源不存在"
        )
    
    # 检查权限：只有上传者或管理员可以删除资源
    if resource.uploader_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 删除文件
    file_path = os.path.join(settings.UPLOAD_FOLDER, resource.file_path)
    if os.path.exists(file_path):
        os.remove(file_path)
    
    # 删除资源
    content_service.delete_resource(db=db, resource_id=resource_id)


@router.get("/resources/{resource_id}/download")
def download_resource(
    *,
    db: Session = Depends(get_db),
    resource_id: int,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    下载资源
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        current_user: 当前用户
        
    Returns:
        文件响应
    """
    resource = content_service.get_resource_by_id(db=db, resource_id=resource_id)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="资源不存在"
        )
    
    # 检查是否为会员内容，如果是则检查用户权限
    if resource.is_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    # 检查文件是否存在
    file_path = os.path.join(settings.UPLOAD_FOLDER, resource.file_path)
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )
    
    # 增加下载次数
    content_service.increment_download_count(db=db, db_obj=resource)
    
    # 返回文件
    return FileResponse(
        path=file_path,
        filename=os.path.basename(resource.file_path),
        media_type=resource.file_type
    )


@router.post("/resources/{resource_id}/favorite", response_model=Favorite)
def add_resource_to_favorites(
    *,
    db: Session = Depends(get_db),
    resource_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    将资源添加到收藏
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        current_user: 当前用户
        
    Returns:
        收藏对象
    """
    # 检查资源是否存在
    resource = content_service.get_resource_by_id(db=db, resource_id=resource_id)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="资源不存在"
        )
    
    # 添加到收藏
    favorite = content_service.add_to_favorites(db=db, user_id=current_user.id, resource_id=resource_id)
    
    return favorite


@router.delete("/resources/{resource_id}/favorite", status_code=status.HTTP_204_NO_CONTENT)
def remove_resource_from_favorites(
    *,
    db: Session = Depends(get_db),
    resource_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    将资源从收藏中移除
    
    Args:
        db: 数据库会话
        resource_id: 资源ID
        current_user: 当前用户
    """
    # 检查资源是否存在
    resource = content_service.get_resource_by_id(db=db, resource_id=resource_id)
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="资源不存在"
        )
    
    # 从收藏中移除
    content_service.remove_from_favorites(db=db, user_id=current_user.id, resource_id=resource_id)


# 术语词典相关路由
@router.get("/glossary/", response_model=List[Glossary])
def get_glossary(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    search: Optional[str] = None,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取术语词典列表
    
    可以根据分类、搜索关键词进行过滤
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        category: 分类过滤
        search: 搜索关键词
        current_user: 当前用户
        
    Returns:
        术语词典列表
    """
    glossary = content_service.get_all_glossary(
        db=db, 
        skip=skip, 
        limit=limit,
        category=category,
        search=search
    )
    
    return glossary


@router.get("/glossary/categories", response_model=List[str])
def get_glossary_categories(
    db: Session = Depends(get_db),
):
    """
    获取所有术语词典分类
    
    Args:
        db: 数据库会话
        
    Returns:
        分类列表
    """
    categories = content_service.get_glossary_categories(db=db)
    return categories


@router.post("/glossary/", response_model=Glossary)
def create_glossary(
    *,
    db: Session = Depends(get_db),
    glossary_in: GlossaryCreate,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    创建术语词典条目（仅管理员）
    
    Args:
        db: 数据库会话
        glossary_in: 术语词典条目创建模式
        current_user: 当前用户（必须是管理员）
        
    Returns:
        创建的术语词典条目
    """
    # 检查术语是否已存在
    existing_glossary = content_service.get_glossary_by_term(db=db, term=glossary_in.term)
    if existing_glossary:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="术语已存在"
        )
    
    # 创建术语
    glossary = content_service.create_glossary(db=db, obj_in=glossary_in)
    
    return glossary


@router.get("/glossary/{glossary_id}", response_model=Glossary)
def get_glossary_item(
    *,
    db: Session = Depends(get_db),
    glossary_id: int,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取术语词典条目详情
    
    Args:
        db: 数据库会话
        glossary_id: 术语词典条目ID
        current_user: 当前用户
        
    Returns:
        术语词典条目详情
    """
    glossary = content_service.get_glossary_by_id(db=db, glossary_id=glossary_id)
    if not glossary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="术语不存在"
        )
    
    # 检查是否为会员内容，如果是则检查用户权限
    if glossary.is_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    return glossary


@router.put("/glossary/{glossary_id}", response_model=Glossary)
def update_glossary(
    *,
    db: Session = Depends(get_db),
    glossary_id: int,
    glossary_in: GlossaryUpdate,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    更新术语词典条目（仅管理员）
    
    Args:
        db: 数据库会话
        glossary_id: 术语词典条目ID
        glossary_in: 术语词典条目更新模式
        current_user: 当前用户（必须是管理员）
        
    Returns:
        更新后的术语词典条目
    """
    glossary = content_service.get_glossary_by_id(db=db, glossary_id=glossary_id)
    if not glossary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="术语不存在"
        )
    
    # 如果更新术语名称，检查新名称是否已存在
    if glossary_in.term and glossary_in.term != glossary.term:
        existing_glossary = content_service.get_glossary_by_term(db=db, term=glossary_in.term)
        if existing_glossary:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="术语名称已存在"
            )
    
    # 更新术语
    glossary = content_service.update_glossary(db=db, db_obj=glossary, obj_in=glossary_in)
    
    return glossary


@router.delete("/glossary/{glossary_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_glossary(
    *,
    db: Session = Depends(get_db),
    glossary_id: int,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    删除术语词典条目（仅管理员）
    
    Args:
        db: 数据库会话
        glossary_id: 术语词典条目ID
        current_user: 当前用户（必须是管理员）
    """
    glossary = content_service.get_glossary_by_id(db=db, glossary_id=glossary_id)
    if not glossary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="术语不存在"
        )
    
    # 删除术语
    content_service.delete_glossary(db=db, glossary_id=glossary_id)


# 通知相关路由
@router.get("/notifications/", response_model=List[Notification])
def get_notifications(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10,
    only_unread: bool = False,
    current_user: User = Depends(get_current_active_user),
):
    """
    获取当前用户的通知列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        only_unread: 是否只返回未读通知
        current_user: 当前用户
        
    Returns:
        通知列表
    """
    notifications = content_service.get_user_notifications(
        db=db, 
        user_id=current_user.id,
        skip=skip, 
        limit=limit,
        only_unread=only_unread
    )
    
    return notifications


@router.get("/notifications/unread-count")
def get_unread_notifications_count(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    获取当前用户的未读通知数量
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        未读通知数量
    """
    count = content_service.get_unread_notifications_count(db=db, user_id=current_user.id)
    
    return {"count": count}


@router.post("/notifications/mark-all-read", status_code=status.HTTP_200_OK)
def mark_all_notifications_as_read(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    将当前用户的所有通知标记为已读
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        更新的通知数量
    """
    count = content_service.mark_all_notifications_as_read(db=db, user_id=current_user.id)
    
    return {"count": count}


@router.put("/notifications/{notification_id}/read", response_model=Notification)
def mark_notification_as_read(
    *,
    db: Session = Depends(get_db),
    notification_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    将通知标记为已读
    
    Args:
        db: 数据库会话
        notification_id: 通知ID
        current_user: 当前用户
        
    Returns:
        更新后的通知
    """
    notification = content_service.get_notification_by_id(db=db, notification_id=notification_id)
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="通知不存在"
        )
    
    # 检查权限：只能标记自己的通知
    if notification.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    notification = content_service.mark_notification_as_read(db=db, notification_id=notification_id)
    
    return notification


@router.delete("/notifications/{notification_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_notification(
    *,
    db: Session = Depends(get_db),
    notification_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    删除通知
    
    Args:
        db: 数据库会话
        notification_id: 通知ID
        current_user: 当前用户
    """
    notification = content_service.get_notification_by_id(db=db, notification_id=notification_id)
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="通知不存在"
        )
    
    # 检查权限：只能删除自己的通知或管理员可以删除任何通知
    if notification.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    content_service.delete_notification(db=db, notification_id=notification_id) 