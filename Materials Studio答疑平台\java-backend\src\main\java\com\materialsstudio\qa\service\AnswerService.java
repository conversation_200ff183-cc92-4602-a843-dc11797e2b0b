package com.materialsstudio.qa.service;

import com.materialsstudio.qa.model.dto.AnswerPostDTO;
import com.materialsstudio.qa.model.vo.AnswerVO;

import java.util.List;

/**
 * 回答服务接口
 */
public interface AnswerService {
    
    /**
     * 根据问题ID获取回答列表
     *
     * @param questionId 问题ID
     * @return 回答列表
     */
    List<AnswerVO> getAnswersByQuestionId(Long questionId);
    
    /**
     * 发布回答
     *
     * @param answerPostDTO 回答发布信息
     * @return 回答ID
     */
    Long postAnswer(AnswerPostDTO answerPostDTO);
    
    /**
     * 采纳回答
     *
     * @param id 回答ID
     * @return 是否成功
     */
    boolean acceptAnswer(Long id);
    
    /**
     * 点赞回答
     *
     * @param id 回答ID
     * @return 是否成功
     */
    boolean voteAnswer(Long id);
} 