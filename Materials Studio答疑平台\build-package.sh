#!/bin/bash

# 生成部署包的脚本
# 此脚本将前端和后端打包为一个可部署的压缩包

# 检查是否安装了所需工具
command -v mvn >/dev/null 2>&1 || { echo "需要安装Maven，请运行: sudo apt install maven"; exit 1; }
command -v node >/dev/null 2>&1 || { echo "需要安装Node.js，请运行: sudo apt install nodejs"; exit 1; }
command -v zip >/dev/null 2>&1 || { echo "需要安装zip，请运行: sudo apt install zip"; exit 1; }

# 设置环境变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${SCRIPT_DIR}/build"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="materials_studio_qa_${TIMESTAMP}.zip"

echo "===== 开始构建部署包 ====="

# 创建构建目录
echo "创建构建目录..."
mkdir -p "${BUILD_DIR}"
rm -rf "${BUILD_DIR:?}"/*

# 1. 构建后端JAR包
echo "构建后端JAR包..."
cd "${SCRIPT_DIR}/java-backend" || exit 1
mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "后端构建失败，退出构建"
    exit 1
fi

# 复制后端JAR包到构建目录
echo "复制后端JAR包..."
mkdir -p "${BUILD_DIR}/backend"
cp target/qa-platform-0.0.1-SNAPSHOT.jar "${BUILD_DIR}/backend/"

# 2. 准备前端文件
echo "准备前端文件..."
cd "${SCRIPT_DIR}" || exit 1

# 将前端配置切换为生产环境
echo "切换前端配置为生产环境..."
node switch-to-prod.js

# 复制前端文件到构建目录
echo "复制前端文件..."
mkdir -p "${BUILD_DIR}/frontend"
cp *.html "${BUILD_DIR}/frontend/"
cp -r js "${BUILD_DIR}/frontend/"
cp -r css "${BUILD_DIR}/frontend/" 2>/dev/null || :
cp -r images "${BUILD_DIR}/frontend/" 2>/dev/null || :
cp -r fonts "${BUILD_DIR}/frontend/" 2>/dev/null || :
cp -r assets "${BUILD_DIR}/frontend/" 2>/dev/null || :

# 3. 复制部署相关文件
echo "复制部署脚本..."
cp 部署指南.md "${BUILD_DIR}/"

# 创建部署脚本
echo "创建快速部署脚本..."
cat > "${BUILD_DIR}/deploy.sh" << 'EOL'
#!/bin/bash

# 快速部署脚本

# 检查是否以root权限运行
if [ "$(id -u)" != "0" ]; then
   echo "此脚本需要root权限，请使用sudo运行" 
   exit 1
fi

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="/app/materials-studio-qa"
FRONTEND_DIR="/var/www/materials-studio-qa"

# 1. 部署后端
echo "部署后端应用..."
mkdir -p ${BACKEND_DIR}
cp ${SCRIPT_DIR}/backend/qa-platform-0.0.1-SNAPSHOT.jar ${BACKEND_DIR}/

# 创建启动脚本
cat > ${BACKEND_DIR}/start.sh << 'EOF'
#!/bin/bash
cd /app/materials-studio-qa
nohup java -Dspring.profiles.active=prod -jar qa-platform-0.0.1-SNAPSHOT.jar > app.log 2>&1 &
echo $! > app.pid
echo "应用已启动，PID: $(cat app.pid)"
EOF

chmod +x ${BACKEND_DIR}/start.sh

# 创建停止脚本
cat > ${BACKEND_DIR}/stop.sh << 'EOF'
#!/bin/bash
cd /app/materials-studio-qa
if [ -f app.pid ]; then
    kill $(cat app.pid)
    rm app.pid
    echo "应用已停止"
else
    echo "未找到PID文件"
fi
EOF

chmod +x ${BACKEND_DIR}/stop.sh

# 2. 部署前端
echo "部署前端文件..."
mkdir -p ${FRONTEND_DIR}
cp -r ${SCRIPT_DIR}/frontend/* ${FRONTEND_DIR}/

# 3. 创建uploads目录
echo "创建上传文件目录..."
mkdir -p /app/uploads
chmod -R 755 /app/uploads

# 4. 设置环境变量
echo "请设置以下环境变量："
echo "export MYSQL_USERNAME=数据库用户名"
echo "export MYSQL_PASSWORD=数据库密码"
echo "export REDIS_HOST=Redis主机"
echo "export REDIS_PORT=6379"
echo "export REDIS_PASSWORD=Redis密码"
echo "export JWT_SECRET=JWT密钥"
echo "export FILE_UPLOAD_DIR=/app/uploads/"

echo "===== 部署完成 ====="
echo "后端应用部署在: ${BACKEND_DIR}"
echo "前端文件部署在: ${FRONTEND_DIR}"
echo "请按照部署指南.md中的说明配置Web服务器和数据库"
echo "启动应用: ${BACKEND_DIR}/start.sh"
echo "停止应用: ${BACKEND_DIR}/stop.sh"
EOL

chmod +x "${BUILD_DIR}/deploy.sh"

# 4. 打包所有文件
echo "打包所有文件..."
cd "${BUILD_DIR}" || exit 1
zip -r "../${PACKAGE_NAME}" ./*

echo "===== 构建完成 ====="
echo "部署包已创建: ${SCRIPT_DIR}/${PACKAGE_NAME}"
echo "请将此文件上传到服务器，解压后按照部署指南进行部署" 