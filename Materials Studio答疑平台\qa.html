<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问答中心 - Materials Studio 答疑平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    
    <!-- 科技感效果库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #0d47a1;
            --accent-color: #03a9f4;
            --light-bg: #f5f5f5;
            --member-color: #ffc107;
            --success-color: #4caf50;
            --info-color: #03a9f4;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --dark-color: #212121;
            --text-color: #212121;
            --light-text: #ffffff;
            --border-radius: 8px;
            --box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 4px 8px rgba(0,0,0,0.06);
            --transition-speed: 0.3s;
        }
        
        body {
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        /* 导航栏 */
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.85);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all var(--transition-speed);
        }
        
        .navbar-dark .navbar-nav .nav-link:hover,
        .navbar-dark .navbar-nav .nav-link.active {
            color: rgba(255,255,255,1);
            background-color: rgba(255,255,255,0.2);
        }
        
        .member-badge {
            background-color: var(--member-color);
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        /* 页面标题区域 */
        .page-title-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 50px 0;
            position: relative;
        }
        
        .page-title-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://images.unsplash.com/photo-1557425529-b1ae9c141e7a?ixid=MnwxMjA3fDB8MHxzZWFyY2h8MXx8bW9sZWN1bGUlMjBzdHJ1Y3R1cmV8ZW58MHx8MHx8&auto=format&fit=crop&w=1200&q=60') center/cover no-repeat;
            opacity: 0.1;
            z-index: 0;
        }
        
        .page-title-section .container {
            position: relative;
            z-index: 1;
        }
        
        /* 问题列表 */
        .question-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 0;
            margin-bottom: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        
        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .question-header {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            background-color: rgba(0,0,0,0.02);
        }
        
        .question-body {
            padding: 20px;
        }
        
        .question-footer {
            padding: 10px 20px;
            background-color: rgba(0,0,0,0.02);
            border-top: 1px solid rgba(0,0,0,0.1);
            font-size: 0.9rem;
        }
        
        .question-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .question-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .question-tag {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            margin-right: 5px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }
        
        .tag-method {
            background-color: rgba(52, 152, 219, 0.15);
            color: #2980b9;
        }
        
        .tag-parameter {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }
        
        .tag-error {
            background-color: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }
        
        .tag-analysis {
            background-color: rgba(155, 89, 182, 0.15);
            color: #8e44ad;
        }
        
        /* 侧边栏 */
        .sidebar-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .category-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .category-list li {
            margin-bottom: 10px;
        }
        
        .category-list a {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .category-list a:hover {
            background-color: rgba(0,0,0,0.05);
        }
        
        .category-list a.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .category-count {
            background-color: rgba(0,0,0,0.1);
            color: var(--text-color);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.75rem;
        }
        
        .category-list a.active .category-count {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        
        /* 会员内容 */
        .member-content {
            background-color: #fff8e6;
            border: 1px dashed var(--member-color);
            border-radius: var(--border-radius);
            padding: 15px;
            margin: 15px 0;
            position: relative;
        }
        
        .member-content-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: var(--border-radius);
        }
        
        .member-content-message {
            text-align: center;
            margin-bottom: 15px;
        }
        
        /* 分页 */
        .pagination .page-link {
            color: var(--primary-color);
            border-radius: 4px;
            margin: 0 3px;
        }
        
        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        /* 浮动按钮 */
        .floating-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .floating-btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.3);
        }
        
        .floating-btn i {
            font-size: 24px;
        }
        
        /* 页脚 */
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
            margin-top: 50px;
        }
        
        .footer-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
        }
        
        /* 科技装饰元素 */
        .tech-decorator {
            position: absolute;
            width: 60px;
            height: 60px;
            z-index: 0;
            opacity: 0.1;
        }
        
        .tech-decorator.top-right {
            top: -10px;
            right: -10px;
            border-top: 2px solid var(--primary-color);
            border-right: 2px solid var(--primary-color);
        }
        
        .tech-decorator.bottom-left {
            bottom: -10px;
            left: -10px;
            border-bottom: 2px solid var(--primary-color);
            border-left: 2px solid var(--primary-color);
        }
        
        /* 悬停动态效果 */
        .question-card {
            overflow: visible !important;
        }
        
        .question-card:hover .tech-decorator {
            opacity: 0.3;
            transition: opacity 0.3s ease;
        }
        
        #particles-js {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">Materials Studio 答疑平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="qa.html"><i class="fas fa-question-circle"></i> 问答</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html"><i class="fas fa-book"></i> 学习资源</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.html"><i class="fas fa-users"></i> 社区</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light" href="register.html"><i class="fas fa-user-plus"></i> 注册</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 粒子背景 -->
    <div id="particles-js" style="position: absolute; width: 100%; height: 300px; z-index: 0;"></div>

    <!-- 页面标题区域 -->
    <section class="page-title-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6" data-aos="fade-right">
                    <h1 class="mb-3">问答中心</h1>
                    <p class="lead mb-0">查找关于 Materials Studio 软件的问题解答，或提出您的疑问</p>
                </div>
                <div class="col-md-6" data-aos="fade-left">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-lg" placeholder="搜索问题...">
                        <button class="btn btn-light" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要内容 -->
    <div class="container py-5">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar-card">
                    <h5 class="sidebar-title">问题分类</h5>
                    <ul class="category-list">
                        <li><a href="#" class="active">所有问题 <span class="category-count">256</span></a></li>
                        <li><a href="#">分子动力学 <span class="category-count">78</span></a></li>
                        <li><a href="#">CASTEP <span class="category-count">64</span></a></li>
                        <li><a href="#">Forcite <span class="category-count">42</span></a></li>
                        <li><a href="#">晶体构建 <span class="category-count">38</span></a></li>
                        <li><a href="#">界面构建 <span class="category-count">34</span></a></li>
                    </ul>
                </div>
                
                <div class="sidebar-card">
                    <h5 class="sidebar-title">热门标签</h5>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="#" class="question-tag tag-method">力场选择</a>
                        <a href="#" class="question-tag tag-parameter">计算参数</a>
                        <a href="#" class="question-tag tag-error">错误解决</a>
                        <a href="#" class="question-tag tag-analysis">结果分析</a>
                        <a href="#" class="question-tag tag-method">模型构建</a>
                        <a href="#" class="question-tag tag-parameter">收敛问题</a>
                    </div>
                </div>
                
                <div class="sidebar-card">
                    <h5 class="sidebar-title">筛选</h5>
                    <div class="mb-3">
                        <label for="sort-by" class="form-label">排序方式</label>
                        <select class="form-select" id="sort-by">
                            <option value="newest">最新提问</option>
                            <option value="hottest">热门问题</option>
                            <option value="most-answered">回答最多</option>
                            <option value="unanswered">未回答</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show-answered">
                            <label class="form-check-label" for="show-answered">
                                只看已解决
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show-member-content">
                            <label class="form-check-label" for="show-member-content">
                                显示会员内容
                            </label>
                        </div>
                    </div>
                    <button class="btn btn-primary w-100">应用筛选</button>
                </div>
            </div>
            
            <!-- 问题列表 -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>最新问题 (256)</h4>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary active">
                            <i class="fas fa-th-list"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 问题项 -->
                <div class="question-card" data-aos="fade-up">
                    <div class="question-header">
                        <h5 class="question-title">
                            <a href="qa-detail.html?id=1" class="text-decoration-none text-dark">如何在 Materials Studio 中正确设置分子动力学模拟参数？</a>
                        </h5>
                        <div class="question-meta">
                            <span><i class="fas fa-user me-1"></i>张博士</span>
                            <span class="ms-3"><i class="fas fa-clock me-1"></i>2023-10-15</span>
                            <span class="ms-3"><i class="fas fa-eye me-1"></i>245 次浏览</span>
                            <span class="ms-3"><i class="fas fa-comment me-1"></i>3 个回答</span>
                        </div>
                    </div>
                    <div class="question-body">
                        <p>我在尝试使用 Materials Studio 的 Forcite 模块进行水溶液中的分子动力学模拟时，遇到了一些问题。我设置了 NVT 系综，温度为 298K，但模拟过程中温度波动很大。另外，我不确定力场应该选择哪一个，COMPASS II 还是 PCFF 更适合我的体系？</p>
                        <div class="mt-2">
                            <span class="question-tag tag-method">分子动力学</span>
                            <span class="question-tag tag-parameter">温度控制</span>
                            <span class="question-tag tag-method">力场选择</span>
                        </div>
                    </div>
                    <div class="question-footer d-flex justify-content-between align-items-center">
                        <span class="badge bg-success">已解决</span>
                        <a href="qa-detail.html?id=1" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                </div>
                
                <div class="question-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="question-header">
                        <h5 class="question-title">
                            <a href="qa-detail.html?id=2" class="text-decoration-none text-dark">CASTEP 模块计算过程中出现 SCF 收敛问题如何解决？</a>
                        </h5>
                        <div class="question-meta">
                            <span><i class="fas fa-user me-1"></i>李教授</span>
                            <span class="ms-3"><i class="fas fa-clock me-1"></i>2023-10-10</span>
                            <span class="ms-3"><i class="fas fa-eye me-1"></i>186 次浏览</span>
                            <span class="ms-3"><i class="fas fa-comment me-1"></i>2 个回答</span>
                        </div>
                    </div>
                    <div class="question-body">
                        <p>在使用 CASTEP 计算金属氧化物表面的吸附能时，经常遇到 SCF 无法收敛的问题。我已经尝试了增加最大 SCF 循环次数和调整收敛阈值，但问题依然存在。有没有其他方法可以帮助解决这个问题？</p>
                        <div class="member-content">
                            <p>我在尝试了多种方法后，发现以下几种方法对收敛有显著帮助：</p>
                            <div class="member-content-overlay">
                                <div class="member-content-message">
                                    <h5><i class="fas fa-lock me-2"></i>会员专享内容</h5>
                                    <p>成为会员后可查看完整解答</p>
                                </div>
                                <a href="membership.html" class="btn btn-warning">升级会员</a>
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="question-tag tag-error">收敛问题</span>
                            <span class="question-tag tag-parameter">SCF</span>
                            <span class="question-tag tag-method">CASTEP</span>
                        </div>
                    </div>
                    <div class="question-footer d-flex justify-content-between align-items-center">
                        <span class="badge bg-warning text-dark">会员内容</span>
                        <a href="qa-detail.html?id=2" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                </div>
                
                <div class="question-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="question-header">
                        <h5 class="question-title">
                            <a href="qa-detail.html?id=3" class="text-decoration-none text-dark">如何正确分析 Materials Studio 中的径向分布函数结果？</a>
                        </h5>
                        <div class="question-meta">
                            <span><i class="fas fa-user me-1"></i>王同学</span>
                            <span class="ms-3"><i class="fas fa-clock me-1"></i>2023-10-05</span>
                            <span class="ms-3"><i class="fas fa-eye me-1"></i>127 次浏览</span>
                            <span class="ms-3"><i class="fas fa-comment me-1"></i>1 个回答</span>
                        </div>
                    </div>
                    <div class="question-body">
                        <p>我在 Materials Studio 中进行了水-乙醇混合溶液的分子动力学模拟，并计算了径向分布函数（RDF）。但我不太清楚如何正确解读这些 RDF 曲线，特别是不同原子对之间的相互作用信息。有没有关于 RDF 解读的详细指南？</p>
                        <div class="mt-2">
                            <span class="question-tag tag-analysis">径向分布函数</span>
                            <span class="question-tag tag-analysis">数据解读</span>
                            <span class="question-tag tag-method">分子动力学</span>
                        </div>
                    </div>
                    <div class="question-footer d-flex justify-content-between align-items-center">
                        <span class="badge bg-primary">待解答</span>
                        <a href="qa-detail.html?id=3" class="btn btn-sm btn-outline-primary">查看详情</a>
                    </div>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="问题分页" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    
    <!-- 浮动提问按钮 -->
    <a href="ask-question.html" class="floating-btn" title="提出问题">
        <i class="fas fa-plus"></i>
    </a>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h4 class="footer-title">Materials Studio 答疑平台</h4>
                    <p>专注于为材料科学研究者提供专业的 Materials Studio 软件使用指导、问题解答和资源共享。</p>
                    <div class="social-links mt-3">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">快速链接</h5>
                    <div class="footer-links">
                        <a href="index.html">首页</a>
                        <a href="qa.html">问答</a>
                        <a href="resources.html">资源</a>
                        <a href="community.html">社区</a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">帮助</h5>
                    <div class="footer-links">
                        <a href="#">常见问题</a>
                        <a href="#">使用指南</a>
                        <a href="#">联系我们</a>
                        <a href="#">意见反馈</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5 class="footer-title">联系我们</h5>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-phone me-2"></i> ************</p>
                    <p><i class="fas fa-map-marker-alt me-2"></i> 北京市海淀区中关村科技园</p>
                </div>
            </div>
            <hr class="mt-4 mb-3 bg-light">
            <div class="text-center py-3">
                <p class="mb-0">&copy; 2023 Materials Studio 答疑平台. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    
    <!-- API请求脚本 -->
    <script src="js/api/config.js"></script>
    <script src="js/api/utils.js"></script>
    <script src="js/api/auth.js"></script>
    <script src="js/api/qa.js"></script>
    <script src="js/api/user.js"></script>
    <script src="js/api/index.js"></script>

    <script>
        // 初始化AOS动画
            AOS.init({
                duration: 800,
                once: true
            });
            
        // 初始化代码高亮
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('pre code').forEach((el) => {
                hljs.highlightElement(el);
            });
        });
        
        // 查询参数
        let currentPage = 1;
        const PAGE_SIZE = 10;
        let currentCategory = '';
        let currentTag = '';
        let currentSearchQuery = '';
        let currentFilter = {
            only_premium: false,
            only_solved: false,
            author_id: null
        };
        
        // 在页面加载时检查用户登录状态并加载数据
        document.addEventListener('api:ready', async () => {
            try {
                // 更新导航栏
                updateNavbar();
                
                // 解析URL参数
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('tag')) {
                    currentTag = urlParams.get('tag');
                    document.getElementById('current-tag-filter').textContent = `标签: ${currentTag}`;
                    document.getElementById('clear-tag-filter').style.display = 'inline-block';
                }
                
                if (urlParams.has('category')) {
                    currentCategory = urlParams.get('category');
                    const categorySelect = document.getElementById('category-select');
                    if (categorySelect) {
                        categorySelect.value = currentCategory;
                    }
                }
                
                if (urlParams.has('q')) {
                    currentSearchQuery = urlParams.get('q');
                    document.getElementById('search-input').value = currentSearchQuery;
                    document.getElementById('current-search-filter').textContent = `搜索: ${currentSearchQuery}`;
                    document.getElementById('clear-search-filter').style.display = 'inline-block';
                }
                
                // 加载数据
                await loadQuestions();
                await loadTags();
                
                // 绑定事件
                setupEventListeners();
                
            } catch (error) {
                console.error('页面初始化错误:', error);
            }
        });
        
        // 更新导航栏显示
        async function updateNavbar() {
            const userNavElement = document.getElementById('user-nav');
            
            if (window.api.auth.isLoggedIn()) {
                try {
                    // 获取用户信息
                    const user = await window.api.auth.getCurrentUser();
                    const membershipInfo = await window.api.user.getMembershipInfo();
                    
                    // 更新导航栏显示已登录状态
                    let memberBadge = '';
                    if (membershipInfo && membershipInfo.is_active) {
                        memberBadge = `<span class="member-badge">${membershipInfo.level_name || '会员'}</span>`;
                    }
                    
                    userNavElement.innerHTML = `
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle"></i> ${user.username} ${memberBadge}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.html"><i class="fas fa-id-card"></i> 个人资料</a></li>
                                <li><a class="dropdown-item" href="membership.html"><i class="fas fa-crown"></i> 会员中心</a></li>
                                <li><a class="dropdown-item" href="favorites.html"><i class="fas fa-star"></i> 我的收藏</a></li>
                                <li><a class="dropdown-item" href="my-questions.html"><i class="fas fa-question-circle"></i> 我的提问</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                            </ul>
                        </li>
                    `;
                    
                    // 绑定退出登录按钮事件
                    document.getElementById('logout-btn').addEventListener('click', (event) => {
                        event.preventDefault();
                        window.api.auth.logout();
                        window.location.reload();
                    });
                    
                    // 显示提问按钮
                    document.getElementById('ask-question-btn').style.display = 'block';
                } catch (error) {
                    console.error('获取用户信息失败:', error);
                    // 如果获取用户信息失败，可能是token无效，执行登出
                    window.api.auth.logout();
                    updateNavbarForGuest();
                }
            } else {
                updateNavbarForGuest();
                
                // 隐藏提问按钮或改为跳转到登录页
                const askQuestionBtn = document.getElementById('ask-question-btn');
                askQuestionBtn.href = 'login.html';
                askQuestionBtn.setAttribute('data-bs-toggle', 'tooltip');
                askQuestionBtn.setAttribute('data-bs-placement', 'bottom');
                askQuestionBtn.setAttribute('title', '请先登录后提问');
                
                // 初始化工具提示
                new bootstrap.Tooltip(askQuestionBtn);
            }
        }
        
        // 更新导航栏为游客状态
        function updateNavbarForGuest() {
            const userNavElement = document.getElementById('user-nav');
            userNavElement.innerHTML = `
                <li class="nav-item">
                    <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录/注册</a>
                </li>
            `;
        }
        
        // 加载问题列表
        async function loadQuestions() {
            try {
                showLoading(true);
                
                const skip = (currentPage - 1) * PAGE_SIZE;
                const limit = PAGE_SIZE;
                
                // 构建查询参数
                const queryParams = {
                    skip,
                    limit,
                    search: currentSearchQuery || undefined,
                    only_premium: currentFilter.only_premium || undefined,
                    only_solved: currentFilter.only_solved || undefined,
                    author_id: currentFilter.author_id || undefined
                };
                
                // 如果有标签过滤，添加标签ID
                if (currentTag) {
                    queryParams.tag_id = currentTag;
                }
                
                // 获取问题列表
                const result = await window.api.qa.getQuestions(queryParams);
                
                // 显示问题列表
                displayQuestions(result.items);
                
                // 更新分页
                updatePagination(result.total, PAGE_SIZE, currentPage);
                
            } catch (error) {
                console.error('加载问题列表失败:', error);
                showError('加载问题列表失败，请稍后重试');
            } finally {
                showLoading(false);
            }
        }
        
        // 显示问题列表
        function displayQuestions(questions) {
            const questionsContainer = document.getElementById('questions-container');
            
            if (!questions || questions.length === 0) {
                questionsContainer.innerHTML = '<div class="alert alert-info">暂无相关问题，请尝试其他搜索条件或 <a href="ask-question.html" class="alert-link">提出新问题</a></div>';
                return;
            }
            
            let questionsHTML = '';
            
            questions.forEach(question => {
                // 准备标签HTML
                let tagsHTML = '';
                if (question.tags && question.tags.length > 0) {
                    question.tags.forEach(tag => {
                        tagsHTML += `<span class="question-tag tag-${tag.category || 'default'}" data-tag-id="${tag.id}">${tag.name}</span>`;
                    });
                }
                
                // 准备会员标签
                const premiumBadge = question.is_premium 
                    ? '<span class="badge bg-warning text-dark ms-2"><i class="fas fa-crown"></i> 会员</span>' 
                    : '';
                
                // 准备已解决标签
                const solvedBadge = question.is_solved 
                    ? '<span class="badge bg-success ms-2"><i class="fas fa-check-circle"></i> 已解决</span>' 
                    : '';
                
                // 计算回答数量标签的样式
                let answerBadgeClass = 'bg-secondary';
                if (question.answer_count > 0) {
                    answerBadgeClass = question.is_solved ? 'bg-success' : 'bg-primary';
                }
                
                // 格式化日期
                const createdDate = new Date(question.created_at).toLocaleDateString();
                
                questionsHTML += `
                    <div class="question-card" data-aos="fade-up">
                        <div class="question-header">
                            <h3 class="question-title">
                                <a href="qa-detail.html?id=${question.id}">${question.title}</a>
                                ${premiumBadge}
                                ${solvedBadge}
                            </h3>
                            <div class="question-meta">
                                <span><i class="fas fa-user"></i> ${question.author.username}</span>
                                <span><i class="far fa-clock"></i> ${createdDate}</span>
                                <span><i class="far fa-eye"></i> ${question.view_count || 0} 次查看</span>
                            </div>
                        </div>
                        <div class="question-body">
                            <p>${question.content.substring(0, 200)}${question.content.length > 200 ? '...' : ''}</p>
                            <div class="question-tags">
                                ${tagsHTML}
                            </div>
                        </div>
                        <div class="question-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge ${answerBadgeClass}">${question.answer_count} 回答</span>
                                <a href="qa-detail.html?id=${question.id}" class="btn btn-sm btn-outline-primary">查看详情</a>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            questionsContainer.innerHTML = questionsHTML;
            
            // 为标签添加点击事件
            document.querySelectorAll('.question-tag').forEach(tagElement => {
                tagElement.addEventListener('click', (event) => {
                    const tagId = event.currentTarget.getAttribute('data-tag-id');
                    const tagName = event.currentTarget.textContent;
                    
                    // 更新标签过滤
                    currentTag = tagId;
                    document.getElementById('current-tag-filter').textContent = `标签: ${tagName}`;
                    document.getElementById('clear-tag-filter').style.display = 'inline-block';
                    
                    // 重置到第一页
                    currentPage = 1;
                    
                    // 重新加载问题
                    loadQuestions();
                });
            });
        }
        
        // 更新分页
        function updatePagination(total, pageSize, currentPage) {
            const totalPages = Math.ceil(total / pageSize);
            const paginationContainer = document.getElementById('pagination-container');
            
            if (totalPages <= 1) {
                paginationContainer.innerHTML = '';
                return;
            }
            
            let paginationHTML = `
                <nav aria-label="问题列表分页">
                    <ul class="pagination justify-content-center">
                        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="上一页">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
            `;
            
            // 显示的页码范围
            const range = 2; // 当前页前后显示的页数
            let startPage = Math.max(1, currentPage - range);
            let endPage = Math.min(totalPages, currentPage + range);
            
            // 确保至少显示2*range+1个页码
            if (endPage - startPage < 2 * range) {
                if (startPage === 1) {
                    endPage = Math.min(totalPages, startPage + 2 * range);
                } else if (endPage === totalPages) {
                    startPage = Math.max(1, endPage - 2 * range);
                }
            }
            
            // 添加第一页
            if (startPage > 1) {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="1">1</a>
                    </li>
                `;
                if (startPage > 2) {
                    paginationHTML += `
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    `;
                }
            }
            
            // 添加页码
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            }
            
            // 添加最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHTML += `
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    `;
                }
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
                    </li>
                `;
            }
            
            paginationHTML += `
                        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="下一页">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            `;
            
            paginationContainer.innerHTML = paginationHTML;
            
            // 添加分页点击事件
            document.querySelectorAll('.pagination .page-link').forEach(pageLink => {
                pageLink.addEventListener('click', (event) => {
                    event.preventDefault();
                    const page = parseInt(event.currentTarget.getAttribute('data-page'));
                    if (page && page !== currentPage) {
                        currentPage = page;
                        loadQuestions();
                        // 滚动到顶部
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                });
            });
        }
        
        // 加载标签列表
        async function loadTags() {
            try {
                const tags = await window.api.qa.getTags();
                
                if (tags && tags.length > 0) {
                    const tagCloudElement = document.getElementById('tag-cloud');
                    let tagHTML = '';
                    
                    tags.forEach(tag => {
                        // 根据tag.count决定标签的大小，从1到5级
                        const tagSize = Math.min(Math.ceil(tag.count / 5), 5);
                        tagHTML += `<a href="#" class="tag-size-${tagSize}" data-tag-id="${tag.id}">${tag.name}</a> `;
                    });
                    
                    tagCloudElement.innerHTML = tagHTML;
                    
                    // 为标签添加点击事件
                    document.querySelectorAll('#tag-cloud a').forEach(tagElement => {
                        tagElement.addEventListener('click', (event) => {
                            event.preventDefault();
                            const tagId = event.currentTarget.getAttribute('data-tag-id');
                            const tagName = event.currentTarget.textContent;
                            
                            // 更新标签过滤
                            currentTag = tagId;
                            document.getElementById('current-tag-filter').textContent = `标签: ${tagName}`;
                            document.getElementById('clear-tag-filter').style.display = 'inline-block';
                            
                            // 重置到第一页
                            currentPage = 1;
                            
                            // 重新加载问题
                            loadQuestions();
                        });
                    });
                }
            } catch (error) {
                console.error('加载标签列表失败:', error);
            }
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 搜索表单提交
            document.getElementById('search-form').addEventListener('submit', (event) => {
                event.preventDefault();
                const searchQuery = document.getElementById('search-input').value.trim();
                
                // 更新搜索过滤
                currentSearchQuery = searchQuery;
                if (searchQuery) {
                    document.getElementById('current-search-filter').textContent = `搜索: ${searchQuery}`;
                    document.getElementById('clear-search-filter').style.display = 'inline-block';
                } else {
                    document.getElementById('current-search-filter').textContent = '';
                    document.getElementById('clear-search-filter').style.display = 'none';
                }
                
                // 重置到第一页
                currentPage = 1;
                
                // 重新加载问题
                loadQuestions();
            });
            
            // 清除标签过滤
            document.getElementById('clear-tag-filter').addEventListener('click', (event) => {
                event.preventDefault();
                currentTag = '';
                document.getElementById('current-tag-filter').textContent = '';
                document.getElementById('clear-tag-filter').style.display = 'none';
                
                // 重置到第一页
                currentPage = 1;
                
                // 重新加载问题
                loadQuestions();
            });
            
            // 清除搜索过滤
            document.getElementById('clear-search-filter').addEventListener('click', (event) => {
                event.preventDefault();
                currentSearchQuery = '';
                document.getElementById('search-input').value = '';
                document.getElementById('current-search-filter').textContent = '';
                document.getElementById('clear-search-filter').style.display = 'none';
                
                // 重置到第一页
                currentPage = 1;
                
                // 重新加载问题
                loadQuestions();
            });
            
            // 过滤器更改
            document.getElementById('filter-only-premium').addEventListener('change', (event) => {
                currentFilter.only_premium = event.target.checked;
                currentPage = 1;
                loadQuestions();
            });
            
            document.getElementById('filter-only-solved').addEventListener('change', (event) => {
                currentFilter.only_solved = event.target.checked;
                currentPage = 1;
                loadQuestions();
            });
            
            // 排序选择
            document.getElementById('sort-select').addEventListener('change', (event) => {
                currentSort = event.target.value;
                currentPage = 1;
                loadQuestions();
            });
        }
        
        // 显示加载指示器
        function showLoading(isLoading) {
            const loadingElement = document.getElementById('loading-indicator');
            if (loadingElement) {
                loadingElement.style.display = isLoading ? 'block' : 'none';
            }
        }
        
        // 显示错误消息
        function showError(message) {
            const errorElement = document.getElementById('error-message');
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    errorElement.style.display = 'none';
                }, 3000);
            }
        }
    </script>
</body>
</html> 