package com.materialsstudio.qa.controller;

import com.materialsstudio.qa.common.Result;
import com.materialsstudio.qa.model.dto.LoginDTO;
import com.materialsstudio.qa.model.vo.LoginVO;
import com.materialsstudio.qa.model.vo.UserVO;
import com.materialsstudio.qa.service.UserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Resource
    private UserService userService;
    
    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<LoginVO> login(@Validated @RequestBody LoginDTO loginDTO) {
        try {
            LoginVO loginVO = userService.login(loginDTO);
            return Result.success(loginVO);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/me")
    public Result<UserVO> getCurrentUser() {
        try {
            UserVO userVO = userService.getCurrentUser();
            return Result.success(userVO);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
} 