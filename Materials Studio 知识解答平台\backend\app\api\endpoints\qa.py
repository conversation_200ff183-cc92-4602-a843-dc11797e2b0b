from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form, Query
from sqlalchemy.orm import Session

from app.core.deps import get_current_active_user, get_db, check_premium_content_access
from app.models.user import User
from app.schemas.qa import (
    Question, QuestionCreate, QuestionUpdate, QuestionDetail, QuestionSummary,
    Answer, AnswerCreate, AnswerUpdate, AnswerDetail,
    Comment, CommentCreate, CommentDetail,
    Tag, TagCreate, TagUpdate,
    Favorite
)
from app.services import qa_service

router = APIRouter()


# 问题相关路由
@router.get("/questions/", response_model=List[QuestionSummary])
def get_questions(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10,
    tag_id: Optional[int] = None,
    author_id: Optional[int] = None,
    search: Optional[str] = None,
    only_premium: bool = False,
    only_solved: bool = False,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取问题列表
    
    可以根据标签、作者、搜索关键词进行过滤
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        tag_id: 标签ID过滤
        author_id: 作者ID过滤
        search: 搜索关键词
        only_premium: 是否只返回会员内容
        only_solved: 是否只返回已解决的问题
        current_user: 当前用户
        
    Returns:
        问题列表
    """
    if only_premium:
        # 检查用户是否有权限访问会员内容
        check_premium_content_access(current_user, is_premium_content=True)
    
    questions = qa_service.get_questions(
        db=db, 
        skip=skip, 
        limit=limit,
        tag_id=tag_id,
        author_id=author_id,
        search=search,
        only_premium=only_premium,
        only_solved=only_solved
    )
    
    total = qa_service.get_questions_count(
        db=db,
        tag_id=tag_id,
        author_id=author_id,
        search=search,
        only_premium=only_premium,
        only_solved=only_solved
    )
    
    return questions


@router.post("/questions/", response_model=Question)
def create_question(
    *,
    db: Session = Depends(get_db),
    question_in: QuestionCreate,
    current_user: User = Depends(get_current_active_user),
):
    """
    创建新问题
    
    Args:
        db: 数据库会话
        question_in: 问题创建模式
        current_user: 当前用户
        
    Returns:
        创建的问题
    """
    # 设置作者ID
    question_in_data = question_in.dict()
    question_in_data["author_id"] = current_user.id
    
    # 创建问题
    question = qa_service.create_question(db=db, obj_in=question_in)
    
    return question


@router.get("/questions/{question_id}", response_model=QuestionDetail)
def get_question(
    *,
    db: Session = Depends(get_db),
    question_id: int,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取问题详情
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        current_user: 当前用户
        
    Returns:
        问题详情
    """
    question = qa_service.get_question_with_details(db=db, question_id=question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问题不存在"
        )
    
    # 检查是否为会员内容，如果是则检查用户权限
    if question.is_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    # 增加问题浏览量
    qa_service.increment_question_view(db=db, db_obj=question)
    
    return question


@router.put("/questions/{question_id}", response_model=Question)
def update_question(
    *,
    db: Session = Depends(get_db),
    question_id: int,
    question_in: QuestionUpdate,
    current_user: User = Depends(get_current_active_user),
):
    """
    更新问题
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        question_in: 问题更新模式
        current_user: 当前用户
        
    Returns:
        更新后的问题
    """
    question = qa_service.get_question_by_id(db=db, question_id=question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问题不存在"
        )
    
    # 检查权限：只有作者或管理员可以更新问题
    if question.author_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 更新问题
    question = qa_service.update_question(db=db, db_obj=question, obj_in=question_in)
    
    return question


@router.delete("/questions/{question_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_question(
    *,
    db: Session = Depends(get_db),
    question_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    删除问题
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        current_user: 当前用户
    """
    question = qa_service.get_question_by_id(db=db, question_id=question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问题不存在"
        )
    
    # 检查权限：只有作者或管理员可以删除问题
    if question.author_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 删除问题
    qa_service.delete_question(db=db, question_id=question_id)


# 回答相关路由
@router.post("/questions/{question_id}/answers/", response_model=Answer)
def create_answer(
    *,
    db: Session = Depends(get_db),
    question_id: int,
    answer_in: AnswerCreate,
    current_user: User = Depends(get_current_active_user),
):
    """
    创建回答
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        answer_in: 回答创建模式
        current_user: 当前用户
        
    Returns:
        创建的回答
    """
    # 检查问题是否存在
    question = qa_service.get_question_by_id(db=db, question_id=question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问题不存在"
        )
    
    # 设置回答数据
    answer_in_data = answer_in.dict()
    answer_in_data["author_id"] = current_user.id
    answer_in_data["question_id"] = question_id
    
    # 创建回答
    answer = qa_service.create_answer(db=db, obj_in=answer_in)
    
    return answer


@router.get("/answers/{answer_id}", response_model=AnswerDetail)
def get_answer(
    *,
    db: Session = Depends(get_db),
    answer_id: int,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取回答详情
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        current_user: 当前用户
        
    Returns:
        回答详情
    """
    answer = qa_service.get_answer_by_id(db=db, answer_id=answer_id)
    if not answer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="回答不存在"
        )
    
    # 检查问题是否为会员内容，如果是则检查用户权限
    question = qa_service.get_question_by_id(db=db, question_id=answer.question_id)
    if question and question.is_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    return answer


@router.put("/answers/{answer_id}", response_model=Answer)
def update_answer(
    *,
    db: Session = Depends(get_db),
    answer_id: int,
    answer_in: AnswerUpdate,
    current_user: User = Depends(get_current_active_user),
):
    """
    更新回答
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        answer_in: 回答更新模式
        current_user: 当前用户
        
    Returns:
        更新后的回答
    """
    answer = qa_service.get_answer_by_id(db=db, answer_id=answer_id)
    if not answer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="回答不存在"
        )
    
    # 检查权限：只有作者或管理员可以更新回答
    if answer.author_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 如果是问题作者或管理员，可以设置是否接受该回答
    question = qa_service.get_question_by_id(db=db, question_id=answer.question_id)
    if "is_accepted" in answer_in.dict(exclude_unset=True):
        if current_user.id != question.author_id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有问题作者或管理员可以接受回答"
            )
        
        # 如果接受回答，则调用专门的方法
        if answer_in.is_accepted:
            qa_service.accept_answer(db=db, answer_id=answer_id, question_id=answer.question_id)
            answer = qa_service.get_answer_by_id(db=db, answer_id=answer_id)
            return answer
    
    # 更新回答
    answer = qa_service.update_answer(db=db, db_obj=answer, obj_in=answer_in)
    
    return answer


@router.delete("/answers/{answer_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_answer(
    *,
    db: Session = Depends(get_db),
    answer_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    删除回答
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        current_user: 当前用户
    """
    answer = qa_service.get_answer_by_id(db=db, answer_id=answer_id)
    if not answer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="回答不存在"
        )
    
    # 检查权限：只有作者、问题作者或管理员可以删除回答
    question = qa_service.get_question_by_id(db=db, question_id=answer.question_id)
    if (answer.author_id != current_user.id and 
        question.author_id != current_user.id and 
        not current_user.is_superuser):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 删除回答
    qa_service.delete_answer(db=db, answer_id=answer_id)


@router.post("/answers/{answer_id}/vote/{vote_type}", response_model=Answer)
def vote_answer(
    *,
    db: Session = Depends(get_db),
    answer_id: int,
    vote_type: str,
    current_user: User = Depends(get_current_active_user),
):
    """
    对回答进行投票
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        vote_type: 投票类型（upvote或downvote）
        current_user: 当前用户
        
    Returns:
        投票后的回答
    """
    # 检查回答是否存在
    answer = qa_service.get_answer_by_id(db=db, answer_id=answer_id)
    if not answer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="回答不存在"
        )
    
    # 检查投票类型是否有效
    if vote_type not in ["upvote", "downvote"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的投票类型"
        )
    
    # 进行投票
    answer = qa_service.vote_answer(db=db, answer_id=answer_id, vote_type=vote_type)
    
    return answer


# 评论相关路由
@router.post("/answers/{answer_id}/comments/", response_model=Comment)
def create_comment(
    *,
    db: Session = Depends(get_db),
    answer_id: int,
    comment_in: CommentCreate,
    current_user: User = Depends(get_current_active_user),
):
    """
    创建评论
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        comment_in: 评论创建模式
        current_user: 当前用户
        
    Returns:
        创建的评论
    """
    # 检查回答是否存在
    answer = qa_service.get_answer_by_id(db=db, answer_id=answer_id)
    if not answer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="回答不存在"
        )
    
    # 设置评论数据
    comment_in_data = comment_in.dict()
    comment_in_data["author_id"] = current_user.id
    comment_in_data["answer_id"] = answer_id
    
    # 创建评论
    comment = qa_service.create_comment(db=db, obj_in=comment_in)
    
    return comment


@router.get("/answers/{answer_id}/comments/", response_model=List[CommentDetail])
def get_comments(
    *,
    db: Session = Depends(get_db),
    answer_id: int,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    获取回答的评论列表
    
    Args:
        db: 数据库会话
        answer_id: 回答ID
        current_user: 当前用户
        
    Returns:
        评论列表
    """
    # 检查回答是否存在
    answer = qa_service.get_answer_by_id(db=db, answer_id=answer_id)
    if not answer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="回答不存在"
        )
    
    # 检查问题是否为会员内容，如果是则检查用户权限
    question = qa_service.get_question_by_id(db=db, question_id=answer.question_id)
    if question and question.is_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    # 获取评论列表
    comments = qa_service.get_comments_by_answer(db=db, answer_id=answer_id)
    
    return comments


@router.delete("/comments/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_comment(
    *,
    db: Session = Depends(get_db),
    comment_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    删除评论
    
    Args:
        db: 数据库会话
        comment_id: 评论ID
        current_user: 当前用户
    """
    # 检查评论是否存在
    comment = qa_service.get_comment_by_id(db=db, comment_id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在"
        )
    
    # 检查权限：只有评论作者或管理员可以删除评论
    if comment.author_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 删除评论
    qa_service.delete_comment(db=db, comment_id=comment_id)


# 标签相关路由
@router.get("/tags/", response_model=List[Tag])
def get_tags(
    db: Session = Depends(get_db),
):
    """
    获取所有标签
    
    Args:
        db: 数据库会话
        
    Returns:
        标签列表
    """
    tags = qa_service.get_all_tags(db=db)
    return tags


@router.post("/tags/", response_model=Tag)
def create_tag(
    *,
    db: Session = Depends(get_db),
    tag_in: TagCreate,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    创建标签（仅管理员）
    
    Args:
        db: 数据库会话
        tag_in: 标签创建模式
        current_user: 当前用户（必须是管理员）
        
    Returns:
        创建的标签
    """
    # 检查标签是否已存在
    existing_tag = qa_service.get_tag_by_name(db=db, name=tag_in.name)
    if existing_tag:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="标签已存在"
        )
    
    # 创建标签
    tag = qa_service.create_tag(db=db, obj_in=tag_in)
    
    return tag


@router.put("/tags/{tag_id}", response_model=Tag)
def update_tag(
    *,
    db: Session = Depends(get_db),
    tag_id: int,
    tag_in: TagUpdate,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    更新标签（仅管理员）
    
    Args:
        db: 数据库会话
        tag_id: 标签ID
        tag_in: 标签更新模式
        current_user: 当前用户（必须是管理员）
        
    Returns:
        更新后的标签
    """
    # 检查标签是否存在
    tag = qa_service.get_tag_by_id(db=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 如果更新名称，检查新名称是否已存在
    if tag_in.name and tag_in.name != tag.name:
        existing_tag = qa_service.get_tag_by_name(db=db, name=tag_in.name)
        if existing_tag:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="标签名称已存在"
            )
    
    # 更新标签
    tag = qa_service.update_tag(db=db, db_obj=tag, obj_in=tag_in)
    
    return tag


@router.delete("/tags/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_tag(
    *,
    db: Session = Depends(get_db),
    tag_id: int,
    current_user: User = Depends(get_current_active_superuser),
):
    """
    删除标签（仅管理员）
    
    Args:
        db: 数据库会话
        tag_id: 标签ID
        current_user: 当前用户（必须是管理员）
    """
    # 检查标签是否存在
    tag = qa_service.get_tag_by_id(db=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 删除标签
    qa_service.delete_tag(db=db, tag_id=tag_id)


# 收藏相关路由
@router.post("/questions/{question_id}/favorite", response_model=Favorite)
def add_question_to_favorites(
    *,
    db: Session = Depends(get_db),
    question_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    将问题添加到收藏
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        current_user: 当前用户
        
    Returns:
        收藏对象
    """
    # 检查问题是否存在
    question = qa_service.get_question_by_id(db=db, question_id=question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问题不存在"
        )
    
    # 添加到收藏
    favorite = qa_service.add_to_favorites(db=db, user_id=current_user.id, question_id=question_id)
    
    return favorite


@router.delete("/questions/{question_id}/favorite", status_code=status.HTTP_204_NO_CONTENT)
def remove_question_from_favorites(
    *,
    db: Session = Depends(get_db),
    question_id: int,
    current_user: User = Depends(get_current_active_user),
):
    """
    将问题从收藏中移除
    
    Args:
        db: 数据库会话
        question_id: 问题ID
        current_user: 当前用户
    """
    # 检查问题是否存在
    question = qa_service.get_question_by_id(db=db, question_id=question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问题不存在"
        )
    
    # 从收藏中移除
    qa_service.remove_from_favorites(db=db, user_id=current_user.id, question_id=question_id)


@router.get("/favorites/", response_model=List[Favorite])
def get_user_favorites(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    获取用户的收藏列表
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        收藏列表
    """
    favorites = qa_service.get_user_favorites(db=db, user_id=current_user.id)
    
    return favorites 