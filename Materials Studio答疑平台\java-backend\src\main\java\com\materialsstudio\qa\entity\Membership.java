package com.materialsstudio.qa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员实体类
 */
@Data
@TableName("membership")
public class Membership implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 会员ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 会员等级：standard-标准会员，premium-高级会员
     */
    private String level;
    
    /**
     * 会员等级名称
     */
    private String levelName;
    
    /**
     * 会员状态：0-过期，1-有效
     */
    private Integer isActive;
    
    /**
     * 开始时间
     */
    private LocalDateTime startDate;
    
    /**
     * 结束时间
     */
    private LocalDateTime endDate;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
} 