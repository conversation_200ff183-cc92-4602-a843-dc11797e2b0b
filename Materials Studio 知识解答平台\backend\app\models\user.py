from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Integer, String, ForeignKey, Text
from sqlalchemy.orm import relationship

from ..db.session import Base


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    profile = relationship("UserProfile", back_populates="user", uselist=False)
    membership = relationship("Membership", back_populates="user", uselist=False)
    questions = relationship("Question", back_populates="author")
    answers = relationship("Answer", back_populates="author")
    comments = relationship("Comment", back_populates="author")
    resources = relationship("Resource", back_populates="uploader")
    favorites = relationship("Favorite", back_populates="user")
    search_histories = relationship("SearchHistory", back_populates="user")
    notifications = relationship("Notification", back_populates="user")


class UserProfile(Base):
    """用户个人资料模型"""
    __tablename__ = "user_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    full_name = Column(String(100))
    avatar = Column(String(255))
    bio = Column(Text)
    organization = Column(String(100))
    position = Column(String(100))
    website = Column(String(255))
    social_link = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="profile")


class Membership(Base):
    """会员信息模型"""
    __tablename__ = "memberships"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    is_premium = Column(Boolean, default=False)
    level = Column(Integer, default=0)  # 会员等级：0-免费用户, 1-初级会员, 2-高级会员, 3-专业会员
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="membership")


class Favorite(Base):
    """用户收藏"""
    __tablename__ = "favorites"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    question_id = Column(Integer, ForeignKey("questions.id", ondelete="CASCADE"), nullable=True)
    resource_id = Column(Integer, ForeignKey("resources.id", ondelete="CASCADE"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="favorites")
    question = relationship("Question", back_populates="favorites")
    resource = relationship("Resource", back_populates="favorites") 