from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.deps import get_current_active_user, get_current_active_superuser
from app.db.session import get_db
from app.models.user import User
from app.schemas.user import (
    User as UserSchema, 
    UserUpdate, 
    UserProfile as UserProfileSchema,
    UserProfileUpdate,
    Membership as MembershipSchema,
    MembershipUpdate
)
from app.services import user_service

router = APIRouter()


@router.get("/", response_model=List[UserSchema])
def get_users(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_superuser),
) -> Any:
    """
    获取用户列表（仅超级管理员）
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        current_user: 当前用户（必须是超级管理员）
        
    Returns:
        List[User]: 用户列表
    """
    users = db.query(User).offset(skip).limit(limit).all()
    return users


@router.get("/{user_id}", response_model=UserSchema)
def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取用户信息
    
    Args:
        user_id: 用户ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        User: 用户信息
        
    Raises:
        HTTPException: 如果用户不存在
    """
    # 只能查看自己的信息或者超级管理员可以查看任何人的信息
    if user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限",
        )
    
    user = user_service.get_by_id(db, user_id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )
    return user


@router.put("/me", response_model=UserSchema)
def update_user_me(
    *,
    db: Session = Depends(get_db),
    user_in: UserUpdate,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    更新当前用户信息
    
    Args:
        db: 数据库会话
        user_in: 用户更新模式
        current_user: 当前用户
        
    Returns:
        User: 更新后的用户信息
    """
    user = user_service.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.get("/me/profile", response_model=UserProfileSchema)
def get_user_profile_me(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取当前用户的个人资料
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        UserProfile: 用户个人资料
        
    Raises:
        HTTPException: 如果用户个人资料不存在
    """
    profile = user_service.get_profile(db, user_id=current_user.id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户个人资料不存在",
        )
    return profile


@router.put("/me/profile", response_model=UserProfileSchema)
def update_user_profile_me(
    *,
    db: Session = Depends(get_db),
    profile_in: UserProfileUpdate,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    更新当前用户的个人资料
    
    Args:
        db: 数据库会话
        profile_in: 用户个人资料更新模式
        current_user: 当前用户
        
    Returns:
        UserProfile: 更新后的用户个人资料
        
    Raises:
        HTTPException: 如果用户个人资料不存在
    """
    profile = user_service.get_profile(db, user_id=current_user.id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户个人资料不存在",
        )
    
    profile = user_service.update_profile(db, db_obj=profile, obj_in=profile_in)
    return profile


@router.get("/me/membership", response_model=MembershipSchema)
def get_user_membership_me(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取当前用户的会员信息
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        Membership: 用户会员信息
        
    Raises:
        HTTPException: 如果用户会员信息不存在
    """
    membership = user_service.get_membership(db, user_id=current_user.id)
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户会员信息不存在",
        )
    return membership


@router.put("/{user_id}/membership", response_model=MembershipSchema)
def update_user_membership(
    *,
    user_id: int,
    db: Session = Depends(get_db),
    membership_in: MembershipUpdate,
    current_user: User = Depends(get_current_active_superuser),
) -> Any:
    """
    更新用户的会员信息（仅超级管理员）
    
    Args:
        user_id: 用户ID
        db: 数据库会话
        membership_in: 用户会员信息更新模式
        current_user: 当前用户（必须是超级管理员）
        
    Returns:
        Membership: 更新后的用户会员信息
        
    Raises:
        HTTPException: 如果用户或用户会员信息不存在
    """
    user = user_service.get_by_id(db, user_id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )
    
    membership = user_service.get_membership(db, user_id=user_id)
    if not membership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户会员信息不存在",
        )
    
    membership = user_service.update_membership(db, db_obj=membership, obj_in=membership_in)
    return membership 