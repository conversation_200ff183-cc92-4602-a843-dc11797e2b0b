{"name": "materials-studio-platform-backend", "version": "1.0.0", "description": "Materials Studio答疑平台后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js", "db:reset": "node src/database/reset.js", "db:create": "node src/database/create.js"}, "keywords": ["materials-studio", "api", "nodejs", "express", "mysql", "redis"], "author": "Materials Studio Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^6.1.5", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.7.0", "express-validator": "^6.15.0", "mysql2": "^3.3.3", "redis": "^4.6.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.3", "socket.io": "^4.6.2", "dotenv": "^16.1.4", "joi": "^17.9.2", "moment": "^2.29.4", "uuid": "^9.0.0", "winston": "^3.9.0", "winston-daily-rotate-file": "^4.7.1", "elasticsearch": "^16.7.3", "sharp": "^0.32.1", "node-cron": "^3.0.2", "express-slow-down": "^1.6.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.42.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/database/**/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}