package com.materialsstudio.qa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 回答实体类
 */
@Data
@TableName("answer")
public class Answer implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 回答ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 问题ID
     */
    private Long questionId;
    
    /**
     * 回答内容
     */
    private String content;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 是否被采纳：0-否，1-是
     */
    private Integer isAccepted;
    
    /**
     * 是否为会员专属：0-否，1-是
     */
    private Integer isPremium;
    
    /**
     * 点赞数量
     */
    private Integer votes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
} 