server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: materials-studio-qa-platform
  profiles:
    active: dev # 默认使用开发环境配置，部署时改为prod

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# 开发环境JWT配置
jwt:
  secret: materials-studio-qa-platform-jwt-secret-key-dev
  expiration: 86400000  # 过期时间：1天（毫秒）
  token-prefix: "Bearer "
  header: "Authorization"

# 开发环境文件存储
file:
  upload-dir: ./uploads/dev/
  allowed-file-extensions: jpg,jpeg,png,gif,doc,docx,pdf,ppt,pptx,mp4,webm

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  # 数据库配置 - 生产环境
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:password}
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
  
  # Redis配置 - 生产环境
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 16
        max-wait: -1
        max-idle: 16
        min-idle: 8
  
  # 文件上传配置 - 生产环境
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 200MB

# 生产环境JWT配置
jwt:
  # 使用环境变量存储密钥，提高安全性
  secret: ${JWT_SECRET:materials-studio-qa-platform-jwt-secret-key-prod}
  expiration: 86400000  # 过期时间：1天（毫秒）
  token-prefix: "Bearer "
  header: "Authorization"

# 生产环境文件存储
file:
  upload-dir: ${FILE_UPLOAD_DIR:/app/uploads/}
  allowed-file-extensions: jpg,jpeg,png,gif,doc,docx,pdf,ppt,pptx,mp4,webm

---
# MyBatis-Plus配置（通用配置）
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  type-aliases-package: com.materialsstudio.qa.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0 