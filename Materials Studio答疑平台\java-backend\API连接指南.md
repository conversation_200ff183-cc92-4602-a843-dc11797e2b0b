# Materials Studio 答疑平台 API 连接指南

本指南帮助前端开发人员理解如何连接和使用后端 API。

## 基本信息

- 基础 URL: `http://localhost:8080/api/v1`
- 内容类型: `application/json`
- 授权方式: Bearer <PERSON>ken (`Authorization: Bearer {token}`)

## 认证流程

1. **登录获取令牌**

```
POST /auth/login
Content-Type: application/json

{
  "username": "用户名或邮箱",
  "password": "密码"
}
```

成功响应:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 86400,
    "userId": 1,
    "username": "admin",
    "role": "admin"
  }
}
```

2. **使用令牌访问受保护资源**

```
GET /questions
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

## 用户 API

### 获取当前用户信息

```
GET /auth/me
Authorization: Bearer {token}
```

## 问题 API

### 获取问题列表

```
GET /questions?page=1&size=10&keyword=关键字&tagId=1&authorId=1&isPremium=false&status=0&sortField=createTime&sortOrder=desc
```

参数说明:
- `page`: 页码，默认 1
- `size`: 每页数量，默认 10
- `keyword`: 搜索关键字
- `tagId`: 标签 ID
- `authorId`: 作者 ID
- `isPremium`: 是否会员专属
- `status`: 状态，0-未解决，1-已解决
- `sortField`: 排序字段，默认 "create_time"
- `sortOrder`: 排序方式，默认 "desc"

### 获取问题详情

```
GET /questions/{id}
```

### 发布问题

```
POST /questions
Content-Type: application/json
Authorization: Bearer {token}

{
  "title": "问题标题",
  "content": "问题内容",
  "isPremium": false,
  "tagIds": [1, 2, 3]
}
```

## 回答 API

### 获取问题的回答列表

```
GET /answers/question/{questionId}
```

### 发布回答

```
POST /answers
Content-Type: application/json
Authorization: Bearer {token}

{
  "questionId": 1,
  "content": "回答内容",
  "isPremium": false
}
```

### 采纳回答

```
PUT /answers/{id}/accept
Authorization: Bearer {token}
```

### 点赞回答

```
PUT /answers/{id}/vote
Authorization: Bearer {token}
```

## 标签 API

### 获取所有标签

```
GET /tags
```

### 获取热门标签

```
GET /tags/hot?limit=10
```

参数说明:
- `limit`: 获取数量，默认 10

### 获取问题的标签列表

```
GET /tags/question/{questionId}
```

## 响应格式

所有 API 响应均使用以下统一格式:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

- `success`: 操作是否成功
- `code`: 状态码，200 表示成功，其他表示失败
- `message`: 操作消息
- `data`: 返回的数据

## 错误处理

当操作失败时，API 会返回错误信息:

```json
{
  "success": false,
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

常见错误码:
- `400`: 请求参数错误
- `401`: 未授权，需要登录
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

## 测试账户

系统初始化后默认提供以下账户用于测试:

- 管理员: 账号 `admin`, 密码 `123456`
- 会员用户: 账号 `member`, 密码 `123456`
- 普通用户: 账号 `user`, 密码 `123456` 