import logging
from sqlalchemy.orm import Session

from app.config.settings import settings
from app.core.security import get_password_hash
from app.db.session import Base, engine
from app.models import user, qa, content  # 导入模型以确保创建表

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db(db: Session) -> None:
    """
    初始化数据库
    
    - 创建所有表
    - 创建初始超级用户
    - 创建初始数据
    
    Args:
        db: 数据库会话
    """
    # 创建表
    Base.metadata.create_all(bind=engine)
    
    # 检查是否有管理员用户
    from app.models.user import User
    
    admin_user = db.query(User).filter(User.email == settings.FIRST_SUPERUSER).first()
    
    if not admin_user:
        # 创建超级用户
        admin_user = User(
            email=settings.FIRST_SUPERUSER,
            username=settings.FIRST_SUPERUSER_USERNAME,
            hashed_password=get_password_hash(settings.FIRST_SUPERUSER_PASSWORD),
            is_superuser=True,
            is_active=True,
        )
        db.add(admin_user)
        db.commit()
        logger.info("创建初始超级用户成功")
    
    # 创建初始数据
    # 可以在这里添加初始数据的创建
    
    logger.info("数据库初始化完成") 