package com.materialsstudio.qa.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 问题发布DTO
 */
@Data
public class QuestionPostDTO {
    
    /**
     * 问题标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(min = 5, max = 100, message = "标题长度在5-100之间")
    private String title;
    
    /**
     * 问题内容
     */
    @NotBlank(message = "内容不能为空")
    @Size(min = 10, message = "内容长度不能少于10个字符")
    private String content;
    
    /**
     * 是否为会员专属
     */
    @NotNull(message = "请指定是否为会员专属")
    private Boolean isPremium;
    
    /**
     * 标签ID列表
     */
    @Size(min = 1, max = 5, message = "标签数量在1-5之间")
    private List<Long> tagIds;
} 