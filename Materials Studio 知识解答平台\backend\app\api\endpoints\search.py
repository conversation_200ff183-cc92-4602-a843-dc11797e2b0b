from typing import Any, List, Optional

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from app.core.deps import get_current_active_user, get_db, check_premium_content_access
from app.models.user import User
from app.schemas.content import SearchResult
from app.services import qa_service, content_service

router = APIRouter()


@router.get("/", response_model=SearchResult)
def search(
    *,
    db: Session = Depends(get_db),
    q: str = Query(..., description="搜索关键词"),
    skip: int = 0,
    limit: int = 10,
    search_type: str = "all",  # all, question, resource, glossary
    include_premium: bool = False,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    综合搜索
    
    可以搜索问题、资源和术语词典
    
    Args:
        db: 数据库会话
        q: 搜索关键词
        skip: 跳过的记录数
        limit: 返回的记录数
        search_type: 搜索类型（all, question, resource, glossary）
        include_premium: 是否包含会员内容
        current_user: 当前用户
        
    Returns:
        搜索结果
    """
    # 检查是否包含会员内容，如果包含则检查用户权限
    if include_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    # 保存搜索历史
    if current_user:
        content_service.create_search_history(
            db=db,
            obj_in={"user_id": current_user.id, "query": q, "search_type": search_type}
        )
    
    result = SearchResult(
        questions=[],
        resources=[],
        glossary=[],
        total_count=0
    )
    
    # 搜索问题
    if search_type in ["all", "question"]:
        questions = qa_service.get_questions(
            db=db,
            skip=skip,
            limit=limit,
            search=q,
            only_premium=include_premium
        )
        questions_count = qa_service.get_questions_count(
            db=db,
            search=q,
            only_premium=include_premium
        )
        result.questions = questions
        result.total_count += questions_count
    
    # 搜索资源
    if search_type in ["all", "resource"]:
        resources = content_service.get_resources(
            db=db,
            skip=skip,
            limit=limit,
            search=q,
            only_premium=include_premium
        )
        resources_count = content_service.get_resources_count(
            db=db,
            search=q,
            only_premium=include_premium
        )
        result.resources = resources
        result.total_count += resources_count
    
    # 搜索术语词典
    if search_type in ["all", "glossary"]:
        glossary = content_service.get_all_glossary(
            db=db,
            skip=skip,
            limit=limit,
            search=q
        )
        # 暂时无法获取术语词典总数，所以直接使用列表长度
        result.glossary = glossary
        result.total_count += len(glossary)
    
    return result


@router.get("/questions", response_model=List)
def search_questions(
    *,
    db: Session = Depends(get_db),
    q: str = Query(..., description="搜索关键词"),
    skip: int = 0,
    limit: int = 10,
    tag_id: Optional[int] = None,
    only_solved: bool = False,
    include_premium: bool = False,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    搜索问题
    
    Args:
        db: 数据库会话
        q: 搜索关键词
        skip: 跳过的记录数
        limit: 返回的记录数
        tag_id: 标签ID过滤
        only_solved: 是否只返回已解决的问题
        include_premium: 是否包含会员内容
        current_user: 当前用户
        
    Returns:
        问题列表
    """
    # 检查是否包含会员内容，如果包含则检查用户权限
    if include_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    # 保存搜索历史
    if current_user:
        content_service.create_search_history(
            db=db,
            obj_in={"user_id": current_user.id, "query": q, "search_type": "question"}
        )
    
    questions = qa_service.get_questions(
        db=db,
        skip=skip,
        limit=limit,
        search=q,
        tag_id=tag_id,
        only_premium=include_premium,
        only_solved=only_solved
    )
    
    return questions


@router.get("/resources", response_model=List)
def search_resources(
    *,
    db: Session = Depends(get_db),
    q: str = Query(..., description="搜索关键词"),
    skip: int = 0,
    limit: int = 10,
    category_id: Optional[int] = None,
    file_type: Optional[str] = None,
    include_premium: bool = False,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    搜索资源
    
    Args:
        db: 数据库会话
        q: 搜索关键词
        skip: 跳过的记录数
        limit: 返回的记录数
        category_id: 分类ID过滤
        file_type: 文件类型过滤
        include_premium: 是否包含会员内容
        current_user: 当前用户
        
    Returns:
        资源列表
    """
    # 检查是否包含会员内容，如果包含则检查用户权限
    if include_premium:
        check_premium_content_access(current_user, is_premium_content=True)
    
    # 保存搜索历史
    if current_user:
        content_service.create_search_history(
            db=db,
            obj_in={"user_id": current_user.id, "query": q, "search_type": "resource"}
        )
    
    resources = content_service.get_resources(
        db=db,
        skip=skip,
        limit=limit,
        search=q,
        category_id=category_id,
        file_type=file_type,
        only_premium=include_premium
    )
    
    return resources


@router.get("/glossary", response_model=List)
def search_glossary(
    *,
    db: Session = Depends(get_db),
    q: str = Query(..., description="搜索关键词"),
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    current_user: Optional[User] = Depends(get_current_active_user),
):
    """
    搜索术语词典
    
    Args:
        db: 数据库会话
        q: 搜索关键词
        skip: 跳过的记录数
        limit: 返回的记录数
        category: 分类过滤
        current_user: 当前用户
        
    Returns:
        术语词典列表
    """
    # 保存搜索历史
    if current_user:
        content_service.create_search_history(
            db=db,
            obj_in={"user_id": current_user.id, "query": q, "search_type": "glossary"}
        )
    
    glossary = content_service.get_all_glossary(
        db=db,
        skip=skip,
        limit=limit,
        search=q,
        category=category
    )
    
    return glossary


@router.get("/history", response_model=List)
def get_search_history(
    *,
    db: Session = Depends(get_db),
    limit: int = 10,
    current_user: User = Depends(get_current_active_user),
):
    """
    获取当前用户的搜索历史
    
    Args:
        db: 数据库会话
        limit: 返回的记录数
        current_user: 当前用户
        
    Returns:
        搜索历史列表
    """
    history = content_service.get_user_search_history(
        db=db,
        user_id=current_user.id,
        limit=limit
    )
    
    return history


@router.delete("/history", status_code=204)
def clear_search_history(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    清空当前用户的搜索历史
    
    Args:
        db: 数据库会话
        current_user: 当前用户
    """
    content_service.clear_user_search_history(db=db, user_id=current_user.id) 