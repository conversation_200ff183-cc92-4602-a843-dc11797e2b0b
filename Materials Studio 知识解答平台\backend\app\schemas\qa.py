from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from .user import User


# 标签基础模式
class TagBase(BaseModel):
    """标签基础模式"""
    name: str
    color: Optional[str] = "#6c757d"
    description: Optional[str] = None


# 创建标签请求模式
class TagCreate(TagBase):
    """创建标签请求模式"""
    pass


# 更新标签请求模式
class TagUpdate(TagBase):
    """更新标签请求模式"""
    name: Optional[str] = None


# 返回给API的标签模式
class Tag(TagBase):
    """返回给API的标签模式"""
    id: int
    created_at: datetime
    
    class Config:
        orm_mode = True


# 附件基础模式
class AttachmentBase(BaseModel):
    """附件基础模式"""
    filename: str
    file_path: str
    file_size: Optional[int] = None
    file_type: Optional[str] = None


# 问题附件创建模式
class QuestionAttachmentCreate(AttachmentBase):
    """问题附件创建模式"""
    question_id: int


# 回答附件创建模式
class AnswerAttachmentCreate(AttachmentBase):
    """回答附件创建模式"""
    answer_id: int


# 返回给API的附件模式
class Attachment(AttachmentBase):
    """返回给API的附件模式"""
    id: int
    created_at: datetime
    
    class Config:
        orm_mode = True


# 评论基础模式
class CommentBase(BaseModel):
    """评论基础模式"""
    content: str


# 创建评论请求模式
class CommentCreate(CommentBase):
    """创建评论请求模式"""
    answer_id: int


# 返回给API的评论模式
class Comment(CommentBase):
    """返回给API的评论模式"""
    id: int
    author_id: int
    answer_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 评论详情模式（包含作者信息）
class CommentDetail(Comment):
    """评论详情模式"""
    from app.schemas.user import User
    
    author: User


# 回答基础模式
class AnswerBase(BaseModel):
    """回答基础模式"""
    content: str


# 创建回答请求模式
class AnswerCreate(AnswerBase):
    """创建回答请求模式"""
    question_id: int


# 更新回答请求模式
class AnswerUpdate(BaseModel):
    """更新回答请求模式"""
    content: Optional[str] = None
    is_accepted: Optional[bool] = None


# 返回给API的回答模式
class Answer(AnswerBase):
    """返回给API的回答模式"""
    id: int
    author_id: int
    question_id: int
    is_accepted: bool
    vote_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 回答详情模式（包含作者信息）
class AnswerDetail(Answer):
    """回答详情模式"""
    from app.schemas.user import User
    
    author: User
    comments_count: int = 0


# 问题基础模式
class QuestionBase(BaseModel):
    """问题基础模式"""
    title: str
    content: str
    is_premium: bool = False


# 创建问题请求模式
class QuestionCreate(QuestionBase):
    """创建问题请求模式"""
    tags: Optional[List[int]] = []


# 更新问题请求模式
class QuestionUpdate(BaseModel):
    """更新问题请求模式"""
    title: Optional[str] = None
    content: Optional[str] = None
    is_premium: Optional[bool] = None
    is_solved: Optional[bool] = None
    tags: Optional[List[int]] = None


# 返回给API的问题模式
class Question(QuestionBase):
    """返回给API的问题模式"""
    id: int
    author_id: int
    is_solved: bool
    view_count: int
    created_at: datetime
    updated_at: datetime
    tags: List[Tag] = []
    
    class Config:
        orm_mode = True


# 问题详情模式（包含作者信息）
class QuestionDetail(Question):
    """问题详情模式"""
    author: User
    answers_count: int = 0


# 问题列表中的简化问题模式
class QuestionSummary(BaseModel):
    id: int
    title: str
    author: Optional[User] = None
    views: int
    is_solved: bool
    is_premium: bool
    created_at: datetime
    tags: List[Tag] = []
    answer_count: int

    class Config:
        orm_mode = True


# 收藏基础模式
class FavoriteBase(BaseModel):
    user_id: int
    question_id: Optional[int] = None
    resource_id: Optional[int] = None


# 创建收藏时使用的模式
class FavoriteCreate(FavoriteBase):
    pass


# 数据库中存储的收藏模式
class FavoriteInDBBase(FavoriteBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True


# 返回给API的收藏模式
class Favorite(FavoriteInDBBase):
    pass 