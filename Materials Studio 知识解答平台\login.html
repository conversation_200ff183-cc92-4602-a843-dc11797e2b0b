<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录/注册 - Materials Studio 知识解答平台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .login-container {
            max-width: 500px;
            margin: 60px auto;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            background-color: #fff;
        }
        
        .form-tab-content {
            display: none;
        }
        
        .form-tab-content.active {
            display: block;
        }
        
        .form-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .form-tab {
            padding: 12px 24px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 500;
            color: #6c757d;
            background: none;
            border: none;
            outline: none;
            position: relative;
        }
        
        .form-tab.active {
            color: #0d6efd;
        }
        
        .form-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: #0d6efd;
        }
        
        .social-login {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .social-login button {
            margin: 0 10px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: #6c757d;
        }
        
        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background-color: #dee2e6;
        }
        
        .divider span {
            padding: 0 10px;
        }
        
        .form-label {
            font-weight: 500;
            margin-bottom: 6px;
        }
        
        .form-control {
            padding: 12px;
            border-radius: 8px;
        }
        
        .btn-primary {
            padding: 12px;
            font-weight: 500;
            border-radius: 8px;
        }
        
        .auth-message {
            display: none;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .auth-message.success {
            display: block;
            background-color: #d1e7dd;
            color: #0f5132;
            border: 1px solid #badbcc;
        }
        
        .auth-message.error {
            display: block;
            background-color: #f8d7da;
            color: #842029;
            border: 1px solid #f5c2c7;
        }
        
        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }
        
        .forgot-link {
            display: block;
            text-align: right;
            margin-top: 8px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">Materials Studio 知识解答平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="qa.html">问答</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.html">社区</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html">资源</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container">
        <div class="login-container">
            <div id="auth-message" class="auth-message"></div>
            
            <div class="form-tabs">
                <button class="form-tab active" data-tab="login">登录</button>
                <button class="form-tab" data-tab="register">注册</button>
            </div>
            
            <!-- 登录表单 -->
            <div id="login-form" class="form-tab-content active">
                <form id="login-form-element">
                    <div class="mb-3">
                        <label for="login-username" class="form-label">用户名或邮箱</label>
                        <input type="text" class="form-control" id="login-username" name="username" required>
                    </div>
                    <div class="mb-3 position-relative">
                        <label for="login-password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="login-password" name="password" required>
                        <i class="bi bi-eye password-toggle" data-target="login-password"></i>
                        <a href="#" class="forgot-link" id="forgot-password-link">忘记密码？</a>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember-me">
                        <label class="form-check-label" for="remember-me">记住我</label>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">登录</button>
                </form>
                
                <div class="divider">
                    <span>或者使用第三方账号登录</span>
                </div>
                
                <div class="social-login">
                    <button class="btn btn-outline-primary" title="微信登录">
                        <i class="bi bi-wechat"></i>
                    </button>
                    <button class="btn btn-outline-primary" title="QQ登录">
                        <i class="bi bi-chat-dots-fill"></i>
                    </button>
                    <button class="btn btn-outline-primary" title="微博登录">
                        <i class="bi bi-sina-weibo"></i>
                    </button>
                </div>
            </div>
            
            <!-- 注册表单 -->
            <div id="register-form" class="form-tab-content">
                <form id="register-form-element">
                    <div class="mb-3">
                        <label for="register-username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="register-username" name="username" required minlength="3" maxlength="30">
                        <div class="form-text">用户名长度需在3-30个字符之间，只能包含字母、数字、下划线和连字符</div>
                    </div>
                    <div class="mb-3">
                        <label for="register-email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="register-email" name="email" required>
                    </div>
                    <div class="mb-3 position-relative">
                        <label for="register-password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="register-password" name="password" required minlength="6">
                        <i class="bi bi-eye password-toggle" data-target="register-password"></i>
                        <div class="form-text">密码长度至少为6个字符，必须包含大小写字母和数字</div>
                    </div>
                    <div class="mb-3 position-relative">
                        <label for="register-confirm-password" class="form-label">确认密码</label>
                        <input type="password" class="form-control" id="register-confirm-password" required>
                        <i class="bi bi-eye password-toggle" data-target="register-confirm-password"></i>
                    </div>
                    <div class="mb-3">
                        <label for="register-name" class="form-label">姓名（可选）</label>
                        <input type="text" class="form-control" id="register-name" name="name">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="agree-terms" required>
                        <label class="form-check-label" for="agree-terms">我已阅读并同意<a href="#" target="_blank">用户协议</a>和<a href="#" target="_blank">隐私政策</a></label>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">注册</button>
                </form>
            </div>
            
            <!-- 忘记密码表单 -->
            <div id="forgot-password-form" class="form-tab-content">
                <form id="forgot-password-form-element">
                    <div class="mb-3">
                        <label for="forgot-email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="forgot-email" name="email" required>
                        <div class="form-text">我们将向您的邮箱发送重置密码的链接</div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">发送重置链接</button>
                    <button type="button" class="btn btn-link w-100" id="back-to-login">返回登录</button>
                </form>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2023 Materials Studio 知识解答平台</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-decoration-none me-3">关于我们</a>
                    <a href="#" class="text-decoration-none me-3">联系我们</a>
                    <a href="#" class="text-decoration-none">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 表单切换功能
            const tabs = document.querySelectorAll('.form-tab');
            const tabContents = document.querySelectorAll('.form-tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.dataset.tab;
                    
                    // 激活选项卡
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // 显示相应内容
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    document.getElementById(`${tabId}-form`).classList.add('active');
                    
                    // 清除消息
                    document.getElementById('auth-message').className = 'auth-message';
                    document.getElementById('auth-message').textContent = '';
                });
            });
            
            // 忘记密码链接
            const forgotPasswordLink = document.getElementById('forgot-password-link');
            forgotPasswordLink.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 隐藏所有表单，显示忘记密码表单
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById('forgot-password-form').classList.add('active');
                
                // 取消所有选项卡的活动状态
                tabs.forEach(t => t.classList.remove('active'));
            });
            
            // 返回登录链接
            const backToLoginBtn = document.getElementById('back-to-login');
            backToLoginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 隐藏忘记密码表单，显示登录表单
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById('login-form').classList.add('active');
                
                // 激活登录选项卡
                tabs.forEach(t => t.classList.remove('active'));
                document.querySelector('[data-tab="login"]').classList.add('active');
            });
            
            // 密码显示切换
            const passwordToggles = document.querySelectorAll('.password-toggle');
            passwordToggles.forEach(toggle => {
                toggle.addEventListener('click', () => {
                    const targetId = toggle.dataset.target;
                    const passwordInput = document.getElementById(targetId);
                    
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        toggle.classList.remove('bi-eye');
                        toggle.classList.add('bi-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        toggle.classList.remove('bi-eye-slash');
                        toggle.classList.add('bi-eye');
                    }
                });
            });
            
            // 登录表单提交
            const loginForm = document.getElementById('login-form-element');
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const username = document.getElementById('login-username').value;
                const password = document.getElementById('login-password').value;
                const rememberMe = document.getElementById('remember-me').checked;
                
                try {
                    const user = await auth.login(username, password);
                    
                    // 显示成功消息
                    const messageEl = document.getElementById('auth-message');
                    messageEl.className = 'auth-message success';
                    messageEl.textContent = '登录成功，正在跳转...';
                    
                    // 跳转到首页
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } catch (error) {
                    // 显示错误消息
                    const messageEl = document.getElementById('auth-message');
                    messageEl.className = 'auth-message error';
                    messageEl.textContent = error.message || '登录失败，请检查用户名和密码';
                }
            });
            
            // 注册表单提交
            const registerForm = document.getElementById('register-form-element');
            registerForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const username = document.getElementById('register-username').value;
                const email = document.getElementById('register-email').value;
                const password = document.getElementById('register-password').value;
                const confirmPassword = document.getElementById('register-confirm-password').value;
                const name = document.getElementById('register-name').value;
                const agreeTerms = document.getElementById('agree-terms').checked;
                
                // 基本验证
                if (password !== confirmPassword) {
                    const messageEl = document.getElementById('auth-message');
                    messageEl.className = 'auth-message error';
                    messageEl.textContent = '两次输入的密码不一致';
                    return;
                }
                
                if (!agreeTerms) {
                    const messageEl = document.getElementById('auth-message');
                    messageEl.className = 'auth-message error';
                    messageEl.textContent = '请同意用户协议和隐私政策';
                    return;
                }
                
                try {
                    const userData = {
                        username,
                        email,
                        password,
                        name: name || undefined
                    };
                    
                    const user = await auth.register(userData);
                    
                    // 显示成功消息
                    const messageEl = document.getElementById('auth-message');
                    messageEl.className = 'auth-message success';
                    messageEl.textContent = '注册成功，正在跳转...';
                    
                    // 跳转到首页
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } catch (error) {
                    // 显示错误消息
                    const messageEl = document.getElementById('auth-message');
                    messageEl.className = 'auth-message error';
                    messageEl.textContent = error.message || '注册失败，请稍后再试';
                }
            });
            
            // 忘记密码表单提交
            const forgotPasswordForm = document.getElementById('forgot-password-form-element');
            forgotPasswordForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const email = document.getElementById('forgot-email').value;
                
                try {
                    const response = await fetch(`/api/v1/auth/forgot-password`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        // 显示成功消息
                        const messageEl = document.getElementById('auth-message');
                        messageEl.className = 'auth-message success';
                        messageEl.textContent = '重置密码链接已发送到您的邮箱，请查收';
                        
                        // 返回登录表单
                        setTimeout(() => {
                            tabContents.forEach(content => {
                                content.classList.remove('active');
                            });
                            document.getElementById('login-form').classList.add('active');
                            
                            tabs.forEach(t => t.classList.remove('active'));
                            document.querySelector('[data-tab="login"]').classList.add('active');
                        }, 3000);
                    } else {
                        throw new Error(data.error || '发送重置链接失败');
                    }
                } catch (error) {
                    // 显示错误消息
                    const messageEl = document.getElementById('auth-message');
                    messageEl.className = 'auth-message error';
                    messageEl.textContent = error.message || '发送重置链接失败，请稍后再试';
                }
            });
            
            // 如果用户已登录，重定向到首页
            if (auth.isAuthenticated()) {
                window.location.href = 'index.html';
            }
        });
    </script>
</body>
</html> 