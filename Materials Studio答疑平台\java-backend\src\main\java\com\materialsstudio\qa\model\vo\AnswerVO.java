package com.materialsstudio.qa.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 回答视图对象
 */
@Data
public class AnswerVO {
    
    /**
     * 回答ID
     */
    private Long id;
    
    /**
     * 问题ID
     */
    private Long questionId;
    
    /**
     * 回答内容
     */
    private String content;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 作者用户名
     */
    private String authorName;
    
    /**
     * 作者头像
     */
    private String authorAvatar;
    
    /**
     * 是否被采纳
     */
    private Boolean isAccepted;
    
    /**
     * 是否为会员专属
     */
    private Boolean isPremium;
    
    /**
     * 点赞数量
     */
    private Integer votes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 