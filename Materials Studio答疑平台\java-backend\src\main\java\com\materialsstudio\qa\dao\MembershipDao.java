package com.materialsstudio.qa.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.materialsstudio.qa.entity.Membership;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 会员DAO接口
 */
public interface MembershipDao extends BaseMapper<Membership> {
    
    /**
     * 根据用户ID查询有效会员信息
     *
     * @param userId 用户ID
     * @return 会员信息
     */
    @Select("SELECT * FROM membership WHERE user_id = #{userId} AND is_active = 1 AND deleted = 0 AND end_date > NOW() ORDER BY end_date DESC LIMIT 1")
    Membership selectActiveByUserId(@Param("userId") Long userId);
} 