package com.materialsstudio.qa.common;

/**
 * 响应状态码
 */
public class ResultCode {
    
    /**
     * 成功
     */
    public static final int SUCCESS = 200;
    
    /**
     * 失败
     */
    public static final int FAILURE = 400;
    
    /**
     * 未授权
     */
    public static final int UNAUTHORIZED = 401;
    
    /**
     * 禁止访问
     */
    public static final int FORBIDDEN = 403;
    
    /**
     * 资源不存在
     */
    public static final int NOT_FOUND = 404;
    
    /**
     * 服务器错误
     */
    public static final int ERROR = 500;
    
    /**
     * 参数错误
     */
    public static final int BAD_REQUEST = 400;
    
    /**
     * 业务异常
     */
    public static final int BUSINESS_ERROR = 501;
} 