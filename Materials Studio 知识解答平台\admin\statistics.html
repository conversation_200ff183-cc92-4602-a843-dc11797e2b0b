<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据统计与分析 - Materials Studio 知识解答平台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #0056b3;
            --secondary-color: #004494;
            --border-radius: 10px;
            --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }
        
        .sidebar {
            background-color: #fff;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            padding: 20px;
        }
        
        .sidebar .nav-link {
            color: #333;
            border-radius: 5px;
            margin-bottom: 5px;
            padding: 10px 15px;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(0,86,179,0.1);
        }
        
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
        }
        
        .stat-card {
            transition: all 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .stat-icon.bg-purple {
            background-color: rgba(111, 66, 193, 0.1);
            color: #6f42c1;
        }
        
        .stat-icon.bg-blue {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }
        
        .stat-icon.bg-teal {
            background-color: rgba(32, 201, 151, 0.1);
            color: #20c997;
        }
        
        .stat-icon.bg-orange {
            background-color: rgba(253, 126, 20, 0.1);
            color: #fd7e14;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .tag {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            background-color: #e9ecef;
            color: #495057;
            font-size: 0.85rem;
        }
        
        .tag.size-1 { font-size: 0.85rem; }
        .tag.size-2 { font-size: 1rem; }
        .tag.size-3 { font-size: 1.2rem; }
        .tag.size-4 { font-size: 1.5rem; }
        .tag.size-5 { font-size: 1.8rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <img src="https://via.placeholder.com/150x40?text=MS+知识平台" alt="Materials Studio 知识解答平台">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 管理控制台</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="adminDropdown" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> 管理员
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog"></i> 个人设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar">
                    <h5 class="mb-3"><i class="fas fa-cogs"></i> 系统管理</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="user-roles.html">
                                <i class="fas fa-users-cog"></i> 用户角色与权限
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="user-management.html">
                                <i class="fas fa-user-edit"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="content-management.html">
                                <i class="fas fa-file-alt"></i> 内容管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certification-management.html">
                                <i class="fas fa-certificate"></i> 专业认证管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="statistics.html">
                                <i class="fas fa-chart-bar"></i> 数据统计
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3>数据统计与分析</h3>
                    <div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-primary active">今日</button>
                            <button type="button" class="btn btn-outline-primary">本周</button>
                            <button type="button" class="btn btn-outline-primary">本月</button>
                            <button type="button" class="btn btn-outline-primary">全部</button>
                        </div>
                        <button class="btn btn-primary ms-2"><i class="fas fa-download me-1"></i> 导出报表</button>
                    </div>
                </div>
                
                <!-- 概览统计卡片 -->
                <div class="row">
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-purple">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <h6 class="card-title mb-0">总用户数</h6>
                                        <h3 class="mb-0">3,254</h3>
                                        <small class="text-success"><i class="fas fa-arrow-up me-1"></i>8.3% 较上月</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-blue">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <div>
                                        <h6 class="card-title mb-0">总问题数</h6>
                                        <h3 class="mb-0">1,852</h3>
                                        <small class="text-success"><i class="fas fa-arrow-up me-1"></i>12.4% 较上月</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-teal">
                                        <i class="fas fa-comment-alt"></i>
                                    </div>
                                    <div>
                                        <h6 class="card-title mb-0">总回答数</h6>
                                        <h3 class="mb-0">6,432</h3>
                                        <small class="text-success"><i class="fas fa-arrow-up me-1"></i>5.7% 较上月</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-orange">
                                        <i class="fas fa-file"></i>
                                    </div>
                                    <div>
                                        <h6 class="card-title mb-0">资源数量</h6>
                                        <h3 class="mb-0">753</h3>
                                        <small class="text-success"><i class="fas fa-arrow-up me-1"></i>9.2% 较上月</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 用户活跃度图表 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-chart-line me-2"></i>用户活跃度趋势</span>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary active">日活</button>
                            <button type="button" class="btn btn-outline-secondary">周活</button>
                            <button type="button" class="btn btn-outline-secondary">月活</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="userActivityChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- 内容热度分布 -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <i class="fas fa-fire me-2"></i>内容热度分布
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="contentHeatChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 用户类型分布 -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <i class="fas fa-users-cog me-2"></i>用户类型分布
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="userTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 热门搜索关键词 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-search me-2"></i>热门搜索关键词
                    </div>
                    <div class="card-body">
                        <div class="tag-cloud">
                            <span class="tag size-5">CASTEP</span>
                            <span class="tag size-4">Forcite</span>
                            <span class="tag size-4">分子动力学</span>
                            <span class="tag size-3">DFT计算</span>
                            <span class="tag size-3">晶体结构</span>
                            <span class="tag size-3">GULP</span>
                            <span class="tag size-2">Materials Visualizer</span>
                            <span class="tag size-2">力场参数</span>
                            <span class="tag size-2">吸附能</span>
                            <span class="tag size-2">界面模型</span>
                            <span class="tag size-1">能带结构</span>
                            <span class="tag size-1">密度泛函理论</span>
                            <span class="tag size-1">DMol3</span>
                            <span class="tag size-1">结构优化</span>
                            <span class="tag size-1">过渡态</span>
                            <span class="tag size-1">晶格常数</span>
                            <span class="tag size-1">Perl脚本</span>
                            <span class="tag size-1">COMPASS力场</span>
                        </div>
                    </div>
                </div>
                
                <!-- 内容质量评估 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-star me-2"></i>内容质量评估</span>
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>问答内容</option>
                            <option>资源内容</option>
                            <option>讨论内容</option>
                        </select>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>内容ID</th>
                                        <th>标题</th>
                                        <th>创建者</th>
                                        <th>评分</th>
                                        <th>评论数</th>
                                        <th>浏览量</th>
                                        <th>综合质量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#Q10253</td>
                                        <td>如何在CASTEP中设置过渡态计算？</td>
                                        <td>张明</td>
                                        <td>4.8/5.0</td>
                                        <td>12</td>
                                        <td>528</td>
                                        <td>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: 92%;" aria-valuenow="92" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#Q10249</td>
                                        <td>Materials Studio中如何构建二维材料界面？</td>
                                        <td>李华</td>
                                        <td>4.6/5.0</td>
                                        <td>9</td>
                                        <td>425</td>
                                        <td>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: 88%;" aria-valuenow="88" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#Q10245</td>
                                        <td>Forcite模块的COMPASS力场参数如何调整？</td>
                                        <td>王军</td>
                                        <td>4.3/5.0</td>
                                        <td>7</td>
                                        <td>382</td>
                                        <td>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-primary" role="progressbar" style="width: 82%;" aria-valuenow="82" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#Q10240</td>
                                        <td>DMol3计算过程中遇到收敛问题怎么解决？</td>
                                        <td>赵敏</td>
                                        <td>4.1/5.0</td>
                                        <td>5</td>
                                        <td>298</td>
                                        <td>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-primary" role="progressbar" style="width: 78%;" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#Q10235</td>
                                        <td>如何使用Perl脚本批量处理Materials Studio结果？</td>
                                        <td>刘强</td>
                                        <td>3.9/5.0</td>
                                        <td>4</td>
                                        <td>215</td>
                                        <td>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-info" role="progressbar" style="width: 72%;" aria-valuenow="72" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部版权信息 -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2023 Materials Studio 知识解答平台. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white-50 text-decoration-none me-3">隐私政策</a>
                    <a href="#" class="text-white-50 text-decoration-none me-3">使用条款</a>
                    <a href="#" class="text-white-50 text-decoration-none">联系我们</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 图表脚本 -->
    <script>
        // 用户活跃度趋势图
        const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
        const userActivityChart = new Chart(userActivityCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [
                    {
                        label: '活跃用户',
                        data: [320, 350, 380, 420, 450, 470, 510, 550, 590, 620, 650, 680],
                        borderColor: 'rgba(13, 110, 253, 1)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: '新注册用户',
                        data: [120, 140, 130, 150, 160, 170, 165, 180, 190, 200, 210, 220],
                        borderColor: 'rgba(32, 201, 151, 1)',
                        backgroundColor: 'rgba(32, 201, 151, 0.1)',
                        tension: 0.3,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // 内容热度分布图
        const contentHeatCtx = document.getElementById('contentHeatChart').getContext('2d');
        const contentHeatChart = new Chart(contentHeatCtx, {
            type: 'pie',
            data: {
                labels: ['问答内容', '资源内容', '讨论内容', '教程内容', '其他内容'],
                datasets: [{
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: [
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(32, 201, 151, 0.7)',
                        'rgba(253, 126, 20, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
        
        // 用户类型分布图
        const userTypeCtx = document.getElementById('userTypeChart').getContext('2d');
        const userTypeChart = new Chart(userTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['注册用户', '高级会员', '专家用户', '版主', '管理员'],
                datasets: [{
                    data: [65, 20, 10, 3, 2],
                    backgroundColor: [
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(32, 201, 151, 0.7)',
                        'rgba(253, 126, 20, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    </script>
</body>
</html> 