/**
 * switch-to-prod.js
 * 用于在部署前将API配置切换到生产环境
 */
const fs = require('fs');
const path = require('path');

// 配置文件路径
const configPath = path.join(__dirname, 'js/api/config.js');

// 读取当前配置文件
fs.readFile(configPath, 'utf8', (err, data) => {
    if (err) {
        console.error('读取配置文件失败:', err);
        process.exit(1);
    }
    
    // 替换环境变量为生产环境
    const updatedData = data.replace(
        /const CURRENT_ENV = ['"]development['"]/g, 
        'const CURRENT_ENV = \'production\''
    );
    
    // 写入更新后的配置
    fs.writeFile(configPath, updatedData, 'utf8', (err) => {
        if (err) {
            console.error('写入配置文件失败:', err);
            process.exit(1);
        }
        
        console.log('已成功将配置切换至生产环境！');
    });
}); 