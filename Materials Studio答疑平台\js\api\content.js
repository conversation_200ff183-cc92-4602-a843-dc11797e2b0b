/**
 * content.js - 内容和资源相关的API交互
 * 处理各类资源、教程和文章的获取和管理
 */

// API基础URL，从配置文件获取
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:8000/api/v1';

/**
 * 获取资源列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的记录数
 * @param {string} params.category - 资源类别
 * @param {string} params.search - 搜索关键词
 * @param {boolean} params.only_premium - 是否只返回会员内容
 * @returns {Promise} - 包含资源列表的Promise
 */
async function getResources(params = {}) {
    try {
        const queryParams = new URLSearchParams();
        
        // 添加查询参数
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                queryParams.append(key, params[key]);
            }
        });
        
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/content/resources/?${queryParams.toString()}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取资源列表失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取资源列表错误:', error);
        throw error;
    }
}

/**
 * 获取资源详情
 * @param {number} resourceId - 资源ID
 * @returns {Promise} - 包含资源详情的Promise
 */
async function getResource(resourceId) {
    try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/content/resources/${resourceId}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取资源详情失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`获取资源ID=${resourceId}的详情错误:`, error);
        throw error;
    }
}

/**
 * 获取资源分类列表
 * @returns {Promise} - 包含资源分类列表的Promise
 */
async function getResourceCategories() {
    try {
        const response = await fetch(`${API_BASE_URL}/content/categories/`, {
            method: 'GET'
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取资源分类列表失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取资源分类列表错误:', error);
        throw error;
    }
}

/**
 * 获取视频教程列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的记录数
 * @param {string} params.category - 教程类别
 * @param {string} params.search - 搜索关键词
 * @param {boolean} params.only_premium - 是否只返回会员内容
 * @returns {Promise} - 包含视频教程列表的Promise
 */
async function getVideos(params = {}) {
    try {
        const queryParams = new URLSearchParams();
        
        // 添加查询参数
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                queryParams.append(key, params[key]);
            }
        });
        
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/content/videos/?${queryParams.toString()}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取视频教程列表失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取视频教程列表错误:', error);
        throw error;
    }
}

/**
 * 获取视频教程详情
 * @param {number} videoId - 视频教程ID
 * @returns {Promise} - 包含视频教程详情的Promise
 */
async function getVideo(videoId) {
    try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/content/videos/${videoId}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取视频教程详情失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`获取视频教程ID=${videoId}的详情错误:`, error);
        throw error;
    }
}

/**
 * 获取文章列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的记录数
 * @param {string} params.category - 文章类别
 * @param {string} params.search - 搜索关键词
 * @param {boolean} params.only_premium - 是否只返回会员内容
 * @returns {Promise} - 包含文章列表的Promise
 */
async function getArticles(params = {}) {
    try {
        const queryParams = new URLSearchParams();
        
        // 添加查询参数
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                queryParams.append(key, params[key]);
            }
        });
        
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/content/articles/?${queryParams.toString()}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取文章列表失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取文章列表错误:', error);
        throw error;
    }
}

/**
 * 获取文章详情
 * @param {number} articleId - 文章ID
 * @returns {Promise} - 包含文章详情的Promise
 */
async function getArticle(articleId) {
    try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/content/articles/${articleId}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取文章详情失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`获取文章ID=${articleId}的详情错误:`, error);
        throw error;
    }
}

/**
 * 搜索全站内容
 * @param {string} query - 搜索关键词
 * @param {string} type - 内容类型，可选值：'all', 'question', 'article', 'video', 'resource'
 * @param {number} skip - 跳过的记录数
 * @param {number} limit - 返回的记录数
 * @returns {Promise} - 包含搜索结果的Promise
 */
async function search(query, type = 'all', skip = 0, limit = 10) {
    try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const queryParams = new URLSearchParams({
            query,
            type,
            skip,
            limit
        });
        
        const response = await fetch(`${API_BASE_URL}/search/?${queryParams.toString()}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '搜索失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('搜索错误:', error);
        throw error;
    }
}

/**
 * 获取首页推荐内容
 * @returns {Promise} - 包含首页推荐内容的Promise
 */
async function getFeaturedContent() {
    try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/content/featured/`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取推荐内容失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取推荐内容错误:', error);
        throw error;
    }
}

/**
 * 获取热门话题
 * @param {number} limit - 返回的记录数
 * @returns {Promise} - 包含热门话题的Promise
 */
async function getHotTopics(limit = 5) {
    try {
        const response = await fetch(`${API_BASE_URL}/content/hot-topics/?limit=${limit}`, {
            method: 'GET'
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取热门话题失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取热门话题错误:', error);
        throw error;
    }
}

// 导出API函数
window.contentAPI = {
    getResources,
    getResource,
    getResourceCategories,
    getVideos,
    getVideo,
    getArticles,
    getArticle,
    search,
    getFeaturedContent,
    getHotTopics
}; 