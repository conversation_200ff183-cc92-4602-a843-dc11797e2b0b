/**
 * qa.js - 问答相关的API交互
 * 处理问题和答案的获取、创建、更新和删除
 */

// API基础URL，从配置文件获取
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:8000/api/v1';

/**
 * 获取问题列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过的记录数
 * @param {number} params.limit - 返回的记录数
 * @param {number} params.tag_id - 按标签筛选
 * @param {number} params.author_id - 按作者筛选
 * @param {string} params.search - 搜索关键词
 * @param {boolean} params.only_premium - 是否只返回会员内容
 * @param {boolean} params.only_solved - 是否只返回已解决的问题
 * @returns {Promise} - 包含问题列表的Promise
 */
async function getQuestions(params = {}) {
    try {
        const queryParams = new URLSearchParams();
        
        // 添加查询参数
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                queryParams.append(key, params[key]);
            }
        });
        
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/?${queryParams.toString()}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取问题列表失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取问题列表错误:', error);
        throw error;
    }
}

/**
 * 获取问题详情
 * @param {number} questionId - 问题ID
 * @returns {Promise} - 包含问题详情的Promise
 */
async function getQuestion(questionId) {
    try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/${questionId}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取问题详情失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`获取问题ID=${questionId}的详情错误:`, error);
        throw error;
    }
}

/**
 * 创建新问题
 * @param {Object} questionData - 问题数据
 * @param {string} questionData.title - 问题标题
 * @param {string} questionData.content - 问题内容
 * @param {Array<number>} questionData.tags - 标签ID数组
 * @param {boolean} questionData.is_premium - 是否为会员内容
 * @returns {Promise} - 包含创建的问题的Promise
 */
async function createQuestion(questionData) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(questionData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '创建问题失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('创建问题错误:', error);
        throw error;
    }
}

/**
 * 更新问题
 * @param {number} questionId - 问题ID
 * @param {Object} questionData - 问题更新数据
 * @returns {Promise} - 包含更新后的问题的Promise
 */
async function updateQuestion(questionId, questionData) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/${questionId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(questionData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '更新问题失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`更新问题ID=${questionId}错误:`, error);
        throw error;
    }
}

/**
 * 删除问题
 * @param {number} questionId - 问题ID
 * @returns {Promise} - 包含删除结果的Promise
 */
async function deleteQuestion(questionId) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/${questionId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '删除问题失败');
        }
        
        return true; // 删除成功
    } catch (error) {
        console.error(`删除问题ID=${questionId}错误:`, error);
        throw error;
    }
}

/**
 * 创建回答
 * @param {number} questionId - 问题ID
 * @param {Object} answerData - 回答数据
 * @param {string} answerData.content - 回答内容
 * @param {boolean} answerData.is_premium - 是否为会员内容
 * @returns {Promise} - 包含创建的回答的Promise
 */
async function createAnswer(questionId, answerData) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/${questionId}/answers/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(answerData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '创建回答失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`为问题ID=${questionId}创建回答错误:`, error);
        throw error;
    }
}

/**
 * 获取回答详情
 * @param {number} answerId - 回答ID
 * @returns {Promise} - 包含回答详情的Promise
 */
async function getAnswer(answerId) {
    try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        
        const response = await fetch(`${API_BASE_URL}/qa/answers/${answerId}`, {
            method: 'GET',
            headers
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取回答详情失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`获取回答ID=${answerId}的详情错误:`, error);
        throw error;
    }
}

/**
 * 更新回答
 * @param {number} answerId - 回答ID
 * @param {Object} answerData - 回答更新数据
 * @returns {Promise} - 包含更新后的回答的Promise
 */
async function updateAnswer(answerId, answerData) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/answers/${answerId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(answerData)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '更新回答失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`更新回答ID=${answerId}错误:`, error);
        throw error;
    }
}

/**
 * 删除回答
 * @param {number} answerId - 回答ID
 * @returns {Promise} - 包含删除结果的Promise
 */
async function deleteAnswer(answerId) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/answers/${answerId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '删除回答失败');
        }
        
        return true; // 删除成功
    } catch (error) {
        console.error(`删除回答ID=${answerId}错误:`, error);
        throw error;
    }
}

/**
 * 获取标签列表
 * @returns {Promise} - 包含标签列表的Promise
 */
async function getTags() {
    try {
        const response = await fetch(`${API_BASE_URL}/qa/tags/`, {
            method: 'GET'
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取标签列表失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取标签列表错误:', error);
        throw error;
    }
}

/**
 * 添加问题到收藏
 * @param {number} questionId - 问题ID
 * @returns {Promise} - 包含收藏结果的Promise
 */
async function addToFavorites(questionId) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/${questionId}/favorite`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '添加收藏失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error(`添加问题ID=${questionId}到收藏错误:`, error);
        throw error;
    }
}

/**
 * 从收藏中移除问题
 * @param {number} questionId - 问题ID
 * @returns {Promise} - 包含移除结果的Promise
 */
async function removeFromFavorites(questionId) {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/questions/${questionId}/favorite`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '移除收藏失败');
        }
        
        return true; // 移除成功
    } catch (error) {
        console.error(`从收藏中移除问题ID=${questionId}错误:`, error);
        throw error;
    }
}

/**
 * 获取用户收藏列表
 * @returns {Promise} - 包含用户收藏列表的Promise
 */
async function getUserFavorites() {
    try {
        const token = localStorage.getItem('token');
        
        if (!token) {
            throw new Error('未登录，请先登录');
        }
        
        const response = await fetch(`${API_BASE_URL}/qa/favorites/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取收藏列表失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('获取用户收藏列表错误:', error);
        throw error;
    }
}

// 导出API函数
window.qaAPI = {
    getQuestions,
    getQuestion,
    createQuestion,
    updateQuestion,
    deleteQuestion,
    createAnswer,
    getAnswer,
    updateAnswer,
    deleteAnswer,
    getTags,
    addToFavorites,
    removeFromFavorites,
    getUserFavorites
}; 