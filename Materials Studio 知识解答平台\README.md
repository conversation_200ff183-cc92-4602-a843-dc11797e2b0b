# Materials Studio 知识解答平台 - 部署指南

## 项目概述

Materials Studio 知识解答平台是一个专注于解答 Materials Studio 软件用户问题的在线服务网站。平台提供问答、资源共享、专业社区等功能，帮助材料科学研究人员解决实际问题。

## 技术架构

- 前端：HTML5, CSS3, JavaScript, Bootstrap
- 后端：Node.js, Express.js
- 数据库：PostgreSQL/SQLite
- 部署：Docker, Nginx, PM2

## 部署需求

- Node.js v14+
- PostgreSQL 14+ (生产环境)
- Docker & Docker Compose (推荐部署方式)
- 支持HTTPS的Web服务器

## 部署步骤

### 方式一：使用Docker Compose (推荐)

1. 克隆代码仓库

```bash
git clone https://github.com/your-username/materials-studio-platform.git
cd materials-studio-platform
```

2. 准备环境配置

```bash
cd backend
cp config/production.env.example .env
```

编辑`.env`文件，设置生产环境所需的参数：
- 设置强密码的JWT_SECRET和COOKIE_SECRET
- 配置数据库连接参数
- 设置正确的CORS_ORIGIN为您的域名

3. 准备SSL证书

将SSL证书文件放置在`backend/nginx/ssl/`目录下：
- `server.crt`：SSL证书文件
- `server.key`：SSL私钥文件

如果没有证书，可以使用Let's Encrypt获取免费证书。

4. 修改Nginx配置

编辑`backend/nginx/conf.d/default.conf`，将`server_name`替换为您的域名。

5. 启动服务

```bash
docker-compose up -d
```

6. 初始化数据库

```bash
docker-compose exec api npx sequelize-cli db:migrate
docker-compose exec api npx sequelize-cli db:seed:all
```

### 方式二：手动部署

1. 配置后端

```bash
cd backend
npm install
cp config/production.env.example .env
```

编辑`.env`文件设置生产环境参数。

2. 配置数据库

安装PostgreSQL并创建数据库：

```bash
createdb materials_studio_db
```

3. 启动PM2服务

```bash
npm install -g pm2
NODE_ENV=production pm2 start ecosystem.config.js --env production
pm2 save
```

4. 配置Nginx

安装Nginx并设置反向代理，参考`backend/nginx/conf.d/default.conf`的配置。

5. 设置SSL证书

使用Let's Encrypt或其他方式获取SSL证书，并配置到Nginx中。

## 维护与监控

### 日志管理

- Docker部署：`docker-compose logs -f api`
- PM2部署：`pm2 logs materials-studio-api`

日志文件位于`backend/logs/`目录。

### 数据库备份

定期备份PostgreSQL数据库：

```bash
docker-compose exec db pg_dump -U postgres materials_studio_db > backup_$(date +%Y%m%d).sql
```

### 更新系统

1. 拉取最新代码

```bash
git pull origin main
```

2. 重新构建并启动容器

```bash
docker-compose build
docker-compose up -d
```

### 安全注意事项

- 定期更新依赖包：`npm audit fix`
- 设置强密码并定期更换
- 配置防火墙仅开放必要端口
- 定期更新SSL证书

## 故障排除

### 常见问题

1. 数据库连接失败

检查数据库连接参数是否正确，数据库服务是否正常运行。

2. API请求失败

检查日志文件，可能是跨域配置或鉴权问题。

3. HTTPS证书问题

确保证书文件正确，且Nginx配置正确引用了证书文件。

## 联系与支持

如有任何部署问题，请联系：<EMAIL> 