package com.materialsstudio.qa.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息视图对象
 */
@Data
public class UserVO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 电子邮箱
     */
    private String email;
    
    /**
     * 手机号码
     */
    private String phone;
    
    /**
     * 用户头像URL
     */
    private String avatar;
    
    /**
     * 用户角色
     */
    private String role;
    
    /**
     * 是否为会员
     */
    private Boolean isMember;
    
    /**
     * 会员等级
     */
    private String memberLevel;
    
    /**
     * 会员等级名称
     */
    private String memberLevelName;
    
    /**
     * 会员过期时间
     */
    private LocalDateTime memberExpireTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 