package com.materialsstudio.qa.service.impl;

import com.materialsstudio.qa.dao.AnswerDao;
import com.materialsstudio.qa.dao.QuestionDao;
import com.materialsstudio.qa.dao.UserDao;
import com.materialsstudio.qa.entity.Answer;
import com.materialsstudio.qa.entity.User;
import com.materialsstudio.qa.model.dto.AnswerPostDTO;
import com.materialsstudio.qa.model.vo.AnswerVO;
import com.materialsstudio.qa.service.AnswerService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 回答服务实现类
 */
@Service
public class AnswerServiceImpl implements AnswerService {
    
    @Resource
    private AnswerDao answerDao;
    
    @Resource
    private QuestionDao questionDao;
    
    @Resource
    private UserDao userDao;
    
    @Override
    public List<AnswerVO> getAnswersByQuestionId(Long questionId) {
        return answerDao.getAnswersByQuestionId(questionId);
    }
    
    @Override
    @Transactional
    public Long postAnswer(AnswerPostDTO answerPostDTO) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RuntimeException("用户未登录");
        }
        
        // 获取当前用户信息
        String username = authentication.getName();
        User user = userDao.selectByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 创建回答对象
        Answer answer = new Answer();
        answer.setQuestionId(answerPostDTO.getQuestionId());
        answer.setContent(answerPostDTO.getContent());
        answer.setAuthorId(user.getId());
        answer.setIsAccepted(0); // 未采纳
        answer.setIsPremium(answerPostDTO.getIsPremium() ? 1 : 0);
        answer.setVotes(0);
        answer.setCreateTime(LocalDateTime.now());
        answer.setUpdateTime(LocalDateTime.now());
        answer.setDeleted(0);
        
        // 保存回答
        answerDao.insert(answer);
        
        return answer.getId();
    }
    
    @Override
    @Transactional
    public boolean acceptAnswer(Long id) {
        // 先取消所有采纳（同一问题下）
        answerDao.cancelAllAccepted(id);
        
        // 再采纳当前回答
        int result = answerDao.acceptAnswer(id);
        
        // 更新问题状态为已解决
        if (result > 0) {
            Answer answer = answerDao.selectById(id);
            if (answer != null) {
                questionDao.updateStatus(answer.getQuestionId(), 1);
            }
        }
        
        return result > 0;
    }
    
    @Override
    public boolean voteAnswer(Long id) {
        return answerDao.voteAnswer(id) > 0;
    }
} 