const logger = require('../utils/logger');
const config = require('../config');

// 自定义错误类
class AppError extends Error {
  constructor(message, statusCode = 500, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 验证错误类
class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 400, 'VALIDATION_ERROR');
    this.errors = errors;
  }
}

// 认证错误类
class AuthenticationError extends AppError {
  constructor(message = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

// 授权错误类
class AuthorizationError extends AppError {
  constructor(message = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

// 资源未找到错误类
class NotFoundError extends AppError {
  constructor(message = '资源未找到') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

// 冲突错误类
class ConflictError extends AppError {
  constructor(message = '资源冲突') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

// 速率限制错误类
class RateLimitError extends AppError {
  constructor(message = '请求过于频繁') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

// 处理数据库错误
const handleDatabaseError = (error) => {
  logger.error('数据库错误:', error);

  switch (error.code) {
    case 'ER_DUP_ENTRY':
      return new ConflictError('数据已存在');
    case 'ER_NO_REFERENCED_ROW_2':
      return new ValidationError('引用的数据不存在');
    case 'ER_ROW_IS_REFERENCED_2':
      return new ConflictError('数据正在被引用，无法删除');
    case 'ER_DATA_TOO_LONG':
      return new ValidationError('数据长度超出限制');
    case 'ER_BAD_NULL_ERROR':
      return new ValidationError('必填字段不能为空');
    case 'ECONNREFUSED':
      return new AppError('数据库连接失败', 503, 'DATABASE_CONNECTION_ERROR');
    case 'PROTOCOL_CONNECTION_LOST':
      return new AppError('数据库连接丢失', 503, 'DATABASE_CONNECTION_LOST');
    default:
      return new AppError('数据库操作失败', 500, 'DATABASE_ERROR');
  }
};

// 处理JWT错误
const handleJWTError = (error) => {
  logger.error('JWT错误:', error);

  switch (error.name) {
    case 'JsonWebTokenError':
      return new AuthenticationError('无效的访问令牌');
    case 'TokenExpiredError':
      return new AuthenticationError('访问令牌已过期');
    case 'NotBeforeError':
      return new AuthenticationError('访问令牌尚未生效');
    default:
      return new AuthenticationError('令牌验证失败');
  }
};

// 处理验证错误
const handleValidationError = (error) => {
  logger.error('验证错误:', error);

  if (error.details) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));
    
    return new ValidationError('数据验证失败', errors);
  }

  return new ValidationError(error.message);
};

// 处理Multer错误
const handleMulterError = (error) => {
  logger.error('文件上传错误:', error);

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      return new ValidationError('文件大小超出限制');
    case 'LIMIT_FILE_COUNT':
      return new ValidationError('文件数量超出限制');
    case 'LIMIT_UNEXPECTED_FILE':
      return new ValidationError('不支持的文件字段');
    case 'MISSING_FILE':
      return new ValidationError('缺少文件');
    default:
      return new AppError('文件上传失败', 400, 'FILE_UPLOAD_ERROR');
  }
};

// 发送错误响应
const sendErrorResponse = (res, error) => {
  const response = {
    success: false,
    code: error.statusCode,
    message: error.message,
    timestamp: new Date().toISOString()
  };

  // 在开发环境中包含错误详情
  if (config.app.env === 'development') {
    response.stack = error.stack;
    
    if (error.errors) {
      response.errors = error.errors;
    }
  }

  // 在生产环境中隐藏敏感信息
  if (config.app.env === 'production' && error.statusCode === 500) {
    response.message = '服务器内部错误';
  }

  res.status(error.statusCode).json(response);
};

// 错误处理中间件
const errorHandler = (error, req, res, next) => {
  let err = error;

  // 记录错误日志
  logger.logError(error, req);

  // 处理不同类型的错误
  if (error.name === 'ValidationError' || error.isJoi) {
    err = handleValidationError(error);
  } else if (error.name === 'JsonWebTokenError' || 
             error.name === 'TokenExpiredError' || 
             error.name === 'NotBeforeError') {
    err = handleJWTError(error);
  } else if (error.code && error.code.startsWith('ER_')) {
    err = handleDatabaseError(error);
  } else if (error.code && error.code.startsWith('LIMIT_')) {
    err = handleMulterError(error);
  } else if (!error.isOperational) {
    // 未知错误，转换为通用错误
    err = new AppError('服务器内部错误', 500, 'INTERNAL_SERVER_ERROR');
  }

  // 发送错误响应
  sendErrorResponse(res, err);
};

// 异步错误包装器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 创建错误的便捷函数
const createError = (message, statusCode = 500, code = null) => {
  return new AppError(message, statusCode, code);
};

module.exports = {
  errorHandler,
  asyncHandler,
  createError,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError
};
