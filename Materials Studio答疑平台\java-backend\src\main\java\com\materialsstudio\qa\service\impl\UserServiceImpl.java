package com.materialsstudio.qa.service.impl;

import com.materialsstudio.qa.dao.MembershipDao;
import com.materialsstudio.qa.dao.UserDao;
import com.materialsstudio.qa.entity.Membership;
import com.materialsstudio.qa.entity.User;
import com.materialsstudio.qa.model.dto.LoginDTO;
import com.materialsstudio.qa.model.vo.LoginVO;
import com.materialsstudio.qa.model.vo.UserVO;
import com.materialsstudio.qa.service.UserService;
import com.materialsstudio.qa.utils.JwtUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    @Resource
    private UserDao userDao;
    
    @Resource
    private MembershipDao membershipDao;
    
    @Resource
    private AuthenticationManager authenticationManager;
    
    @Resource
    private JwtUtils jwtUtils;
    
    @Resource
    private PasswordEncoder passwordEncoder;
    
    @Value("${jwt.expiration}")
    private Long expiration;
    
    @Override
    public LoginVO login(LoginDTO loginDTO) {
        try {
            // 认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginDTO.getUsername(), loginDTO.getPassword())
            );
            
            // 设置认证信息
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 获取用户信息
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            User user = userDao.selectByUsername(userDetails.getUsername());
            
            // 更新最后登录时间
            userDao.updateLastLoginTime(user.getId());
            
            // 生成token
            String token = jwtUtils.generateToken(userDetails);
            
            // 创建返回对象
            LoginVO loginVO = new LoginVO();
            loginVO.setAccess_token(token);
            loginVO.setToken_type("Bearer");
            loginVO.setExpires_in(expiration / 1000);
            loginVO.setUserId(user.getId());
            loginVO.setUsername(user.getUsername());
            loginVO.setRole(user.getRole());
            
            return loginVO;
        } catch (BadCredentialsException e) {
            throw new RuntimeException("用户名或密码错误");
        }
    }
    
    @Override
    public UserVO getCurrentUser() {
        // 获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        // 如果未认证，返回null
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }
        
        // 获取用户名
        String username = authentication.getName();
        
        // 获取用户信息
        return getUserByUsername(username);
    }
    
    @Override
    public UserVO getUserByUsername(String username) {
        // 查询用户
        User user = userDao.selectByUsername(username);
        
        // 如果用户不存在，返回null
        if (user == null) {
            return null;
        }
        
        // 查询会员信息
        Membership membership = membershipDao.selectActiveByUserId(user.getId());
        
        // 创建返回对象
        UserVO userVO = new UserVO();
        userVO.setId(user.getId());
        userVO.setUsername(user.getUsername());
        userVO.setEmail(user.getEmail());
        userVO.setPhone(user.getPhone());
        userVO.setAvatar(user.getAvatar());
        userVO.setRole(user.getRole());
        userVO.setCreateTime(user.getCreateTime());
        
        // 设置会员信息
        boolean isMember = membership != null && membership.getIsActive() == 1 && membership.getEndDate().isAfter(LocalDateTime.now());
        userVO.setIsMember(isMember);
        
        if (isMember) {
            userVO.setMemberLevel(membership.getLevel());
            userVO.setMemberLevelName(membership.getLevelName());
            userVO.setMemberExpireTime(membership.getEndDate());
        }
        
        return userVO;
    }
} 