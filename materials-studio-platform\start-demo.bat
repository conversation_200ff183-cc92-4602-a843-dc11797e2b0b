@echo off
chcp 65001 >nul
echo 🚀 Materials Studio Platform 演示模式启动
echo ==========================================

echo 📦 检查依赖...

:: 检查前端依赖
cd frontend
if not exist "node_modules" (
    echo 安装前端依赖...
    npm install --no-optional
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)
cd ..

:: 检查后端依赖
cd backend
if not exist "node_modules" (
    echo 安装后端依赖...
    npm install --no-optional
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
)
cd ..

echo ✅ 依赖检查完成

echo 🚀 启动服务...
echo.
echo 后端服务: http://localhost:8000
echo 前端服务: http://localhost:3000
echo.
echo 演示账户:
echo - 管理员: admin / 任意密码
echo - 普通用户: 任意用户名 / 任意密码
echo.
echo 按 Ctrl+C 停止服务
echo.

:: 启动后端服务
start "Backend API" cmd /k "cd backend && npm run dev"

:: 等待2秒让后端启动
timeout /t 2 /nobreak >nul

:: 启动前端服务
start "Frontend" cmd /k "cd frontend && npm run dev"

:: 等待5秒后自动打开浏览器
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo 🎉 服务启动完成！
echo 如果浏览器没有自动打开，请手动访问: http://localhost:3000
pause
