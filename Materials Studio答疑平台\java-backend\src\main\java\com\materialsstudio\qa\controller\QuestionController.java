package com.materialsstudio.qa.controller;

import com.materialsstudio.qa.common.Result;
import com.materialsstudio.qa.model.dto.QuestionPostDTO;
import com.materialsstudio.qa.model.dto.QuestionQueryDTO;
import com.materialsstudio.qa.model.vo.PageVO;
import com.materialsstudio.qa.model.vo.QuestionDetailVO;
import com.materialsstudio.qa.model.vo.QuestionVO;
import com.materialsstudio.qa.service.QuestionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 问题控制器
 */
@RestController
@RequestMapping("/questions")
public class QuestionController {
    
    @Resource
    private QuestionService questionService;
    
    /**
     * 分页查询问题列表
     *
     * @param queryDTO 查询条件
     * @return 分页问题列表
     */
    @GetMapping
    public Result<PageVO<QuestionVO>> pageQuestions(QuestionQueryDTO queryDTO) {
        try {
            PageVO<QuestionVO> pageVO = questionService.pageQuestions(queryDTO);
            return Result.success(pageVO);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 获取问题详情
     *
     * @param id 问题ID
     * @return 问题详情
     */
    @GetMapping("/{id}")
    public Result<QuestionDetailVO> getQuestionDetail(@PathVariable Long id) {
        try {
            // 增加浏览量
            questionService.incrementViewCount(id);
            
            QuestionDetailVO detailVO = questionService.getQuestionDetail(id);
            if (detailVO == null) {
                return Result.notFound("问题不存在");
            }
            return Result.success(detailVO);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 发布问题
     *
     * @param questionPostDTO 问题发布信息
     * @return 问题ID
     */
    @PostMapping
    public Result<Long> postQuestion(@Validated @RequestBody QuestionPostDTO questionPostDTO) {
        try {
            Long id = questionService.postQuestion(questionPostDTO);
            return Result.success(id);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
} 