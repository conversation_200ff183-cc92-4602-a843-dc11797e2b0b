<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员中心 - Materials Studio 答疑平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #0d47a1;
            --accent-color: #03a9f4;
            --light-bg: #f5f5f5;
            --member-color: #ffc107;
            --success-color: #4caf50;
            --info-color: #03a9f4;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --dark-color: #212121;
            --text-color: #212121;
            --light-text: #ffffff;
            --border-radius: 8px;
            --box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 4px 8px rgba(0,0,0,0.06);
            --transition-speed: 0.3s;
        }
        
        body {
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        /* 导航栏 */
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.85);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all var(--transition-speed);
        }
        
        .navbar-dark .navbar-nav .nav-link:hover,
        .navbar-dark .navbar-nav .nav-link.active {
            color: rgba(255,255,255,1);
            background-color: rgba(255,255,255,0.2);
        }
        
        /* 页面标题区域 */
        .page-title-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }
        
        .page-title-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://images.unsplash.com/photo-1557425529-b1ae9c141e7a?ixid=MnwxMjA3fDB8MHxzZWFyY2h8MXx8bW9sZWN1bGUlMjBzdHJ1Y3R1cmV8ZW58MHx8MHx8&auto=format&fit=crop&w=1200&q=60') center/cover no-repeat;
            opacity: 0.1;
            z-index: 0;
        }
        
        .page-title-section .container {
            position: relative;
            z-index: 1;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        /* 会员卡片 */
        .pricing-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .pricing-card.popular {
            border: 2px solid var(--member-color);
            box-shadow: 0 10px 30px rgba(255, 193, 7, 0.2);
        }
        
        .pricing-card.popular::before {
            content: '推荐';
            position: absolute;
            top: 15px;
            right: -30px;
            background-color: var(--member-color);
            color: var(--dark-color);
            padding: 5px 30px;
            font-size: 0.8rem;
            font-weight: bold;
            transform: rotate(45deg);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .pricing-header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .pricing-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .pricing-price {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .pricing-price.popular {
            color: var(--member-color);
        }
        
        .pricing-duration {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 15px;
        }
        
        .pricing-description {
            text-align: center;
            margin-bottom: 20px;
            font-size: 0.95rem;
            color: #6c757d;
        }
        
        .pricing-features {
            list-style: none;
            padding: 0;
            margin: 0 0 30px 0;
        }
        
        .pricing-features li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
        }
        
        .pricing-features li:last-child {
            border-bottom: none;
        }
        
        .pricing-features i {
            margin-right: 10px;
            font-size: 1.1rem;
        }
        
        .pricing-features i.fa-check {
            color: var(--success-color);
        }
        
        .pricing-features i.fa-times {
            color: var(--danger-color);
        }
        
        .pricing-btn {
            padding: 12px 20px;
            font-weight: 500;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .pricing-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        /* 会员优势 */
        .feature-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            margin-bottom: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(25, 118, 210, 0.1);
            color: var(--primary-color);
            font-size: 2rem;
        }
        
        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        /* 常见问题 */
        .faq-section {
            background-color: white;
            padding: 80px 0;
        }
        
        .accordion-item {
            border: none;
            margin-bottom: 15px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .accordion-button {
            padding: 20px;
            font-size: 1.1rem;
            font-weight: 500;
            background-color: white;
            color: var(--text-color);
        }
        
        .accordion-button:focus {
            box-shadow: none;
            border-color: rgba(0,0,0,0.1);
        }
        
        .accordion-button:not(.collapsed) {
            background-color: rgba(25, 118, 210, 0.05);
            color: var(--primary-color);
        }
        
        .accordion-body {
            padding: 20px;
            background-color: rgba(25, 118, 210, 0.02);
        }
        
        /* 页脚 */
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
            margin-top: 50px;
        }
        
        .footer-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">Materials Studio 答疑平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="qa.html"><i class="fas fa-question-circle"></i> 问答</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html"><i class="fas fa-book"></i> 学习资源</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.html"><i class="fas fa-users"></i> 社区</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light" href="register.html"><i class="fas fa-user-plus"></i> 注册</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 页面标题区域 -->
    <section class="page-title-section">
        <div class="container text-center">
            <h1 class="page-title" data-aos="fade-up">会员中心</h1>
            <p class="lead" data-aos="fade-up" data-aos-delay="100">加入会员，获取更全面的 Materials Studio 专业知识支持</p>
        </div>
    </section>

    <!-- 会员优势 -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold" data-aos="fade-up">会员特权</h2>
                <p class="lead" data-aos="fade-up" data-aos-delay="100">升级会员，享受更全面的学习和支持体验</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-unlock-alt"></i>
                        </div>
                        <h3 class="feature-title">全部内容无限制</h3>
                        <p>访问所有专业问题解答和教程，包括会员专属内容，没有任何限制</p>
                    </div>
                </div>
                
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3 class="feature-title">高质量视频教程</h3>
                        <p>观看详细的 Materials Studio 操作视频教程，从基础到高级应用全面覆盖</p>
                    </div>
                </div>
                
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h3 class="feature-title">优先解答服务</h3>
                        <p>您提出的问题将获得专家优先解答，大大缩短等待时间</p>
                    </div>
                </div>
                
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <h3 class="feature-title">资源下载</h3>
                        <p>下载实用的模型文件、力场参数、脚本和教程文档等专业资源</p>
                    </div>
                </div>
                
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h3 class="feature-title">专家在线答疑</h3>
                        <p>定期参加专家在线答疑活动，直接与 Materials Studio 专家交流</p>
                    </div>
                </div>
                
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="700">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <h3 class="feature-title">会员认证</h3>
                        <p>获得专属会员标识，在社区中展示您的专业身份</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 会员价格 -->
    <section class="py-5 bg-white">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold" data-aos="fade-up">会员方案</h2>
                <p class="lead" data-aos="fade-up" data-aos-delay="100">选择最适合您的会员方案</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3 class="pricing-title">月度会员</h3>
                            <div class="pricing-price">¥49</div>
                            <div class="pricing-duration">每月</div>
                        </div>
                        <div class="pricing-description">
                            适合短期学习和项目需求
                        </div>
                        <ul class="pricing-features">
                            <li><i class="fas fa-check"></i> 访问所有会员内容</li>
                            <li><i class="fas fa-check"></i> 观看视频教程</li>
                            <li><i class="fas fa-check"></i> 提问优先解答</li>
                            <li><i class="fas fa-times"></i> 专家在线答疑</li>
                            <li><i class="fas fa-times"></i> 高级学习资源下载</li>
                            <li><i class="fas fa-times"></i> 会员专属社区</li>
                        </ul>
                        <a href="#" class="btn btn-outline-primary pricing-btn">立即订阅</a>
                    </div>
                </div>
                
                <div class="col-md-4" data-aos="fade-up">
                    <div class="pricing-card popular">
                        <div class="pricing-header">
                            <h3 class="pricing-title">年度会员</h3>
                            <div class="pricing-price popular">¥398</div>
                            <div class="pricing-duration">每年 (约¥33/月)</div>
                        </div>
                        <div class="pricing-description">
                            最受欢迎的选择，节省 32%
                        </div>
                        <ul class="pricing-features">
                            <li><i class="fas fa-check"></i> 访问所有会员内容</li>
                            <li><i class="fas fa-check"></i> 观看视频教程</li>
                            <li><i class="fas fa-check"></i> 提问优先解答</li>
                            <li><i class="fas fa-check"></i> 专家在线答疑</li>
                            <li><i class="fas fa-check"></i> 高级学习资源下载</li>
                            <li><i class="fas fa-check"></i> 会员专属社区</li>
                        </ul>
                        <a href="#" class="btn btn-warning pricing-btn">立即订阅</a>
                    </div>
                </div>
                
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3 class="pricing-title">团队会员</h3>
                            <div class="pricing-price">¥998</div>
                            <div class="pricing-duration">每年 (5人)</div>
                        </div>
                        <div class="pricing-description">
                            适合研究团队和实验室集体使用
                        </div>
                        <ul class="pricing-features">
                            <li><i class="fas fa-check"></i> 5个账号同时使用</li>
                            <li><i class="fas fa-check"></i> 所有年度会员特权</li>
                            <li><i class="fas fa-check"></i> 团队专属答疑通道</li>
                            <li><i class="fas fa-check"></i> 定制化教学支持</li>
                            <li><i class="fas fa-check"></i> 优先参与线下活动</li>
                            <li><i class="fas fa-check"></i> 团队管理控制台</li>
                        </ul>
                        <a href="#" class="btn btn-outline-primary pricing-btn">联系我们</a>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="200">
                <p class="mb-4">需要更多人数或特殊需求的团队方案？</p>
                <a href="#" class="btn btn-lg btn-outline-primary">联系我们获取定制方案</a>
            </div>
        </div>
    </section>

    <!-- 常见问题 -->
    <section class="faq-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold" data-aos="fade-up">常见问题</h2>
                <p class="lead" data-aos="fade-up" data-aos-delay="100">关于会员服务的常见问题解答</p>
            </div>
            
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item" data-aos="fade-up">
                    <h2 class="accordion-header" id="headingOne">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            如何成为会员？
                        </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            您可以在本页面选择适合您的会员方案，点击"立即订阅"按钮，然后按照页面提示完成支付流程。支付成功后，您的账号将立即升级为会员，并可以访问所有会员特权内容。
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item" data-aos="fade-up" data-aos-delay="100">
                    <h2 class="accordion-header" id="headingTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                            会员费用包括哪些内容？
                        </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            会员费用包括访问所有会员专属内容的权限，包括专业问答解析、视频教程、资源下载、优先提问服务等。不同的会员方案提供的具体服务可能有所不同，您可以在上方的会员方案对比中查看详细内容。
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item" data-aos="fade-up" data-aos-delay="200">
                    <h2 class="accordion-header" id="headingThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                            会员是否会自动续费？
                        </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            是的，会员服务默认为自动续费。在您的会员到期前，系统会自动从您的支付方式中扣除相应费用并续期。如果您不希望自动续费，可以在"个人中心"中的"会员管理"选项中关闭自动续费功能。
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item" data-aos="fade-up" data-aos-delay="300">
                    <h2 class="accordion-header" id="headingFour">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                            如何取消会员服务？
                        </button>
                    </h2>
                    <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            您可以随时在"个人中心"的"会员管理"页面中取消会员服务。取消后，您的会员权益将保持到当前订阅周期结束。我们不提供已付费订阅的退款，除非是特殊情况。
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item" data-aos="fade-up" data-aos-delay="400">
                    <h2 class="accordion-header" id="headingFive">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                            团队会员如何管理多个账号？
                        </button>
                    </h2>
                    <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            购买团队会员后，主账号管理员可以通过"团队管理控制台"添加和管理团队成员。您可以随时添加、删除或替换团队成员，只要不超过您购买的账号数量限制。每个团队成员都将拥有完整的会员权益。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h4 class="footer-title">Materials Studio 答疑平台</h4>
                    <p>专注于为材料科学研究者提供专业的 Materials Studio 软件使用指导、问题解答和资源共享。</p>
                    <div class="social-links mt-3">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">快速链接</h5>
                    <div class="footer-links">
                        <a href="index.html">首页</a>
                        <a href="qa.html">问答</a>
                        <a href="resources.html">资源</a>
                        <a href="community.html">社区</a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">帮助</h5>
                    <div class="footer-links">
                        <a href="#">常见问题</a>
                        <a href="#">使用指南</a>
                        <a href="#">联系我们</a>
                        <a href="#">意见反馈</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5 class="footer-title">联系我们</h5>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-phone me-2"></i> ************</p>
                    <p><i class="fas fa-map-marker-alt me-2"></i> 北京市海淀区中关村科技园</p>
                </div>
            </div>
            <hr class="mt-4 mb-3 bg-light">
            <div class="text-center py-3">
                <p class="mb-0">&copy; 2023 Materials Studio 答疑平台. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    
    <!-- API请求脚本 -->
    <script src="js/api/config.js"></script>
    <script src="js/api/utils.js"></script>
    <script src="js/api/auth.js"></script>
    <script src="js/api/user.js"></script>
    <script src="js/api/index.js"></script>

    <script>
        // 初始化动画
            AOS.init({
                duration: 800,
                once: true
            });
        
        // 在页面加载时检查用户登录状态并获取会员信息
        document.addEventListener('api:ready', async () => {
            try {
                // 更新导航栏和会员状态
                await updateNavbar();
                
                // 获取会员信息并更新UI
                await loadMembershipInfo();
                
                // 绑定事件监听器
                setupEventListeners();
                
            } catch (error) {
                console.error('页面初始化错误:', error);
                showNotification('加载会员信息失败，请稍后重试', 'error');
            }
        });
        
        // 更新导航栏显示
        async function updateNavbar() {
            const userNavElement = document.getElementById('user-nav');
            
            if (window.api.auth.isLoggedIn()) {
                try {
                    // 获取用户信息
                    const user = await window.api.auth.getCurrentUser();
                    const membershipInfo = await window.api.user.getMembershipInfo();
                    
                    // 更新导航栏显示已登录状态
                    let memberBadge = '';
                    if (membershipInfo && membershipInfo.is_active) {
                        memberBadge = `<span class="member-badge">${membershipInfo.level_name || '会员'}</span>`;
                    }
                    
                    userNavElement.innerHTML = `
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle"></i> ${user.username} ${memberBadge}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.html"><i class="fas fa-id-card"></i> 个人资料</a></li>
                                <li><a class="dropdown-item" href="membership.html"><i class="fas fa-crown"></i> 会员中心</a></li>
                                <li><a class="dropdown-item" href="favorites.html"><i class="fas fa-star"></i> 我的收藏</a></li>
                                <li><a class="dropdown-item" href="my-questions.html"><i class="fas fa-question-circle"></i> 我的提问</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                            </ul>
                        </li>
                    `;
                    
                    // 绑定退出登录按钮事件
                    document.getElementById('logout-btn').addEventListener('click', (event) => {
                        event.preventDefault();
                        window.api.auth.logout();
                        window.location.reload();
                    });
                    
                    return membershipInfo;
                } catch (error) {
                    console.error('获取用户信息失败:', error);
                    // 如果获取用户信息失败，可能是token无效，执行登出
                    window.api.auth.logout();
                    updateNavbarForGuest();
                    window.location.href = 'login.html';
                    return null;
                }
            } else {
                updateNavbarForGuest();
                
                // 用户未登录，跳转到登录页面
                showNotification('请先登录后查看会员信息', 'warning');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                
                return null;
            }
        }
        
        // 更新导航栏为游客状态
        function updateNavbarForGuest() {
            const userNavElement = document.getElementById('user-nav');
            userNavElement.innerHTML = `
                <li class="nav-item">
                    <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录/注册</a>
                </li>
            `;
        }
        
        // 加载会员信息
        async function loadMembershipInfo() {
            try {
                if (!window.api.auth.isLoggedIn()) {
                    return;
                }
                
                const membershipInfo = await window.api.user.getMembershipInfo();
                
                // 更新会员状态显示
                updateMembershipStatus(membershipInfo);
                
                // 更新会员方案选择
                updatePricingButtons(membershipInfo);
                
            } catch (error) {
                console.error('加载会员信息失败:', error);
                showNotification('加载会员信息失败，请稍后重试', 'error');
            }
        }
        
        // 更新会员状态显示
        function updateMembershipStatus(membershipInfo) {
            const membershipStatusSection = document.getElementById('membership-status');
            
            if (!membershipStatusSection) {
                // 创建会员状态显示区域
                const pricingSection = document.querySelector('.pricing-section .container');
                
                if (pricingSection) {
                    const statusDiv = document.createElement('div');
                    statusDiv.id = 'membership-status';
                    statusDiv.className = 'mb-5 p-4 rounded bg-light';
                    
                    if (membershipInfo && membershipInfo.is_active) {
                        // 用户是会员
                        const expiryDate = new Date(membershipInfo.expiry_date).toLocaleDateString();
                        statusDiv.innerHTML = `
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-crown fa-3x text-warning"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1">您当前的会员状态: <span class="badge bg-success">有效</span></h4>
                                    <p class="mb-1">会员级别: <strong>${membershipInfo.level_name || '标准会员'}</strong></p>
                                    <p class="mb-1">到期时间: <strong>${expiryDate}</strong></p>
                                    <p class="mb-0">自动续费: <strong>${membershipInfo.auto_renew ? '已开启' : '已关闭'}</strong> 
                                        <button id="toggle-auto-renew" class="btn btn-sm btn-outline-primary ms-2">
                                            ${membershipInfo.auto_renew ? '关闭自动续费' : '开启自动续费'}
                                        </button>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else {
                        // 用户不是会员
                        statusDiv.innerHTML = `
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-user fa-3x text-secondary"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1">您当前的会员状态: <span class="badge bg-secondary">非会员</span></h4>
                                    <p class="mb-0">成为会员，享受更多专业服务和内容</p>
                                </div>
                            </div>
                        `;
                    }
                    
                    // 插入到价格卡片前面
                    const rowElement = pricingSection.querySelector('.row');
                    pricingSection.insertBefore(statusDiv, rowElement);
                    
                    // 如果是会员，绑定自动续费切换事件
                    if (membershipInfo && membershipInfo.is_active) {
                        document.getElementById('toggle-auto-renew').addEventListener('click', async () => {
                            try {
                                await toggleAutoRenew(!membershipInfo.auto_renew);
                                // 刷新会员信息
                                await loadMembershipInfo();
                            } catch (error) {
                                console.error('切换自动续费失败:', error);
                                showNotification('切换自动续费设置失败，请稍后重试', 'error');
                            }
                        });
                    }
                }
            } else {
                // 更新已有的会员状态区域
                if (membershipInfo && membershipInfo.is_active) {
                    // 用户是会员
                    const expiryDate = new Date(membershipInfo.expiry_date).toLocaleDateString();
                    membershipStatusSection.innerHTML = `
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-crown fa-3x text-warning"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">您当前的会员状态: <span class="badge bg-success">有效</span></h4>
                                <p class="mb-1">会员级别: <strong>${membershipInfo.level_name || '标准会员'}</strong></p>
                                <p class="mb-1">到期时间: <strong>${expiryDate}</strong></p>
                                <p class="mb-0">自动续费: <strong>${membershipInfo.auto_renew ? '已开启' : '已关闭'}</strong> 
                                    <button id="toggle-auto-renew" class="btn btn-sm btn-outline-primary ms-2">
                                        ${membershipInfo.auto_renew ? '关闭自动续费' : '开启自动续费'}
                                    </button>
                                </p>
                            </div>
                        </div>
                    `;
                    
                    // 绑定自动续费切换事件
                    document.getElementById('toggle-auto-renew').addEventListener('click', async () => {
                        try {
                            await toggleAutoRenew(!membershipInfo.auto_renew);
                            // 刷新会员信息
                            await loadMembershipInfo();
                        } catch (error) {
                            console.error('切换自动续费失败:', error);
                            showNotification('切换自动续费设置失败，请稍后重试', 'error');
                        }
                    });
                } else {
                    // 用户不是会员
                    membershipStatusSection.innerHTML = `
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-user fa-3x text-secondary"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">您当前的会员状态: <span class="badge bg-secondary">非会员</span></h4>
                                <p class="mb-0">成为会员，享受更多专业服务和内容</p>
                            </div>
                        </div>
                    `;
                }
            }
        }
        
        // 更新会员方案按钮
        function updatePricingButtons(membershipInfo) {
            const pricingButtons = document.querySelectorAll('.pricing-btn');
            
            pricingButtons.forEach(button => {
                // 获取会员类型
                const planType = getPlanTypeFromButton(button);
                
                if (membershipInfo && membershipInfo.is_active) {
                    // 用户已经是会员
                    if (membershipInfo.level_name === planType) {
                        // 当前已是此类型会员
                        button.textContent = '当前方案';
                        button.classList.remove('btn-warning', 'btn-outline-primary');
                        button.classList.add('btn-success');
                        button.disabled = true;
                    } else if (isPlanUpgrade(membershipInfo.level_name, planType)) {
                        // 是升级方案
                        button.textContent = '升级方案';
                        button.onclick = () => handleSubscription(planType, 'upgrade');
                    } else {
                        // 是降级或平行方案
                        button.textContent = '切换方案';
                        button.onclick = () => handleSubscription(planType, 'change');
                    }
                } else {
                    // 用户不是会员，保持原有的订阅按钮
                    button.onclick = () => handleSubscription(planType, 'new');
                }
            });
        }
        
        // 从按钮推断会员计划类型
        function getPlanTypeFromButton(button) {
            const cardElement = button.closest('.pricing-card');
            const titleElement = cardElement.querySelector('.pricing-title');
            return titleElement.textContent.trim();
        }
        
        // 判断是否为升级方案
        function isPlanUpgrade(currentPlan, newPlan) {
            // 简单实现，根据会员名称判断级别
            const planLevels = {
                '月度会员': 1,
                '年度会员': 2,
                '团队会员': 3
            };
            
            return (planLevels[newPlan] || 0) > (planLevels[currentPlan] || 0);
        }
        
        // 处理会员订阅
        async function handleSubscription(planType, action) {
            try {
                // 这里应该调用实际的支付流程，目前只是模拟
                showNotification(`正在处理${action === 'new' ? '新订阅' : action === 'upgrade' ? '升级' : '方案变更'}请求...`, 'info');
                
                // 模拟支付处理延迟
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                // 模拟成功结果
                showNotification(`${planType}订阅成功！`, 'success');
                
                // 重新加载会员信息
                setTimeout(async () => {
                    await loadMembershipInfo();
                }, 1000);
                
            } catch (error) {
                console.error('处理订阅失败:', error);
                showNotification('处理订阅请求失败，请稍后重试', 'error');
            }
        }
        
        // 切换自动续费状态
        async function toggleAutoRenew(newStatus) {
            try {
                // 这里应该调用实际的API
                showNotification(`正在${newStatus ? '开启' : '关闭'}自动续费...`, 'info');
                
                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 模拟成功结果
                showNotification(`自动续费已${newStatus ? '开启' : '关闭'}`, 'success');
                
                return true;
            } catch (error) {
                console.error('切换自动续费失败:', error);
                throw error;
            }
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 可以添加其他事件监听器
        }
        
        // 显示通知消息
        function showNotification(message, type = 'info') {
            // 检查是否已有通知容器
            let notificationContainer = document.getElementById('notification-container');
            
            if (!notificationContainer) {
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'notification-container';
                notificationContainer.style.position = 'fixed';
                notificationContainer.style.top = '20px';
                notificationContainer.style.right = '20px';
                notificationContainer.style.zIndex = '9999';
                document.body.appendChild(notificationContainer);
            }
            
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show`;
            notification.role = 'alert';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            // 添加到容器
            notificationContainer.appendChild(notification);
            
            // 3秒后自动关闭
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 150);
            }, 3000);
        }
    </script>
</body>
</html> 