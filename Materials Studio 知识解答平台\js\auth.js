/**
 * Materials Studio 知识解答平台认证管理
 * 封装了用户认证、登录、注册、权限检查相关的功能
 */

// API基础URL
const API_BASE_URL = 'http://127.0.0.1:8000/api/v1';
const TOKEN_REFRESH_INTERVAL = 10 * 60 * 1000; // 10分钟

/**
 * 认证管理类
 */
class AuthManager {
  constructor() {
    this.token = localStorage.getItem('ms_token');
    this.user = JSON.parse(localStorage.getItem('ms_user') || 'null');
    this.initialized = false;
    this.authStateListeners = [];
    this.refreshInterval = null;
  }

  /**
   * 初始化认证管理器
   */
  async init() {
    if (this.initialized) return;

    // 如果有令牌，验证其有效性
    if (this.token) {
      try {
        const userData = await this.getUserInfo();
        this.setUser(userData);
        
        // 设置令牌刷新计时器
        this._setupTokenRefresh();
      } catch (error) {
        // 如果令牌已过期或无效，清除本地存储
        console.warn('令牌验证失败，将登出', error);
        this.logout();
      }
    }

    this.initialized = true;
    this._updateCSRFToken();
    
    // 通知所有监听器初始认证状态
    this._notifyAuthStateChange();
    
    // 添加页面可见性变化监听器
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && this.isAuthenticated()) {
        this._validateSession();
      }
    });
  }

  /**
   * 用户登录
   * @param {String} username - 用户名或邮箱
   * @param {String} password - 密码
   * @returns {Promise<Object>} - 用户信息
   */
  async login(username, password) {
    try {
      // 修改为表单格式提交，符合OAuth2PasswordRequestForm要求
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('password', password);
      
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-CSRF-Token': this._getCSRFToken()
        },
        credentials: 'include',
        body: formData
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || '登录失败');
      }

      // 保存令牌和用户信息
      this.token = data.access_token;
      localStorage.setItem('ms_token', this.token);
      
      // 获取用户信息
      const userInfo = await this.getUserInfo();
      this.setUser(userInfo);
      
      // 设置令牌刷新计时器
      this._setupTokenRefresh();

      return userInfo;
    } catch (error) {
      console.error('登录错误:', error);
      throw error;
    }
  }

  /**
   * 用户注册
   * @param {Object} userData - 用户注册数据
   * @returns {Promise<Object>} - 用户信息
   */
  async register(userData) {
    try {
      // 密码强度验证
      if (!this._validatePassword(userData.password)) {
        throw new Error('密码必须包含至少8个字符，包括大小写字母、数字和特殊字符');
      }
      
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': this._getCSRFToken()
        },
        credentials: 'include',
        body: JSON.stringify(userData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || '注册失败');
      }

      // 注册成功后，直接使用注册的用户名和密码登录
      return await this.login(userData.username || userData.email, userData.password);
    } catch (error) {
      console.error('注册错误:', error);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout() {
    // 清除令牌刷新计时器
    this._clearTokenRefresh();
    
    // 如果已经登录，向服务器发送登出请求
    if (this.token) {
      try {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: this._getAuthHeaders(),
          credentials: 'include'
        });
      } catch (error) {
        console.error('登出错误:', error);
      }
    }

    // 清除本地存储和状态
    localStorage.removeItem('ms_token');
    localStorage.removeItem('ms_user');
    this.token = null;
    this.user = null;

    // 通知所有监听器
    this._notifyAuthStateChange();
  }

  /**
   * 获取当前用户信息
   * @returns {Promise<Object>} - 用户信息
   */
  async getUserInfo() {
    try {
      const response = await fetch(`${API_BASE_URL}/users/me`, {
        headers: this._getAuthHeaders(),
        credentials: 'include'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || '获取用户信息失败');
      }

      return data;
    } catch (error) {
      console.error('获取用户信息错误:', error);
      throw error;
    }
  }

  /**
   * 验证密码强度
   * @private
   * @param {String} password - 密码
   * @returns {Boolean} - 是否符合强度要求
   */
  _validatePassword(password) {
    // 至少8个字符，包含大写字母、小写字母、数字和特殊字符
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return regex.test(password);
  }

  /**
   * 设置用户信息
   * @param {Object} userData - 用户数据
   */
  setUser(userData) {
    this.user = userData;
    localStorage.setItem('ms_user', JSON.stringify(userData));
    
    // 通知所有监听器
    this._notifyAuthStateChange();
  }

  /**
   * 检查用户是否已认证
   * @returns {Boolean} - 是否已认证
   */
  isAuthenticated() {
    return !!this.token && !!this.user;
  }

  /**
   * 检查用户是否具有指定角色
   * @param {String|Array} roles - 角色或角色数组
   * @returns {Boolean} - 是否具有角色
   */
  hasRole(roles) {
    if (!this.isAuthenticated() || !this.user.roles) {
      return false;
    }

    const userRoles = this.user.roles.map(role => role.name);
    
    if (Array.isArray(roles)) {
      return roles.some(role => userRoles.includes(role));
    }
    
    return userRoles.includes(roles);
  }

  /**
   * 检查用户是否是管理员
   * @returns {Boolean} - 是否是管理员
   */
  isAdmin() {
    return this.hasRole(['admin']);
  }

  /**
   * 检查用户是否是版主
   * @returns {Boolean} - 是否是版主
   */
  isModerator() {
    return this.hasRole(['admin', 'moderator']);
  }

  /**
   * 检查用户是否是高级用户
   * @returns {Boolean} - 是否是高级用户
   */
  isPremium() {
    return this.hasRole(['admin', 'moderator', 'expert', 'premium']);
  }

  /**
   * 添加认证状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  addAuthStateListener(listener) {
    if (typeof listener === 'function' && !this.authStateListeners.includes(listener)) {
      this.authStateListeners.push(listener);
    }
  }

  /**
   * 移除认证状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeAuthStateListener(listener) {
    const index = this.authStateListeners.indexOf(listener);
    if (index > -1) {
      this.authStateListeners.splice(index, 1);
    }
  }

  /**
   * 通知所有认证状态变化监听器
   * @private
   */
  _notifyAuthStateChange() {
    this.authStateListeners.forEach(listener => {
      try {
        listener({
          isAuthenticated: this.isAuthenticated(),
          user: this.user
        });
      } catch (error) {
        console.error('认证状态监听器错误:', error);
      }
    });

    // 更新UI元素
    this._updateAuthUI();
  }

  /**
   * 更新认证相关的UI元素
   * @private
   */
  _updateAuthUI() {
    // 更新导航栏上的用户信息
    const userInfoEl = document.getElementById('user-info');
    const loginButtonsEl = document.getElementById('login-buttons');
    
    if (userInfoEl && loginButtonsEl) {
      if (this.isAuthenticated()) {
        userInfoEl.style.display = 'block';
        loginButtonsEl.style.display = 'none';
        
        // 更新用户名和头像
        const userNameEl = document.getElementById('user-name');
        const userAvatarEl = document.getElementById('user-avatar');
        const userRoleEl = document.getElementById('user-role');
        
        if (userNameEl) {
          userNameEl.textContent = this.user.name || this.user.username;
        }
        
        if (userAvatarEl && this.user.avatar) {
          userAvatarEl.src = this.user.avatar;
        } else if (userAvatarEl) {
          // 默认头像
          userAvatarEl.src = '/img/default-avatar.png';
        }
        
        if (userRoleEl && this.user.roles && this.user.roles.length > 0) {
          userRoleEl.textContent = this.user.roles[0].displayName;
          userRoleEl.style.backgroundColor = this.user.roles[0].color || '#1976d2';
        }
      } else {
        userInfoEl.style.display = 'none';
        loginButtonsEl.style.display = 'block';
      }
    }

    // 更新需要认证才可见的元素
    document.querySelectorAll('[data-auth-required]').forEach(el => {
      el.style.display = this.isAuthenticated() ? '' : 'none';
    });

    // 更新需要特定角色才可见的元素
    document.querySelectorAll('[data-role-required]').forEach(el => {
      const requiredRoles = el.dataset.roleRequired.split(',');
      el.style.display = this.hasRole(requiredRoles) ? '' : 'none';
    });
  }

  /**
   * 获取包含认证头的请求头对象
   * @private
   * @returns {Object} - 请求头对象
   */
  _getAuthHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };
    
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }
    
    const csrfToken = this._getCSRFToken();
    if (csrfToken) {
      headers['X-CSRF-Token'] = csrfToken;
    }
    
    return headers;
  }

  /**
   * 发送API请求的通用方法
   * @param {String} endpoint - API端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} - 响应数据
   */
  async fetchAPI(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    
    // 合并头信息
    const headers = {
      ...this._getAuthHeaders(),
      ...(options.headers || {})
    };
    
    try {
      const response = await fetch(url, {
        ...options,
        headers
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `请求失败: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API请求错误:', error);
      
      // 如果是401错误，可能是令牌过期
      if (error.message.includes('401')) {
        this.logout();
      }
      
      throw error;
    }
  }

  /**
   * 获取CSRF令牌
   * @private
   * @returns {String} - CSRF令牌
   */
  _getCSRFToken() {
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
  }

  /**
   * 更新CSRF令牌
   * @private
   */
  async _updateCSRFToken() {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/csrf-token`);
      const data = await response.json();
      
      if (response.ok && data.csrfToken) {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
          metaTag.setAttribute('content', data.csrfToken);
        } else {
          const meta = document.createElement('meta');
          meta.name = 'csrf-token';
          meta.content = data.csrfToken;
          document.head.appendChild(meta);
        }
      }
    } catch (error) {
      console.error('获取CSRF令牌失败:', error);
    }
  }

  /**
   * 设置令牌刷新计时器
   * @private
   */
  _setupTokenRefresh() {
    // 清除现有计时器
    this._clearTokenRefresh();
    
    // 设置新计时器
    this.refreshInterval = setInterval(async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
          method: 'POST',
          headers: this._getAuthHeaders(),
          credentials: 'include'
        });
        
        const data = await response.json();
        
        if (response.ok && data.token) {
          this.token = data.token;
          localStorage.setItem('ms_token', this.token);
        } else {
          // 刷新失败，可能是会话已过期
          this.logout();
        }
      } catch (error) {
        console.error('令牌刷新失败:', error);
        // 发生错误时，尝试再次验证会话
        this._validateSession();
      }
    }, TOKEN_REFRESH_INTERVAL);
  }

  /**
   * 清除令牌刷新计时器
   * @private
   */
  _clearTokenRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * 验证当前会话
   * @private
   */
  async _validateSession() {
    if (!this.token) return;
    
    try {
      await this.getUserInfo();
    } catch (error) {
      console.warn('会话验证失败，将登出', error);
      this.logout();
    }
  }
}

// 创建并导出单例实例
const authManager = new AuthManager();
export default authManager; 