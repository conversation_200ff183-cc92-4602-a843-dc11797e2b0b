<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面 - Materials Studio 答疑平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body {
            padding: 20px;
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
        }
        .api-card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card-header {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        .result-container {
            max-height: 300px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
        }
        .api-endpoint {
            color: #0d6efd;
            font-weight: bold;
        }
        .test-button {
            min-width: 100px;
        }
        .status-success {
            color: #198754;
        }
        .status-error {
            color: #dc3545;
        }
        #api-log {
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-info {
            color: #0d6efd;
        }
        .log-error {
            color: #dc3545;
        }
        .log-debug {
            color: #6c757d;
        }
        .log-warn {
            color: #ffc107;
        }
        #debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            font-family: monospace;
            max-height: 150px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">API测试页面</h1>
        
        <!-- 调试信息 -->
        <div id="debug-info" class="mb-4">
            <h5>调试信息</h5>
            <div id="debug-content">载入中...</div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">API设置</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">API基础URL</label>
                            <div class="input-group">
                                <input type="text" id="api-base-url" class="form-control" value="http://localhost:8000/api/v1">
                                <button class="btn btn-primary" id="set-base-url">设置</button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">日志级别</label>
                            <select id="log-level" class="form-select">
                                <option value="debug">调试 (Debug)</option>
                                <option value="info" selected>信息 (Info)</option>
                                <option value="warn">警告 (Warn)</option>
                                <option value="error">错误 (Error)</option>
                            </select>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="debug-mode" checked>
                            <label class="form-check-label" for="debug-mode">开启调试模式</label>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button id="clear-token" class="btn btn-outline-danger">
                                <i class="fas fa-trash-alt"></i> 清除Token
                            </button>
                            <button id="clear-log" class="btn btn-outline-secondary">
                                <i class="fas fa-eraser"></i> 清除日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">API状态</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">服务器状态</label>
                            <div id="server-status" class="alert alert-secondary">未检查</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">认证状态</label>
                            <div id="auth-status" class="alert alert-secondary">未检查</div>
                        </div>
                        <button id="check-server" class="btn btn-primary">
                            <i class="fas fa-server"></i> 检查服务器
                        </button>
                        <button id="check-auth" class="btn btn-info text-white ms-2">
                            <i class="fas fa-user-check"></i> 检查认证
                        </button>
                        <button id="test-direct-fetch" class="btn btn-warning text-white mt-2">
                            <i class="fas fa-bolt"></i> 直接测试
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <ul class="nav nav-tabs" id="apiTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="auth-tab" data-bs-toggle="tab" data-bs-target="#auth" type="button" role="tab">认证 API</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="qa-tab" data-bs-toggle="tab" data-bs-target="#qa" type="button" role="tab">问答 API</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="user-tab" data-bs-toggle="tab" data-bs-target="#user" type="button" role="tab">用户 API</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">内容 API</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="log-tab" data-bs-toggle="tab" data-bs-target="#log" type="button" role="tab">API 日志</button>
            </li>
        </ul>
        
        <div class="tab-content mt-3" id="apiTabContent">
            <!-- 认证 API -->
            <div class="tab-pane fade show active" id="auth" role="tabpanel" aria-labelledby="auth-tab">
                <div class="card api-card">
                    <div class="card-header">登录 <span class="api-endpoint">POST /auth/login</span></div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <input type="text" id="login-username" class="form-control mb-2" placeholder="用户名或邮箱">
                                <input type="password" id="login-password" class="form-control mb-2" placeholder="密码">
                                <button id="test-login" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="login-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card api-card">
                    <div class="card-header">获取当前用户信息 <span class="api-endpoint">GET /auth/me</span></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>获取当前已登录用户的信息</p>
                                <button id="test-me" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="me-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 问答 API -->
            <div class="tab-pane fade" id="qa" role="tabpanel" aria-labelledby="qa-tab">
                <div class="card api-card">
                    <div class="card-header">获取问题列表 <span class="api-endpoint">GET /qa/questions</span></div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <div class="input-group">
                                        <span class="input-group-text">跳过</span>
                                        <input type="number" id="questions-skip" class="form-control" value="0">
                                        <span class="input-group-text">数量</span>
                                        <input type="number" id="questions-limit" class="form-control" value="10">
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <input type="text" id="questions-search" class="form-control" placeholder="搜索关键词">
                                </div>
                                <div class="mb-2">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="questions-premium">
                                        <label class="form-check-label" for="questions-premium">仅会员内容</label>
                                    </div>
                                </div>
                                <button id="test-questions" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="questions-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card api-card">
                    <div class="card-header">获取问题详情 <span class="api-endpoint">GET /qa/questions/{id}</span></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <input type="number" id="question-id" class="form-control" placeholder="问题ID">
                                </div>
                                <button id="test-question" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="question-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card api-card">
                    <div class="card-header">获取标签列表 <span class="api-endpoint">GET /qa/tags</span></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>获取所有可用的问题标签</p>
                                <button id="test-tags" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="tags-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 用户 API -->
            <div class="tab-pane fade" id="user" role="tabpanel" aria-labelledby="user-tab">
                <div class="card api-card">
                    <div class="card-header">获取会员信息 <span class="api-endpoint">GET /users/membership</span></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>获取当前用户的会员信息</p>
                                <button id="test-membership" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="membership-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card api-card">
                    <div class="card-header">获取用户信息 <span class="api-endpoint">GET /users/{id}</span></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <input type="number" id="user-id" class="form-control" placeholder="用户ID">
                                </div>
                                <button id="test-user" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="user-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 内容 API -->
            <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
                <div class="card api-card">
                    <div class="card-header">获取资源列表 <span class="api-endpoint">GET /content/resources</span></div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <div class="input-group">
                                        <span class="input-group-text">跳过</span>
                                        <input type="number" id="resources-skip" class="form-control" value="0">
                                        <span class="input-group-text">数量</span>
                                        <input type="number" id="resources-limit" class="form-control" value="10">
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <input type="text" id="resources-category" class="form-control" placeholder="分类ID">
                                </div>
                                <button id="test-resources" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="resources-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card api-card">
                    <div class="card-header">获取资源分类 <span class="api-endpoint">GET /content/categories</span></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>获取所有资源分类</p>
                                <button id="test-categories" class="btn btn-primary test-button">测试</button>
                            </div>
                            <div class="col-md-6">
                                <div class="result-container" id="categories-result">结果将显示在这里...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- API 日志 -->
            <div class="tab-pane fade" id="log" role="tabpanel" aria-labelledby="log-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>API 请求日志</span>
                        <button class="btn btn-sm btn-outline-secondary" id="clear-api-log">
                            <i class="fas fa-eraser"></i> 清除日志
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div id="api-log" class="p-2">
                            <div class="log-entry log-info">日志将显示在这里...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入API和其他库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 修改加载顺序，确保utils.js先加载 -->
    <script src="js/api/config.js"></script>
    <script src="js/api/utils.js"></script>
    <script>
        // 确保utils.js已经加载
        console.log('Utils模块状态:', typeof window.apiUtils !== 'undefined' ? '已加载' : '未加载');
    </script>
    <script src="js/api/auth.js"></script>
    <script src="js/api/qa.js"></script>
    <script src="js/api/user.js"></script>
    <script src="js/api/content.js"></script>
    <script src="js/api/index.js"></script>
    
    <script>
        // 调试信息
        const debugContent = document.getElementById('debug-content');
        debugContent.innerHTML = '正在检查环境...';
        
        let debugInfo = {
            apiBaseUrl: 'http://localhost:8000/api/v1',
            apiObjects: {},
            jsLoaded: {},
            errors: []
        };
        
        function updateDebugInfo() {
            // 检查JS文件是否加载
            debugInfo.jsLoaded = {
                utils: typeof window.apiUtils !== 'undefined',
                auth: typeof window.authAPI !== 'undefined',
                qa: typeof window.qaAPI !== 'undefined',
                user: typeof window.userAPI !== 'undefined',
                content: typeof window.contentAPI !== 'undefined',
                api: typeof window.api !== 'undefined'
            };
            
            // 检查API对象
            if (window.api) {
                debugInfo.apiObjects = {
                    utils: typeof window.api.utils !== 'undefined',
                    auth: typeof window.api.auth !== 'undefined',
                    qa: typeof window.api.qa !== 'undefined',
                    user: typeof window.api.user !== 'undefined',
                    content: typeof window.api.content !== 'undefined'
                };
            }
            
            // 显示调试信息
            debugContent.innerHTML = '<pre>' + JSON.stringify(debugInfo, null, 2) + '</pre>';
        }
        
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', () => {
            // 打印一条消息到控制台，帮助调试
            console.log('DOM内容已加载');
            updateDebugInfo();
            
            // 直接测试按钮
            document.getElementById('test-direct-fetch').addEventListener('click', async () => {
                const serverStatus = document.getElementById('server-status');
                serverStatus.className = 'alert alert-info';
                serverStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
                
                try {
                    const response = await fetch('http://localhost:8000/api/v1/health');
                    const data = await response.text();
                    
                    debugInfo.directTest = {
                        status: response.status,
                        ok: response.ok,
                        data: data
                    };
                    
                    if (response.ok) {
                        serverStatus.className = 'alert alert-success';
                        serverStatus.innerHTML = '<i class="fas fa-check-circle"></i> 直接测试成功: ' + data;
                    } else {
                        serverStatus.className = 'alert alert-danger';
                        serverStatus.innerHTML = '<i class="fas fa-times-circle"></i> 直接测试失败: ' + response.status;
                    }
                } catch (error) {
                    debugInfo.errors.push(error.message);
                    serverStatus.className = 'alert alert-danger';
                    serverStatus.innerHTML = `<i class="fas fa-times-circle"></i> 错误: ${error.message}`;
                }
                
                updateDebugInfo();
            });
            
            // 初始化设置
            const apiBaseUrl = document.getElementById('api-base-url');
            const logLevel = document.getElementById('log-level');
            const debugMode = document.getElementById('debug-mode');
            
            // 监听API就绪事件
            document.addEventListener('api:ready', () => {
                console.log('API 已加载，API对象:', window.api);
                updateDebugInfo();
                
                // 自定义日志记录器
                const logContainer = document.getElementById('api-log');
                
                // 替换原生的console方法来捕获API日志
                const originalConsoleLog = console.log;
                const originalConsoleInfo = console.info;
                const originalConsoleWarn = console.warn;
                const originalConsoleError = console.error;
                
                function addLogEntry(message, level = 'info') {
                    // 只捕获API相关日志
                    if (typeof message === 'string' && message.startsWith('[API')) {
                        const logEntry = document.createElement('div');
                        logEntry.className = `log-entry log-${level}`;
                        
                        // 格式化时间
                        const now = new Date();
                        const timeStr = now.toTimeString().split(' ')[0];
                        
                        // 拼接消息内容
                        let logContent = `[${timeStr}] ${message}`;
                        
                        // 处理额外参数
                        const args = Array.prototype.slice.call(arguments, 2);
                        if (args.length > 0) {
                            args.forEach(arg => {
                                if (typeof arg === 'object') {
                                    try {
                                        logContent += ' ' + JSON.stringify(arg);
                                    } catch (e) {
                                        logContent += ' [Object]';
                                    }
                                } else if (arg !== undefined) {
                                    logContent += ' ' + arg.toString();
                                }
                            });
                        }
                        
                        logEntry.textContent = logContent;
                        logContainer.prepend(logEntry);
                    }
                    
                    // 调用原始方法，保持控制台功能
                    switch (level) {
                        case 'info':
                            return originalConsoleInfo.apply(console, arguments);
                        case 'warn':
                            return originalConsoleWarn.apply(console, arguments);
                        case 'error':
                            return originalConsoleError.apply(console, arguments);
                        default:
                            return originalConsoleLog.apply(console, arguments);
                    }
                }
                
                // 替换console方法
                console.log = function() {
                    addLogEntry(arguments[0], 'debug', ...Array.prototype.slice.call(arguments, 1));
                };
                
                console.info = function() {
                    addLogEntry(arguments[0], 'info', ...Array.prototype.slice.call(arguments, 1));
                };
                
                console.warn = function() {
                    addLogEntry(arguments[0], 'warn', ...Array.prototype.slice.call(arguments, 1));
                };
                
                console.error = function() {
                    addLogEntry(arguments[0], 'error', ...Array.prototype.slice.call(arguments, 1));
                };
                
                // 初始化设置
                apiBaseUrl.value = window.api.utils.settings.baseUrl;
                logLevel.value = window.api.utils.settings.logLevel;
                debugMode.checked = window.api.utils.settings.debug;
                
                // 绑定设置按钮事件
                document.getElementById('set-base-url').addEventListener('click', () => {
                    window.api.utils.setBaseUrl(apiBaseUrl.value);
                });
                
                // 绑定日志级别选择事件
                logLevel.addEventListener('change', () => {
                    window.api.utils.setLogLevel(logLevel.value);
                });
                
                // 绑定调试模式切换事件
                debugMode.addEventListener('change', () => {
                    window.api.utils.setDebugMode(debugMode.checked);
                });
                
                // 绑定清除Token按钮事件
                document.getElementById('clear-token').addEventListener('click', () => {
                    localStorage.removeItem('token');
                    localStorage.removeItem('token_type');
                    updateAuthStatus();
                    addLogEntry('[API INFO] Token已清除', 'info');
                });
                
                // 绑定清除日志按钮事件
                document.getElementById('clear-log').addEventListener('click', () => {
                    logContainer.innerHTML = '';
                });
                
                document.getElementById('clear-api-log').addEventListener('click', () => {
                    logContainer.innerHTML = '';
                });
                
                // 更新认证状态
                function updateAuthStatus() {
                    const authStatus = document.getElementById('auth-status');
                    const token = localStorage.getItem('token');
                    
                    if (token) {
                        authStatus.className = 'alert alert-success';
                        authStatus.innerHTML = '<i class="fas fa-check-circle"></i> 已认证';
                    } else {
                        authStatus.className = 'alert alert-warning';
                        authStatus.innerHTML = '<i class="fas fa-exclamation-circle"></i> 未认证';
                    }
                }
                
                // 检查服务器状态
                document.getElementById('check-server').addEventListener('click', async () => {
                    const serverStatus = document.getElementById('server-status');
                    serverStatus.className = 'alert alert-info';
                    serverStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';
                    
                    try {
                        const isAvailable = await window.api.utils.checkServerStatus();
                        
                        if (isAvailable) {
                            serverStatus.className = 'alert alert-success';
                            serverStatus.innerHTML = '<i class="fas fa-check-circle"></i> 服务器可用';
                        } else {
                            serverStatus.className = 'alert alert-danger';
                            serverStatus.innerHTML = '<i class="fas fa-times-circle"></i> 服务器不可用';
                        }
                    } catch (error) {
                        serverStatus.className = 'alert alert-danger';
                        serverStatus.innerHTML = `<i class="fas fa-times-circle"></i> 错误: ${error.message}`;
                    }
                });
                
                // 检查认证状态
                document.getElementById('check-auth').addEventListener('click', updateAuthStatus);
                
                // 初始检查认证状态
                updateAuthStatus();
                
                // 登录测试
                document.getElementById('test-login').addEventListener('click', async () => {
                    const username = document.getElementById('login-username').value;
                    const password = document.getElementById('login-password').value;
                    const resultElement = document.getElementById('login-result');
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const result = await window.api.auth.login(username, password);
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                        updateAuthStatus();
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取用户信息测试
                document.getElementById('test-me').addEventListener('click', async () => {
                    const resultElement = document.getElementById('me-result');
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const result = await window.api.auth.getCurrentUser();
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取问题列表测试
                document.getElementById('test-questions').addEventListener('click', async () => {
                    const skip = document.getElementById('questions-skip').value;
                    const limit = document.getElementById('questions-limit').value;
                    const search = document.getElementById('questions-search').value;
                    const onlyPremium = document.getElementById('questions-premium').checked;
                    const resultElement = document.getElementById('questions-result');
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const params = {
                            skip: skip,
                            limit: limit
                        };
                        
                        if (search) params.search = search;
                        if (onlyPremium) params.only_premium = true;
                        
                        const result = await window.api.qa.getQuestions(params);
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取问题详情测试
                document.getElementById('test-question').addEventListener('click', async () => {
                    const questionId = document.getElementById('question-id').value;
                    const resultElement = document.getElementById('question-result');
                    
                    if (!questionId) {
                        resultElement.innerHTML = `<pre class="status-error">错误: 请输入问题ID</pre>`;
                        return;
                    }
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const result = await window.api.qa.getQuestion(questionId);
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取标签列表测试
                document.getElementById('test-tags').addEventListener('click', async () => {
                    const resultElement = document.getElementById('tags-result');
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const result = await window.api.qa.getTags();
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取会员信息测试
                document.getElementById('test-membership').addEventListener('click', async () => {
                    const resultElement = document.getElementById('membership-result');
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const result = await window.api.user.getMembershipInfo();
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取用户信息测试
                document.getElementById('test-user').addEventListener('click', async () => {
                    const userId = document.getElementById('user-id').value;
                    const resultElement = document.getElementById('user-result');
                    
                    if (!userId) {
                        resultElement.innerHTML = `<pre class="status-error">错误: 请输入用户ID</pre>`;
                        return;
                    }
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const result = await window.api.user.getUserProfile(userId);
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取资源列表测试
                document.getElementById('test-resources').addEventListener('click', async () => {
                    const skip = document.getElementById('resources-skip').value;
                    const limit = document.getElementById('resources-limit').value;
                    const category = document.getElementById('resources-category').value;
                    const resultElement = document.getElementById('resources-result');
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const params = {
                            skip: skip,
                            limit: limit
                        };
                        
                        if (category) params.category_id = category;
                        
                        const result = await window.api.content.getResources(params);
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
                
                // 获取资源分类测试
                document.getElementById('test-categories').addEventListener('click', async () => {
                    const resultElement = document.getElementById('categories-result');
                    
                    resultElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>正在请求...</div></div>';
                    
                    try {
                        const result = await window.api.content.getCategories();
                        resultElement.innerHTML = `<pre class="status-success">${JSON.stringify(result, null, 2)}</pre>`;
                    } catch (error) {
                        resultElement.innerHTML = `<pre class="status-error">错误: ${error.message}</pre>`;
                    }
                });
            });
            
            // 添加一个辅助事件，确保API就绪事件已经被监听
            setTimeout(() => {
                console.log('触发API就绪检查...');
                updateDebugInfo();
                if (window.api) {
                    console.log('API对象已存在');
                    const apiReadyEvent = new CustomEvent('api:ready');
                    document.dispatchEvent(apiReadyEvent);
                } else {
                    console.error('API对象不存在，可能index.js未正确加载或执行');
                    debugInfo.errors.push('API对象不存在，可能index.js未正确加载或执行');
                    updateDebugInfo();
                }
            }, 1000);
        });
    </script>
</body>
</html> 