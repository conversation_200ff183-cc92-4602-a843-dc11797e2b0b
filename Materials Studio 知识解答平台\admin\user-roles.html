<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户角色与权限管理 - Materials Studio 知识解答平台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Sortable.js -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        :root {
            --primary-color: #0056b3;
            --secondary-color: #004494;
            --border-radius: 10px;
            --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }
        
        .sidebar {
            background-color: #fff;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            padding: 20px;
        }
        
        .sidebar .nav-link {
            color: #333;
            border-radius: 5px;
            margin-bottom: 5px;
            padding: 10px 15px;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(0,86,179,0.1);
        }
        
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
        }
        
        /* 快捷操作按钮样式 */
        .floating-action-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .floating-action-btn:hover {
            background-color: var(--secondary-color);
            transform: scale(1.1);
            box-shadow: 0 6px 15px rgba(0,0,0,0.25);
        }
        
        .floating-action-btn i {
            font-size: 1.5rem;
        }
        
        /* 模态框样式优化 */
        .custom-modal .modal-header {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .permission-group {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .permission-group h5 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .role-color-picker {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .color-option {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .color-option:hover {
            transform: scale(1.1);
        }
        
        .color-option.selected {
            border: 2px solid #333;
        }
        
        /* 拖放权限分配样式 */
        .permission-grid {
            display: flex;
            margin-bottom: 20px;
        }
        
        .permission-source {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-right: 15px;
            background-color: #fff;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .permission-target {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: var(--border-radius);
            padding: 15px;
            background-color: #f8f9fa;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .permission-item {
            padding: 8px 15px;
            margin-bottom: 8px;
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            cursor: move;
            transition: all 0.2s;
            display: flex;
            align-items: center;
        }
        
        .permission-item:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .permission-item i {
            margin-right: 8px;
            color: var(--primary-color);
        }
        
        .permission-item .permission-desc {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }
        
        .permission-item.assigned {
            background-color: #e9f7ef;
            border-color: #28a745;
        }
        
        .permission-target-placeholder {
            color: #6c757d;
            text-align: center;
            padding: 20px;
            font-style: italic;
        }
        
        /* 角色徽章样式增强 */
        .role-badge {
            display: inline-block;
            padding: 3px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
            color: white;
            position: relative;
        }
        
        .role-badge.editable {
            cursor: pointer;
            padding-right: 25px;
        }
        
        .role-badge.editable:hover {
            opacity: 0.9;
        }
        
        .role-badge.editable::after {
            content: '\f044';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.7rem;
        }
        
        /* 权限更改日志样式 */
        .change-log-item {
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .change-log-item:last-child {
            border-bottom: none;
        }
        
        .change-log-item .log-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .change-log-item .log-action {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .change-log-item .log-user {
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.html">
                <img src="https://via.placeholder.com/150x40?text=MS+知识平台" alt="Materials Studio 知识解答平台">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 管理控制台</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="adminDropdown" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> 管理员
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog"></i> 个人设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar">
                    <h5 class="mb-3"><i class="fas fa-cogs"></i> 系统管理</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="fas fa-tachometer-alt"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="user-roles.html">
                                <i class="fas fa-users-cog"></i> 用户角色与权限
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="user-management.html">
                                <i class="fas fa-user-edit"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="content-management.html">
                                <i class="fas fa-file-alt"></i> 内容管理
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-lg-9">
                <h3 class="mb-4">用户角色与权限管理</h3>
                
                <!-- 角色管理卡片 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-user-tag me-2"></i>用户角色管理</span>
                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                            <i class="fas fa-plus me-1"></i> 新增角色
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card" style="border-left: 5px solid #6c757d;">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="fas fa-user me-2 text-secondary"></i>游客</h5>
                                        <p class="card-text text-muted">未注册用户，仅可浏览基础内容</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-secondary">默认角色</span>
                                            <button class="btn btn-sm btn-outline-primary" disabled>编辑</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card" style="border-left: 5px solid #17a2b8;">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="fas fa-user-check me-2 text-info"></i>注册用户</h5>
                                        <p class="card-text text-muted">可提问和参与基础讨论的用户</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-info">基础权限</span>
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editRoleModal" data-role="registered">编辑</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card" style="border-left: 5px solid #ffc107;">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="fas fa-crown me-2 text-warning"></i>高级会员</h5>
                                        <p class="card-text text-muted">可访问完整内容和高级功能</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-warning text-dark">付费会员</span>
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editRoleModal" data-role="premium">编辑</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 权限分配卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-key me-2"></i>权限分配
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>选择一个角色进行权限配置，或根据需要创建新的权限组。
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <select class="form-select" id="roleSelect">
                                    <option selected>选择角色...</option>
                                    <option value="visitor">游客</option>
                                    <option value="user">注册用户</option>
                                    <option value="member">高级会员</option>
                                    <option value="expert">专家用户</option>
                                    <option value="moderator">版主</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                            <div class="col-md-6 text-md-end mt-3 mt-md-0">
                                <button class="btn btn-primary me-2" id="savePermissions"><i class="fas fa-save me-1"></i> 保存更改</button>
                                <button class="btn btn-outline-secondary" id="resetPermissions"><i class="fas fa-undo me-1"></i> 重置</button>
                            </div>
                        </div>
                        
                        <!-- 拖放式权限分配界面 -->
                        <div class="permission-grid mb-4">
                            <div class="permission-source">
                                <h6 class="mb-3">可用权限</h6>
                                <div id="availablePermissions">
                                    <!-- 可用权限项，可以拖放到右侧 -->
                                    <div class="permission-item" data-permission="questions.view">
                                        <i class="fas fa-eye"></i>
                                        <div>
                                            <div>查看问题</div>
                                            <div class="permission-desc">允许查看所有问题和回答</div>
                                        </div>
                                    </div>
                                    <div class="permission-item" data-permission="questions.create">
                                        <i class="fas fa-plus-circle"></i>
                                        <div>
                                            <div>创建问题</div>
                                            <div class="permission-desc">允许创建新问题</div>
                                        </div>
                                    </div>
                                    <div class="permission-item" data-permission="answers.create">
                                        <i class="fas fa-reply"></i>
                                        <div>
                                            <div>回答问题</div>
                                            <div class="permission-desc">允许回答问题</div>
                                        </div>
                                    </div>
                                    <div class="permission-item" data-permission="answers.edit">
                                        <i class="fas fa-edit"></i>
                                        <div>
                                            <div>编辑回答</div>
                                            <div class="permission-desc">允许编辑自己的回答</div>
                                        </div>
                                    </div>
                                    <div class="permission-item" data-permission="comments.create">
                                        <i class="fas fa-comment"></i>
                                        <div>
                                            <div>添加评论</div>
                                            <div class="permission-desc">允许添加评论</div>
                                        </div>
                                    </div>
                                    <div class="permission-item" data-permission="resources.download">
                                        <i class="fas fa-download"></i>
                                        <div>
                                            <div>下载资源</div>
                                            <div class="permission-desc">允许下载资源文件</div>
                                        </div>
                                    </div>
                                    <div class="permission-item" data-permission="resources.upload">
                                        <i class="fas fa-upload"></i>
                                        <div>
                                            <div>上传资源</div>
                                            <div class="permission-desc">允许上传资源文件</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="permission-target">
                                <h6 class="mb-3">已分配权限</h6>
                                <div id="assignedPermissions">
                                    <div class="permission-target-placeholder">
                                        选择一个角色来管理其权限
                                    </div>
                                    <!-- 已分配的权限会显示在这里 -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- 权限变更历史 -->
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-history me-2"></i>最近权限变更
                            </div>
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush">
                                    <div class="change-log-item">
                                        <div class="d-flex justify-content-between">
                                            <span class="log-action">添加权限 "编辑回答" 到 "专家用户"</span>
                                            <span class="log-time">今天 14:30</span>
                                        </div>
                                        <div class="text-muted small">
                                            由 <span class="log-user">管理员</span> 操作
                                        </div>
                                    </div>
                                    <div class="change-log-item">
                                        <div class="d-flex justify-content-between">
                                            <span class="log-action">移除权限 "上传资源" 从 "注册用户"</span>
                                            <span class="log-time">昨天 11:15</span>
                                        </div>
                                        <div class="text-muted small">
                                            由 <span class="log-user">系统管理员</span> 操作
                                        </div>
                                    </div>
                                    <div class="change-log-item">
                                        <div class="d-flex justify-content-between">
                                            <span class="log-action">创建新角色 "课题组长"</span>
                                            <span class="log-time">2023-10-15</span>
                                        </div>
                                        <div class="text-muted small">
                                            由 <span class="log-user">管理员</span> 操作
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷操作按钮 -->
    <div class="quick-fab" data-target="quickActionsModal" style="background-color: var(--primary-color);">
        <i class="fas fa-bolt"></i>
    </div>
    
    <!-- 快速操作模态框 -->
    <div class="modal fade quick-modal" id="quickActionsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-bolt me-2"></i>快速操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group">
                        <button class="list-group-item list-group-item-action d-flex align-items-center" data-quick-action="modal" data-target="addRoleModal">
                            <i class="fas fa-user-plus me-3 text-primary"></i>
                            <div>
                                <h6 class="mb-0">创建新角色</h6>
                                <small class="text-muted">添加新的用户角色和权限</small>
                            </div>
                        </button>
                        <button class="list-group-item list-group-item-action d-flex align-items-center" data-quick-action="modal" data-target="bulkPermissionModal">
                            <i class="fas fa-key me-3 text-success"></i>
                            <div>
                                <h6 class="mb-0">批量修改权限</h6>
                                <small class="text-muted">快速为多个角色设置相同权限</small>
                            </div>
                        </button>
                        <button class="list-group-item list-group-item-action d-flex align-items-center" data-quick-action="modal" data-target="permissionTemplateModal">
                            <i class="fas fa-copy me-3 text-warning"></i>
                            <div>
                                <h6 class="mb-0">应用权限模板</h6>
                                <small class="text-muted">使用预设模板快速配置权限</small>
                            </div>
                        </button>
                        <button class="list-group-item list-group-item-action d-flex align-items-center" data-quick-action="modal" data-target="roleAssignModal">
                            <i class="fas fa-users me-3 text-info"></i>
                            <div>
                                <h6 class="mb-0">批量角色分配</h6>
                                <small class="text-muted">快速为多个用户分配角色</small>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加角色模态框 -->
    <div class="modal fade quick-modal" id="addRoleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>创建新角色</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-add-role-form" class="quick-form">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="roleName" class="form-label">角色名称</label>
                                <input type="text" class="form-control" id="roleName" name="displayName" placeholder="例如：资深专家" required>
                                <div class="invalid-feedback">请输入角色名称</div>
                            </div>
                            <div class="col-md-6">
                                <label for="roleSlug" class="form-label">角色标识</label>
                                <input type="text" class="form-control" id="roleSlug" name="name" placeholder="例如：senior-expert" required pattern="^[a-z0-9-]+$">
                                <div class="form-text">仅使用小写字母、数字和连字符</div>
                                <div class="invalid-feedback">请输入有效的角色标识（仅小写字母、数字和连字符）</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="roleDescription" class="form-label">角色描述</label>
                            <textarea class="form-control" id="roleDescription" name="description" rows="2" placeholder="简要描述此角色的职责和权限"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色颜色</label>
                            <div class="role-color-picker">
                                <div class="color-option selected" style="background-color: #007bff;" data-color="#007bff"></div>
                                <div class="color-option" style="background-color: #28a745;" data-color="#28a745"></div>
                                <div class="color-option" style="background-color: #dc3545;" data-color="#dc3545"></div>
                                <div class="color-option" style="background-color: #ffc107;" data-color="#ffc107"></div>
                                <div class="color-option" style="background-color: #17a2b8;" data-color="#17a2b8"></div>
                                <div class="color-option" style="background-color: #6610f2;" data-color="#6610f2"></div>
                                <div class="color-option" style="background-color: #fd7e14;" data-color="#fd7e14"></div>
                                <div class="color-option" style="background-color: #20c997;" data-color="#20c997"></div>
                            </div>
                            <input type="hidden" id="roleColor" name="color" value="#007bff">
                        </div>
                        <hr>
                        <h5 class="mb-3">初始权限设置</h5>
                        <div class="permission-group">
                            <h6><i class="fas fa-question-circle me-2"></i>问答中心</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="newRole_viewQuestions" name="permissions[]" value="questions.view" checked>
                                        <label class="form-check-label" for="newRole_viewQuestions">查看问题</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="newRole_askQuestions" name="permissions[]" value="questions.create" checked>
                                        <label class="form-check-label" for="newRole_askQuestions">提问</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="newRole_answerQuestions" name="permissions[]" value="answers.create">
                                        <label class="form-check-label" for="newRole_answerQuestions">回答问题</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="newRole_editAnswers" name="permissions[]" value="answers.edit">
                                        <label class="form-check-label" for="newRole_editAnswers">编辑回答</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-quick-action="submit" data-target="quick-add-role-form">创建角色</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑角色模态框 -->
    <div class="modal fade custom-modal" id="editRoleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-edit me-2"></i>编辑角色</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 模态框内容与添加角色类似，这里省略 -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>编辑角色的表单内容与创建角色类似，已预填充当前角色信息
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger me-auto" id="deleteRoleBtn">删除角色</button>
                    <button type="button" class="btn btn-primary" id="updateRoleBtn">更新角色</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量权限修改模态框 -->
    <div class="modal fade quick-modal" id="bulkPermissionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-key me-2"></i>批量修改权限</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-bulk-permission-form" class="quick-form">
                        <div class="mb-3">
                            <label class="form-label">选择角色</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="role_registered" name="roles[]" value="registered">
                                        <label class="form-check-label" for="role_registered">注册用户</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="role_premium" name="roles[]" value="premium">
                                        <label class="form-check-label" for="role_premium">高级会员</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="role_expert" name="roles[]" value="expert">
                                        <label class="form-check-label" for="role_expert">专家用户</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="role_moderator" name="roles[]" value="moderator">
                                        <label class="form-check-label" for="role_moderator">版主</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="role_admin" name="roles[]" value="admin">
                                        <label class="form-check-label" for="role_admin">管理员</label>
                                    </div>
                                </div>
                            </div>
                            <div class="invalid-feedback">请至少选择一个角色</div>
                        </div>
                        <hr>
                        <h5>权限设置</h5>
                        <p class="text-muted small">选择要应用的权限，未选择的权限将保持不变</p>
                        
                        <div class="permission-group">
                            <h6><i class="fas fa-question-circle me-2"></i>问答中心权限</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="bulk_viewQuestions" name="permissions[]" value="questions.view">
                                        <label class="form-check-label" for="bulk_viewQuestions">查看问题</label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="bulk_askQuestions" name="permissions[]" value="questions.create">
                                        <label class="form-check-label" for="bulk_askQuestions">提问</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="bulk_answerQuestions" name="permissions[]" value="answers.create">
                                        <label class="form-check-label" for="bulk_answerQuestions">回答问题</label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="bulk_editAnswers" name="permissions[]" value="answers.edit">
                                        <label class="form-check-label" for="bulk_editAnswers">编辑回答</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-quick-action="submit" data-target="quick-bulk-permission-form">应用权限</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 权限模板模态框 -->
    <div class="modal fade quick-modal" id="permissionTemplateModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-copy me-2"></i>应用权限模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-template-form" class="quick-form">
                        <div class="mb-3">
                            <label for="roleToApplyTemplate" class="form-label">选择要应用模板的角色</label>
                            <select class="form-select" id="roleToApplyTemplate" name="targetRole" required>
                                <option value="" selected disabled>选择角色...</option>
                                <option value="registered">注册用户</option>
                                <option value="premium">高级会员</option>
                                <option value="expert">专家用户</option>
                                <option value="moderator">版主</option>
                                <option value="custom">自定义角色...</option>
                            </select>
                            <div class="invalid-feedback">请选择一个角色</div>
                        </div>
                        <div class="mb-4">
                            <label for="permissionTemplate" class="form-label">选择权限模板</label>
                            <select class="form-select" id="permissionTemplate" name="template" required>
                                <option value="" selected disabled>选择模板...</option>
                                <option value="basic_user">基础用户权限</option>
                                <option value="premium_member">高级会员权限</option>
                                <option value="content_creator">内容创作者权限</option>
                                <option value="moderator">内容审核权限</option>
                                <option value="administrator">管理员权限</option>
                            </select>
                            <div class="invalid-feedback">请选择一个模板</div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>应用模板将覆盖角色当前的所有权限设置。
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-quick-action="submit" data-target="quick-template-form">应用模板</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量角色分配模态框 -->
    <div class="modal fade quick-modal" id="roleAssignModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-users me-2"></i>批量角色分配</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-role-assign-form" class="quick-form">
                        <div class="mb-3">
                            <label for="assignRole" class="form-label">要分配的角色</label>
                            <select class="form-select" id="assignRole" name="role" required>
                                <option value="" selected disabled>选择角色...</option>
                                <option value="registered">注册用户</option>
                                <option value="premium">高级会员</option>
                                <option value="expert">专家用户</option>
                                <option value="moderator">版主</option>
                            </select>
                            <div class="invalid-feedback">请选择一个角色</div>
                        </div>
                        <div class="mb-3">
                            <label for="userSelectionMethod" class="form-label">用户选择方式</label>
                            <select class="form-select" id="userSelectionMethod" name="selectionMethod">
                                <option value="individual">选择个别用户</option>
                                <option value="filter">按条件筛选</option>
                                <option value="csv">从CSV导入</option>
                            </select>
                        </div>
                        
                        <!-- 个别用户选择 -->
                        <div id="individualUserSelection" class="selection-panel">
                            <div class="mb-3">
                                <label class="form-label">选择用户</label>
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" placeholder="搜索用户..." id="userSearchInput">
                                    <button class="btn btn-outline-secondary" type="button">搜索</button>
                                </div>
                                <div class="border p-2 rounded mb-2" style="max-height: 200px; overflow-y: auto;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="user1" id="user1" name="users[]">
                                        <label class="form-check-label" for="user1">张三 (<EMAIL>)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="user2" id="user2" name="users[]">
                                        <label class="form-check-label" for="user2">李四 (<EMAIL>)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="user3" id="user3" name="users[]">
                                        <label class="form-check-label" for="user3">王五 (<EMAIL>)</label>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <button type="button" class="btn btn-sm btn-link" id="selectAllUsers">全选</button> /
                                    <button type="button" class="btn btn-sm btn-link" id="deselectAllUsers">取消全选</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-quick-action="submit" data-target="quick-role-assign-form">分配角色</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 快速交互脚本 -->
    <script src="../js/quick-interaction.js"></script>
    
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化颜色选择器
            document.querySelectorAll('.color-option').forEach(option => {
                option.addEventListener('click', function() {
                    // 移除之前的选择
                    document.querySelectorAll('.color-option').forEach(o => o.classList.remove('selected'));
                    // 添加新的选择
                    this.classList.add('selected');
                    // 更新隐藏字段
                    document.getElementById('roleColor').value = this.dataset.color;
                });
            });

            // 用户选择方法切换
            document.getElementById('userSelectionMethod').addEventListener('change', function() {
                const method = this.value;
                document.querySelectorAll('.selection-panel').forEach(panel => {
                    panel.style.display = 'none';
                });
                document.getElementById(method + 'UserSelection').style.display = 'block';
            });

            // 用户全选/取消全选
            document.getElementById('selectAllUsers').addEventListener('click', function() {
                document.querySelectorAll('input[name="users[]"]').forEach(checkbox => {
                    checkbox.checked = true;
                });
            });

            document.getElementById('deselectAllUsers').addEventListener('click', function() {
                document.querySelectorAll('input[name="users[]"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
            });

            // 表单提交处理
            document.querySelector('[data-target="quick-add-role-form"]').addEventListener('click', function() {
                const form = document.getElementById('quick-add-role-form');
                if (!form.checkValidity()) {
                    form.classList.add('was-validated');
                    return;
                }

                // 收集表单数据
                const formData = new FormData(form);
                const roleData = {
                    name: formData.get('name'),
                    displayName: formData.get('displayName'),
                    description: formData.get('description'),
                    color: formData.get('color'),
                    permissions: formData.getAll('permissions[]')
                };

                // 调用API创建角色
                auth.fetchAPI('/roles', {
                    method: 'POST',
                    body: JSON.stringify(roleData)
                })
                .then(response => {
                    quickInteraction.hideModal('addRoleModal');
                    quickInteraction.showNotification('角色创建成功', 'success');
                    // 重新加载页面或更新UI
                    setTimeout(() => window.location.reload(), 1000);
                })
                .catch(error => {
                    quickInteraction.showNotification(error.message || '创建角色失败', 'error');
                });
            });

            // 初始化权限拖放功能
            const availablePermissions = document.getElementById('availablePermissions');
            const assignedPermissions = document.getElementById('assignedPermissions');
            
            if (availablePermissions && assignedPermissions) {
                // 创建可用权限排序实例
                const availableSortable = new Sortable(availablePermissions, {
                    group: {
                        name: 'permissions',
                        pull: 'clone',
                        put: true
                    },
                    animation: 150,
                    sort: false
                });
                
                // 创建已分配权限排序实例
                const assignedSortable = new Sortable(assignedPermissions, {
                    group: 'permissions',
                    animation: 150,
                    onAdd: function(evt) {
                        // 为新添加的权限添加已分配样式
                        evt.item.classList.add('assigned');
                        
                        // 移除占位符
                        const placeholder = assignedPermissions.querySelector('.permission-target-placeholder');
                        if (placeholder) {
                            placeholder.remove();
                        }
                    },
                    onRemove: function(evt) {
                        // 移除已分配样式
                        evt.item.classList.remove('assigned');
                        
                        // 如果没有已分配权限了，添加占位符
                        if (assignedPermissions.children.length === 0) {
                            const placeholder = document.createElement('div');
                            placeholder.className = 'permission-target-placeholder';
                            placeholder.textContent = '拖动左侧权限到此处进行分配';
                            assignedPermissions.appendChild(placeholder);
                        }
                    }
                });
            }
            
            // 角色选择处理
            const roleSelect = document.getElementById('roleSelect');
            if (roleSelect) {
                roleSelect.addEventListener('change', function() {
                    const role = this.value;
                    if (role && role !== '选择角色...') {
                        loadRolePermissions(role);
                    } else {
                        // 清空已分配权限
                        assignedPermissions.innerHTML = '<div class="permission-target-placeholder">选择一个角色来管理其权限</div>';
                    }
                });
            }
            
            // 加载角色权限
            function loadRolePermissions(role) {
                // 清空已分配权限
                assignedPermissions.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>加载中...</div>';
                
                // 这里应该是通过API获取角色权限
                // 但这里使用模拟数据进行演示
                setTimeout(() => {
                    // 模拟不同角色的权限
                    const rolePermissions = {
                        visitor: ['questions.view'],
                        user: ['questions.view', 'questions.create', 'comments.create'],
                        member: ['questions.view', 'questions.create', 'answers.create', 'comments.create', 'resources.download'],
                        expert: ['questions.view', 'questions.create', 'answers.create', 'answers.edit', 'comments.create', 'resources.download', 'resources.upload'],
                        moderator: ['questions.view', 'questions.create', 'answers.create', 'answers.edit', 'comments.create', 'resources.download', 'resources.upload'],
                        admin: ['questions.view', 'questions.create', 'answers.create', 'answers.edit', 'comments.create', 'resources.download', 'resources.upload']
                    };
                    
                    // 清空容器
                    assignedPermissions.innerHTML = '';
                    
                    // 获取该角色的权限
                    const permissions = rolePermissions[role] || [];
                    
                    if (permissions.length === 0) {
                        // 如果没有权限，显示提示
                        const placeholder = document.createElement('div');
                        placeholder.className = 'permission-target-placeholder';
                        placeholder.textContent = '该角色暂无分配权限，拖动左侧权限到此处进行分配';
                        assignedPermissions.appendChild(placeholder);
                    } else {
                        // 添加权限项
                        permissions.forEach(permission => {
                            // 在可用权限中查找对应的权限项
                            const originalItem = availablePermissions.querySelector(`[data-permission="${permission}"]`);
                            if (originalItem) {
                                // 克隆权限项
                                const clonedItem = originalItem.cloneNode(true);
                                clonedItem.classList.add('assigned');
                                assignedPermissions.appendChild(clonedItem);
                            }
                        });
                    }
                }, 500);
            }
            
            // 保存权限
            const savePermissionsBtn = document.getElementById('savePermissions');
            if (savePermissionsBtn) {
                savePermissionsBtn.addEventListener('click', function() {
                    const role = roleSelect.value;
                    if (!role || role === '选择角色...') {
                        window.quickInteraction.showNotification('请先选择一个角色', 'warning');
                        return;
                    }
                    
                    // 收集已分配的权限
                    const permissions = [];
                    assignedPermissions.querySelectorAll('.permission-item').forEach(item => {
                        permissions.push(item.dataset.permission);
                    });
                    
                    // 显示保存中状态
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> 保存中...';
                    this.disabled = true;
                    
                    // 这里应该是通过API保存权限
                    // 但这里只进行演示
                    setTimeout(() => {
                        // 恢复按钮状态
                        this.innerHTML = '<i class="fas fa-save me-1"></i> 保存更改';
                        this.disabled = false;
                        
                        // 显示成功通知
                        window.quickInteraction.showNotification('权限已成功保存', 'success');
                        
                        // 添加到变更历史
                        const changeLog = document.querySelector('.list-group');
                        if (changeLog) {
                            const now = new Date();
                            const timeString = now.getHours() + ':' + now.getMinutes().toString().padStart(2, '0');
                            
                            const logItem = document.createElement('div');
                            logItem.className = 'change-log-item';
                            logItem.innerHTML = `
                                <div class="d-flex justify-content-between">
                                    <span class="log-action">更新 "${role}" 角色权限</span>
                                    <span class="log-time">今天 ${timeString}</span>
                                </div>
                                <div class="text-muted small">
                                    由 <span class="log-user">管理员</span> 操作
                                </div>
                            `;
                            
                            // 添加到顶部
                            changeLog.insertBefore(logItem, changeLog.firstChild);
                        }
                    }, 800);
                });
            }
            
            // 重置权限
            const resetPermissionsBtn = document.getElementById('resetPermissions');
            if (resetPermissionsBtn) {
                resetPermissionsBtn.addEventListener('click', function() {
                    const role = roleSelect.value;
                    if (!role || role === '选择角色...') {
                        return;
                    }
                    
                    // 重新加载角色权限
                    loadRolePermissions(role);
                });
            }

            // 批量权限处理
            document.querySelector('[data-target="quick-bulk-permission-form"]').addEventListener('click', function() {
                const form = document.getElementById('quick-bulk-permission-form');
                const roles = form.querySelectorAll('input[name="roles[]"]:checked');
                
                if (roles.length === 0) {
                    form.classList.add('was-validated');
                    return;
                }

                const roleIds = Array.from(roles).map(input => input.value);
                const permissions = Array.from(form.querySelectorAll('input[name="permissions[]"]:checked')).map(input => input.value);

                // 调用API更新权限
                auth.fetchAPI('/roles/batch-permissions', {
                    method: 'PUT',
                    body: JSON.stringify({
                        roleIds,
                        permissions
                    })
                })
                .then(response => {
                    quickInteraction.hideModal('bulkPermissionModal');
                    quickInteraction.showNotification('权限已成功更新', 'success');
                    // 重新加载页面或更新UI
                    setTimeout(() => window.location.reload(), 1000);
                })
                .catch(error => {
                    quickInteraction.showNotification(error.message || '更新权限失败', 'error');
                });
            });

            // 权限模板应用处理
            document.querySelector('[data-target="quick-template-form"]').addEventListener('click', function() {
                const form = document.getElementById('quick-template-form');
                if (!form.checkValidity()) {
                    form.classList.add('was-validated');
                    return;
                }

                const formData = new FormData(form);
                const data = {
                    targetRole: formData.get('targetRole'),
                    template: formData.get('template')
                };

                // 调用API应用模板
                auth.fetchAPI('/roles/apply-template', {
                    method: 'PUT',
                    body: JSON.stringify(data)
                })
                .then(response => {
                    quickInteraction.hideModal('permissionTemplateModal');
                    quickInteraction.showNotification('权限模板已成功应用', 'success');
                    // 重新加载页面或更新UI
                    setTimeout(() => window.location.reload(), 1000);
                })
                .catch(error => {
                    quickInteraction.showNotification(error.message || '应用模板失败', 'error');
                });
            });

            // 批量角色分配处理
            document.querySelector('[data-target="quick-role-assign-form"]').addEventListener('click', function() {
                const form = document.getElementById('quick-role-assign-form');
                if (!form.checkValidity()) {
                    form.classList.add('was-validated');
                    return;
                }

                const formData = new FormData(form);
                const data = {
                    role: formData.get('role'),
                    selectionMethod: formData.get('selectionMethod'),
                    users: formData.getAll('users[]')
                };

                // 调用API分配角色
                auth.fetchAPI('/users/assign-role', {
                    method: 'PUT',
                    body: JSON.stringify(data)
                })
                .then(response => {
                    quickInteraction.hideModal('roleAssignModal');
                    quickInteraction.showNotification('角色已成功分配给选定用户', 'success');
                })
                .catch(error => {
                    quickInteraction.showNotification(error.message || '角色分配失败', 'error');
                });
            });
        });
    </script>
</body>
</html> 