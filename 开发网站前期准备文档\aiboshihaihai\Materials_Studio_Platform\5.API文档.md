# Materials Studio答疑平台API文档

## API概述

- 基础URL: `https://api.materialsstudio-qa.com/v1`
- 数据格式: JSON
- 认证方式: JWT (Bearer Token)
- 请求编码: UTF-8

## 通用规范

### 请求头
```
Content-Type: application/json
Authorization: Bearer {token}
```

### 响应格式
```json
{
  "code": 200,                // 状态码: 200成功, 4xx客户端错误, 5xx服务端错误
  "message": "成功",          // 响应消息
  "data": {},                // 响应数据
  "timestamp": 1672531200000  // 时间戳
}
```

### 分页参数
```
page: 当前页码(默认1)
limit: 每页条数(默认10, 最大50)
sort: 排序字段
order: 排序方式(asc/desc)
```

## 用户管理API

### 1. 用户注册

- **接口**: `POST /users/register`
- **描述**: 创建新用户账号
- **请求体**:
  ```json
  {
    "username": "user123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "password": "SecurePassword123",
    "confirmPassword": "SecurePassword123"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "userId": 1001,
      "username": "user123",
      "email": "<EMAIL>",
      "phone": "138****8000"
    },
    "timestamp": 1672531200000
  }
  ```

### 2. 发送验证码

- **接口**: `POST /users/verification-code`
- **描述**: 发送手机或邮箱验证码
- **请求体**:
  ```json
  {
    "target": "13800138000",  // 或邮箱
    "type": 1  // 1-注册, 2-登录, 3-找回密码
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "验证码已发送",
    "data": {
      "expireTime": 300  // 有效期(秒)
    },
    "timestamp": 1672531200000
  }
  ```

### 3. 用户登录

- **接口**: `POST /users/login`
- **描述**: 用户登录获取token
- **请求体**:
  ```json
  {
    "loginType": "password",  // password-密码登录, code-验证码登录
    "username": "user123",    // 用户名/手机/邮箱
    "password": "SecurePassword123"  // 密码登录时必填
    // 或
    "target": "13800138000",  // 验证码登录时必填
    "code": "123456"          // 验证码
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "userId": 1001,
      "username": "user123",
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 86400,
      "userInfo": {
        "avatar": "https://example.com/avatar.jpg",
        "memberLevel": 1,
        "memberExpireDate": "2023-12-31"
      }
    },
    "timestamp": 1672531200000
  }
  ```

### 4. 获取用户信息

- **接口**: `GET /users/profile`
- **描述**: 获取当前登录用户信息
- **认证**: 需要token
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "userId": 1001,
      "username": "user123",
      "email": "<EMAIL>",
      "phone": "138****8000",
      "avatar": "https://example.com/avatar.jpg",
      "memberLevel": 1,
      "memberExpireDate": "2023-12-31",
      "profile": {
        "realName": "张三",
        "gender": 1,
        "education": "博士",
        "institution": "XX大学",
        "researchField": "材料科学"
      },
      "stats": {
        "questionCount": 5,
        "commentCount": 10
      }
    },
    "timestamp": 1672531200000
  }
  ```

### 5. 更新用户信息

- **接口**: `PUT /users/profile`
- **描述**: 更新用户个人资料
- **认证**: 需要token
- **请求体**:
  ```json
  {
    "avatar": "https://example.com/new-avatar.jpg",
    "profile": {
      "realName": "李四",
      "gender": 1,
      "birthday": "1990-01-01",
      "education": "博士",
      "institution": "XX研究所",
      "researchField": "计算材料学",
      "bio": "专注于材料模拟计算"
    }
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "更新成功",
    "data": {
      "userId": 1001,
      "profile": {
        "realName": "李四",
        "updatedAt": "2023-01-01T12:00:00Z"
      }
    },
    "timestamp": 1672531200000
  }
  ```

## 问答系统API

### 1. 获取问题列表

- **接口**: `GET /questions`
- **描述**: 获取问题列表，支持分页和筛选
- **参数**:
  ```
  page: 1
  limit: 10
  category: 1         // 分类ID
  tag: "Materials"    // 标签
  keyword: "模拟"      // 搜索关键词
  sort: "created_at"  // 排序字段
  order: "desc"       // 排序方式
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "questions": [
        {
          "id": 101,
          "title": "Materials Studio中如何设置模拟参数?",
          "brief": "我在使用Materials Studio进行分子动力学模拟时...",
          "userId": 1001,
          "username": "user123",
          "userAvatar": "https://example.com/avatar.jpg",
          "categoryId": 1,
          "categoryName": "分子动力学",
          "tags": ["模拟", "参数设置"],
          "viewCount": 120,
          "answerCount": 3,
          "hasAcceptedAnswer": true,
          "createdAt": "2023-01-01T10:00:00Z",
          "updatedAt": "2023-01-02T14:30:00Z"
        },
        // 更多问题...
      ]
    },
    "timestamp": 1672531200000
  }
  ```

### 2. 获取问题详情

- **接口**: `GET /questions/{questionId}`
- **描述**: 获取单个问题的详细信息
- **参数**: 
  - `questionId`: 问题ID
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "id": 101,
      "title": "Materials Studio中如何设置模拟参数?",
      "content": "我在使用Materials Studio进行分子动力学模拟时遇到了一个问题，如何设置力场参数？我尝试了COMPASS力场，但不确定以下参数如何配置...",
      "contentHtml": "<p>我在使用Materials Studio进行分子动力学模拟时遇到了一个问题，如何设置力场参数？我尝试了COMPASS力场，但不确定以下参数如何配置...</p>",
      "userId": 1001,
      "userInfo": {
        "username": "user123",
        "avatar": "https://example.com/avatar.jpg",
        "memberLevel": 1
      },
      "categoryId": 1,
      "categoryName": "分子动力学",
      "tags": [
        {"id": 5, "name": "模拟"},
        {"id": 8, "name": "参数设置"}
      ],
      "attachments": [
        {
          "id": 201,
          "fileName": "model.xsd",
          "fileSize": 256,
          "fileType": "application/octet-stream",
          "downloadUrl": "https://api.materialsstudio-qa.com/v1/files/201"
        }
      ],
      "viewCount": 120,
      "answerCount": 3,
      "favoriteCount": 10,
      "isFavorite": false,
      "status": 1,
      "createdAt": "2023-01-01T10:00:00Z",
      "updatedAt": "2023-01-02T14:30:00Z"
    },
    "timestamp": 1672531200000
  }
  ```

### 3. 创建问题

- **接口**: `POST /questions`
- **描述**: 创建新问题
- **认证**: 需要token
- **请求体**:
  ```json
  {
    "title": "Materials Studio中力场参数如何设置？",
    "content": "我在模拟高分子材料时，想使用COMPASS力场，但是不确定...",
    "categoryId": 1,
    "tags": ["模拟", "参数设置"]
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "创建成功",
    "data": {
      "questionId": 102,
      "title": "Materials Studio中力场参数如何设置？",
      "url": "/questions/102"
    },
    "timestamp": 1672531200000
  }
  ```

### 4. 上传问题附件

- **接口**: `POST /questions/{questionId}/attachments`
- **描述**: 上传问题附件文件
- **认证**: 需要token
- **请求**: 
  - 表单数据，字段名为`file`
  - 支持格式: .xsd, .mol, .pdb, .xyz, .txt, .pdf, .png, .jpg
  - 文件大小限制: 10MB
- **响应**:
  ```json
  {
    "code": 200,
    "message": "上传成功",
    "data": {
      "attachmentId": 201,
      "fileName": "model.xsd",
      "fileSize": 256,
      "fileType": "application/octet-stream",
      "downloadUrl": "https://api.materialsstudio-qa.com/v1/files/201"
    },
    "timestamp": 1672531200000
  }
  ```

### 5. 获取问题回答列表

- **接口**: `GET /questions/{questionId}/answers`
- **描述**: 获取问题的回答列表
- **参数**: 
  - `questionId`: 问题ID
  - `page`: 页码
  - `limit`: 每页条数
  - `sort`: 排序（accepted-采纳优先, upvotes-点赞数, newest-最新）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "total": 3,
      "page": 1,
      "limit": 10,
      "answers": [
        {
          "id": 301,
          "questionId": 101,
          "userId": 1002,
          "userInfo": {
            "username": "expert1",
            "avatar": "https://example.com/avatar2.jpg",
            "memberLevel": 2,
            "isExpert": true,
            "expertTitle": "材料模拟专家"
          },
          "basicContent": "力场参数设置需要考虑以下几点：\n1. 首先确认分子结构的正确性...",
          "basicContentHtml": "<p>力场参数设置需要考虑以下几点：</p><ol><li>首先确认分子结构的正确性...</li></ol>",
          "hasPremiumContent": true,
          "visibilityLevel": 1,
          "isAccepted": true,
          "upvoteCount": 15,
          "downvoteCount": 1,
          "videos": [
            {
              "id": 401,
              "title": "Materials Studio力场参数设置详解",
              "coverUrl": "https://example.com/video-cover.jpg",
              "duration": 360,
              "visibilityLevel": 1
            }
          ],
          "createdAt": "2023-01-01T12:30:00Z",
          "updatedAt": "2023-01-01T14:00:00Z"
        }
        // 更多回答...
      ]
    },
    "timestamp": 1672531200000
  }
  ```

### 6. 获取回答详情

- **接口**: `GET /answers/{answerId}`
- **描述**: 获取回答详情，包括会员专享内容（如有权限）
- **认证**: 可选（有token时检查会员权限）
- **参数**: 
  - `answerId`: 回答ID
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "id": 301,
      "questionId": 101,
      "questionTitle": "Materials Studio中如何设置模拟参数?",
      "userId": 1002,
      "userInfo": {
        "username": "expert1",
        "avatar": "https://example.com/avatar2.jpg",
        "memberLevel": 2,
        "isExpert": true,
        "expertTitle": "材料模拟专家"
      },
      "basicContent": "力场参数设置需要考虑以下几点：\n1. 首先确认分子结构的正确性...",
      "basicContentHtml": "<p>力场参数设置需要考虑以下几点：</p><ol><li>首先确认分子结构的正确性...</li></ol>",
      "premiumContent": "高级设置技巧：\n1. 对于非标准原子类型，可以通过以下步骤自定义参数...",
      "premiumContentHtml": "<p>高级设置技巧：</p><ol><li>对于非标准原子类型，可以通过以下步骤自定义参数...</li></ol>",
      "visibilityLevel": 1,
      "isAccepted": true,
      "upvoteCount": 15,
      "downvoteCount": 1,
      "userVote": 1, // 1-已点赞，-1-已点踩，0-未操作
      "videos": [
        {
          "id": 401,
          "title": "Materials Studio力场参数设置详解",
          "coverUrl": "https://example.com/video-cover.jpg",
          "videoUrl": "https://api.materialsstudio-qa.com/v1/videos/401",
          "duration": 360,
          "visibilityLevel": 1
        }
      ],
      "createdAt": "2023-01-01T12:30:00Z",
      "updatedAt": "2023-01-01T14:00:00Z"
    },
    "timestamp": 1672531200000
  }
  ```

### 7. 创建回答

- **接口**: `POST /questions/{questionId}/answers`
- **描述**: 为问题创建回答
- **认证**: 需要token
- **请求体**:
  ```json
  {
    "basicContent": "对于COMPASS力场参数设置，基础步骤如下：...",
    "premiumContent": "高级技巧：对于复杂体系，推荐以下优化方法...",
    "visibilityLevel": 1 // 0-所有人可见，1-初级会员可见，2-高级会员可见
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "回答已发布",
    "data": {
      "answerId": 302,
      "questionId": 101
    },
    "timestamp": 1672531200000
  }
  ```

## 会员管理API

### 1. 获取会员套餐列表

- **接口**: `GET /memberships/packages`
- **描述**: 获取可购买的会员套餐列表
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "packages": [
        {
          "id": 1,
          "name": "月度初级会员",
          "level": 1,
          "price": 29.90,
          "originalPrice": 39.90,
          "duration": 1,
          "durationUnit": "month",
          "features": ["基础问答内容", "视频教程访问", "资源下载(10次/月)"],
          "isRecommended": false
        },
        {
          "id": 2,
          "name": "年度初级会员",
          "level": 1,
          "price": 298.00,
          "originalPrice": 358.00,
          "duration": 12,
          "durationUnit": "month",
          "features": ["基础问答内容", "视频教程访问", "资源下载(不限)"],
          "isRecommended": true
        },
        {
          "id": 3,
          "name": "高级会员",
          "level": 2,
          "price": 498.00,
          "originalPrice": 598.00,
          "duration": 12,
          "durationUnit": "month",
          "features": ["全部问答内容", "所有视频教程", "一对一专家咨询(3次)", "优先回答", "资源下载(不限)"],
          "isRecommended": true
        }
      ]
    },
    "timestamp": 1672531200000
  }
  ```

### 2. 创建会员订单

- **接口**: `POST /memberships/orders`
- **描述**: 创建会员购买订单
- **认证**: 需要token
- **请求体**:
  ```json
  {
    "packageId": 2,
    "paymentMethod": "alipay" // alipay-支付宝, wechat-微信支付, card-银行卡
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "订单创建成功",
    "data": {
      "orderId": 1001,
      "orderNo": "MSQ20230101001",
      "amount": 298.00,
      "paymentMethod": "alipay",
      "paymentUrl": "https://api.materialsstudio-qa.com/v1/pay/alipay/1001",
      "qrCode": "data:image/png;base64,iVBOR...",
      "expireTime": "2023-01-01T13:00:00Z"
    },
    "timestamp": 1672531200000
  }
  ```

### 3. 查询订单状态

- **接口**: `GET /memberships/orders/{orderNo}`
- **描述**: 查询订单支付状态
- **认证**: 需要token
- **参数**:
  - `orderNo`: 订单号
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "orderId": 1001,
      "orderNo": "MSQ20230101001",
      "status": 1, // 0-待支付,1-已支付,2-已取消,3-已退款
      "amount": 298.00,
      "packageInfo": {
        "name": "年度初级会员",
        "level": 1,
        "duration": 12,
        "durationUnit": "month"
      },
      "paymentMethod": "alipay",
      "paymentTime": "2023-01-01T12:30:00Z",
      "membershipStartDate": "2023-01-01",
      "membershipEndDate": "2024-01-01"
    },
    "timestamp": 1672531200000
  }
  ```

### 4. 获取用户会员信息

- **接口**: `GET /memberships/user`
- **描述**: 获取当前用户的会员信息
- **认证**: 需要token
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "isMember": true,
      "level": 1,
      "startDate": "2023-01-01",
      "endDate": "2024-01-01",
      "daysRemaining": 365,
      "autoRenew": true,
      "benefits": ["基础问答内容", "视频教程访问", "资源下载(不限)"],
      "usageStats": {
        "viewedPremiumAnswers": 15,
        "downloadedResources": 5
      },
      "upgradeOptions": [
        {
          "packageId": 3,
          "name": "高级会员",
          "price": 200.00, // 差价
          "originalPrice": 498.00
        }
      ]
    },
    "timestamp": 1672531200000
  }
  ```

### 5. 设置自动续费

- **接口**: `PUT /memberships/auto-renew`
- **描述**: 设置会员自动续费状态
- **认证**: 需要token
- **请求体**:
  ```json
  {
    "autoRenew": true
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "设置成功",
    "data": {
      "autoRenew": true
    },
    "timestamp": 1672531200000
  }
  ```

## 资源管理API

### 1. 获取资源列表

- **接口**: `GET /resources`
- **描述**: 获取资源库资源列表
- **认证**: 可选（有token时返回对应权限资源）
- **参数**:
  ```
  page: 1
  limit: 10
  category: 1         // 分类ID
  type: 2             // 资源类型(1-文档,2-视频,3-模板,4-软件)
  keyword: "模拟"      // 搜索关键词
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "total": 50,
      "page": 1,
      "limit": 10,
      "resources": [
        {
          "id": 501,
          "title": "Materials Studio分子动力学模板",
          "description": "适用于高分子材料模拟的Materials Studio模板文件",
          "type": 3,
          "coverImage": "https://example.com/resource-cover.jpg",
          "categoryId": 1,
          "categoryName": "分子动力学",
          "fileSize": 1024,
          "downloadCount": 120,
          "visibilityLevel": 1,
          "createdAt": "2023-01-05T10:00:00Z",
          "canAccess": true // 当前用户是否有权限访问
        },
        // 更多资源...
      ]
    },
    "timestamp": 1672531200000
  }
  ```

### 2. 获取资源详情

- **接口**: `GET /resources/{resourceId}`
- **描述**: 获取资源详情
- **认证**: 可选（有token时检查权限）
- **参数**:
  - `resourceId`: 资源ID
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "id": 501,
      "title": "Materials Studio分子动力学模板",
      "description": "适用于高分子材料模拟的Materials Studio模板文件",
      "content": "本模板包含预设的高分子链结构和模拟参数，适用于...",
      "type": 3,
      "coverImage": "https://example.com/resource-cover.jpg",
      "url": "https://api.materialsstudio-qa.com/v1/resources/download/501",
      "categoryId": 1,
      "categoryName": "分子动力学",
      "tags": ["模板", "高分子", "动力学"],
      "fileSize": 1024,
      "fileType": "application/zip",
      "downloadCount": 120,
      "viewCount": 350,
      "likeCount": 45,
      "visibilityLevel": 1,
      "canAccess": true,
      "relatedResources": [
        {
          "id": 502,
          "title": "高分子模拟参数设置指南",
          "type": 1,
          "visibilityLevel": 1
        }
      ],
      "createdAt": "2023-01-05T10:00:00Z",
      "updatedAt": "2023-01-05T10:00:00Z"
    },
    "timestamp": 1672531200000
  }
  ```

### 3. 获取资源下载地址

- **接口**: `GET /resources/{resourceId}/download`
- **描述**: 获取受保护资源的下载链接
- **认证**: 需要token (会员权限)
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "downloadUrl": "https://cdn.example.com/protected/resource.zip?token=xxx"
    },
    "timestamp": 1672531200000
  }
  ```

### 4. 资源点赞

- **接口**: `POST /resources/{resourceId}/like`
- **描述**: 对资源进行点赞
- **认证**: 需要token
- **参数**:
  - `resourceId`: 资源ID
- **响应**:
  ```json
  {
    "code": 200,
    "message": "点赞成功",
    "data": {
      "resourceId": 501,
      "likeCount": 46
    },
    "timestamp": 1672531200000
  }
  ```

## 统计与通知API

### 1. 获取用户消息通知

- **接口**: `GET /notifications`
- **描述**: 获取用户的消息通知
- **认证**: 需要token
- **参数**:
  ```
  page: 1
  limit: 10
  isRead: false // 可选，筛选已读/未读
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "成功",
    "data": {
      "total": 5,
      "unreadCount": 3,
      "page": 1,
      "limit": 10,
      "notifications": [
        {
          "id": 1001,
          "type": "answer",
          "title": "您的问题有新回答",
          "content": "您的问题"Materials Studio中如何设置模拟参数?"收到了新回答",
          "relatedId": 301,
          "link": "/questions/101#answer-301",
          "isRead": false,
          "createdAt": "2023-01-01T12:30:00Z"
        },
        {
          "id": 1002,
          "type": "comment",
          "title": "您的回答有新评论",
          "content": "用户user123评论了您的回答",
          "relatedId": 401,
          "link": "/questions/102#comment-401",
          "isRead": false,
          "createdAt": "2023-01-01T14:20:00Z"
        }
        // 更多通知...
      ]
    },
    "timestamp": 1672531200000
  }
  ```

### 2. 标记通知已读

- **接口**: `PUT /notifications/read`
- **描述**: 标记通知已读
- **认证**: 需要token
- **请求体**:
  ```json
  {
    "notificationIds": [1001, 1002],
    "all": false // 为true时忽略notificationIds，标记所有通知为已读
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "标记成功",
    "data": {
      "markedCount": 2,
      "unreadCount": 1
    },
    "timestamp": 1672531200000
  }
  ```

## API认证机制详解

### JWT认证流程

1. **登录获取Token**
   
   客户端通过用户登录接口获取JWT token和refresh token。

2. **使用Token访问接口**
   
   在请求头中添加`Authorization: Bearer {token}`来访问需要认证的API。

3. **Token过期处理**
   
   当访问令牌(access token)过期时，使用刷新令牌(refresh token)获取新的访问令牌：

   ```
   POST /users/refresh-token
   
   Request Body:
   {
     "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   }
   
   Response:
   {
     "code": 200,
     "message": "刷新成功",
     "data": {
       "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
       "expiresIn": 86400
     },
     "timestamp": 1672531200000
   }
   ```

### Token结构

1. **访问令牌(access token)**
   
   有效期：2小时
   
   载荷(Payload)结构：
   ```json
   {
     "userId": 1001,
     "username": "user123",
     "memberLevel": 1,
     "exp": 1672617600, // 过期时间戳
     "iat": 1672531200  // 颁发时间戳
   }
   ```

2. **刷新令牌(refresh token)**
   
   有效期：7天
   
   载荷(Payload)结构：
   ```json
   {
     "userId": 1001,
     "tokenId": "6fd9cbf9-7d23-4d8a-9f28-e722e89de60e", // 唯一标识，用于撤销
     "exp": 1673136000, // 过期时间戳
     "iat": 1672531200  // 颁发时间戳
   }
   ```

## 错误码规范

### 通用错误码

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求参数错误 | 请求参数格式不正确或缺少必要参数 |
| 401 | 未授权 | 未登录或token已过期 |
| 403 | 权限不足 | 无权限执行该操作，如非会员访问会员内容 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 429 | 请求过于频繁 | 超出API调用频率限制 |
| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |

### 业务错误码

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 1001 | 用户名已存在 | 注册时用户名重复 |
| 1002 | 邮箱已被注册 | 注册时邮箱重复 |
| 1003 | 手机号已被注册 | 注册时手机号重复 |
| 1004 | 验证码错误 | 提供的验证码不正确或已过期 |
| 1005 | 账户已被锁定 | 多次登录失败导致账户锁定 |
| 1006 | 密码强度不足 | 密码不符合强度要求 |
| 2001 | 问题不存在 | 请求的问题ID不存在 |
| 2002 | 回答不存在 | 请求的回答ID不存在 |
| 2003 | 无权限操作该问题 | 尝试修改他人的问题 |
| 2004 | 无权限操作该回答 | 尝试修改他人的回答 |
| 2005 | 内容含有敏感词 | 提交的内容包含敏感词 |
| 3001 | 会员套餐不存在 | 请求的会员套餐ID不存在 |
| 3002 | 订单创建失败 | 创建会员订单时发生错误 |
| 3003 | 支付失败 | 支付过程中发生错误 |
| 3004 | 资源不存在 | 请求的资源ID不存在 |
| 3005 | 无权限访问该资源 | 当前权限不足以访问请求的资源 |
| 3006 | 下载次数超限 | 当前会员等级的资源下载次数已用尽 |

## API版本控制策略

### 版本标识方式

Materials Studio答疑平台API采用URL路径中的版本号来标识API版本：

```
https://api.materialsstudio-qa.com/v1/users/login
```

### 版本更新策略

1. **向后兼容更新**：
   - 添加新的可选参数
   - 添加新的响应字段
   - 添加新的API端点
   
   这类更新不改变现有API行为，不需要发布新版本。

2. **不兼容更新**：
   - 移除或重命名参数
   - 更改参数或响应的数据类型
   - 更改API的行为逻辑

   不兼容更新需要在新版本中发布，如从`/v1`升级到`/v2`。

### 版本生命周期

1. **当前活跃版本**: 当前支持的主要版本，接收功能更新和bug修复。
2. **维护版本**: 只接收bug修复和安全更新，不添加新功能。
3. **废弃版本**: 将在指定日期后不再支持，建议用户迁移到新版本。
4. **退役版本**: 已关闭的版本，API请求将返回410 Gone响应。

每个API版本的支持期至少为12个月，在废弃前会提前3个月通知用户。

## API限流策略

为保障服务稳定性和公平使用，Materials Studio答疑平台API实施以下限流策略：

1. **未认证请求**：每IP每分钟60次请求
2. **普通用户请求**：每用户每分钟120次请求
3. **会员用户请求**：每用户每分钟240次请求

超出限制时，API将返回429状态码及以下信息：

```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后再试",
  "data": {
    "retryAfter": 30, // 建议多少秒后重试
    "limit": 120,     // 当前限制
    "remaining": 0,   // 剩余请求次数
    "resetAt": 1672531260000 // 限制重置时间戳
  },
  "timestamp": 1672531200000
}
```

涉及敏感操作的API（如登录、注册、支付）有更严格的限流策略，以防止恶意攻击。

## 支付与交易API

### 1. 创建订单

- **接口**: `POST /orders`
- **描述**: 为购买会员套餐创建订单
- **认证**: 需要token
- **请求体**:
  ```json
  {
    "packageId": 1, // 会员套餐ID
    "paymentMethod": "alipay" // alipay 或 wechat_pay
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "订单创建成功",
    "data": {
      "orderNo": "202305201002003004",
      "payData": {
        // 如果是支付宝PC支付，这里是支付页面URL
        "paymentUrl": "https://openapi.alipay.com/gateway.do?..." 
        // 如果是微信扫码支付，这里是二维码链接
        // "qrCodeUrl": "weixin://wxpay/bizpayurl?pr=..."
      }
    },
    "timestamp": 1672531200000
  }
  ```

### 2. 支付结果异步通知

- **接口**: `POST /payments/notify/{paymentMethod}`
- **描述**: 接收支付网关的异步通知。此接口由支付网关调用，无须前端处理。
- **认证**: 无 (通过验签保证安全)
- **参数**:
  - `paymentMethod`: `alipay` 或 `wechat_pay`
- **请求体**:
  - (来自支付网关，格式由对方定义)
- **响应**:
  - (响应给支付网关，通常是 `success` 或 `fail` 字符串)

## 后台管理API (Admin)

### 1. 管理员登录

- **接口**: `POST /admin/login`
- **描述**: 管理员登录获取token
- **请求体**:
  ```json
  {
    "username": "admin",
    "password": "AdminPassword123"
  }
  ```
- **响应**: (同用户登录响应，但包含管理员特定权限信息)

### 2. 用户列表查询

- **接口**: `GET /admin/users`
- **描述**: 获取用户列表，支持筛选和分页
- **认证**: 需要管理员token
- **参数**: (同 `/questions` 的分页和筛选参数)
- **响应**: (返回用户列表及分页信息)

### 3. 更新用户状态

- **接口**: `PUT /admin/users/{userId}/status`
- **描述**: 禁用或启用某个用户
- **认证**: 需要管理员token
- **请求体**:
  ```json
  {
    "status": 0 // 1-正常, 0-禁用
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "用户状态更新成功",
    "data": null,
    "timestamp": 1672531200000
  }
  ```

### 4. 问题审核

- **接口**: `PUT /admin/questions/{questionId}/status`
- **描述**: 审核问题（通过、关闭等）
- **认证**: 需要管理员token
- **请求体**:
  ```json
  {
    "status": 1 // 0-待审核, 1-已发布, 2-已关闭
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "问题状态更新成功",
    "data": null,
    "timestamp": 1672531200000
  }
  ```

## 杂项API