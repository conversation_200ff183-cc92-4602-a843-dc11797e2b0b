<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Materials Studio 答疑平台 - 专注于解答 Materials Studio 软件用户问题的在线服务网站">
    <meta name="keywords" content="Materials Studio, 材料模拟, 分子模拟, 材料科学, 问答平台">
    <title>Materials Studio 答疑平台</title>
    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Material Design Web Components -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- 动画效果库 -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    
    <!-- 3D分子可视化和粒子动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/3dmol@1.8.0/build/3Dmol-min.js"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <!-- 动态背景效果 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tsparticles/2.9.3/tsparticles.min.js"></script>
    <style>
        :root {
            /* 调色板 */
            --primary-color: #1976d2;
            --secondary-color: #0d47a1;
            --accent-color: #03a9f4;
            --light-bg: #f5f5f5;
            --member-color: #ffc107;
            --success-color: #4caf50;
            --info-color: #03a9f4;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --dark-color: #212121;
            --text-color: #212121;
            --light-text: #ffffff;
            --border-radius: 8px;
            --box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 4px 8px rgba(0,0,0,0.06);
            --transition-speed: 0.3s;
        }
        
        /* 基础样式 */
        body {
            font-family: 'Roboto', 'Microsoft YaHei', Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            overflow-x: hidden;
            background-color: var(--light-bg);
            margin: 0;
            padding: 0;
        }
        
        /* 导航栏 */
        .navbar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all var(--transition-speed);
        }
        
        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255,255,255,.85);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all var(--transition-speed);
        }
        
        .navbar-dark .navbar-nav .nav-link:hover,
        .navbar-dark .navbar-nav .nav-link.active {
            color: rgba(255,255,255,1);
            background-color: rgba(255,255,255,0.2);
            font-weight: 600;
        }
        
        .member-badge {
            background-color: var(--member-color);
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        /* 英雄区域 */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
        }
        
        .hero-search-container {
            position: relative;
            max-width: 600px;
            margin: 2rem auto;
        }
        
        .hero-search-input {
            width: 100%;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            border: none;
            font-size: 1.1rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            padding-right: 60px;
            transition: all 0.3s ease;
        }
        
        .hero-search-btn {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color);
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 特色区域 */
        .feature-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            margin-bottom: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        /* 会员区域 */
        .membership-section {
            background: linear-gradient(to right, #ffefba, #ffffff);
            padding: 80px 0;
        }
        
        .membership-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            margin-bottom: 30px;
            border-top: 5px solid var(--member-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .membership-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        /* 页脚 */
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
        }
        
        .footer-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .footer-links a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
        }
        
        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }
        
        .social-links a:hover {
            background-color: var(--primary-color);
        }
        
        /* 科技感动画效果 */
        .molecule-container {
            position: relative;
            height: 100%;
            width: 100%;
            overflow: hidden;
            border-radius: var(--border-radius);
        }
        
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }
        
        /* 科技感加载动画 */
        .loader {
            display: inline-block;
            position: relative;
            width: 80px;
            height: 80px;
        }
        
        .loader div {
            position: absolute;
            border: 4px solid var(--primary-color);
            opacity: 1;
            border-radius: 50%;
            animation: loader 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
        }
        
        .loader div:nth-child(2) {
            animation-delay: -0.5s;
        }
        
        @keyframes loader {
            0% {
                top: 36px;
                left: 36px;
                width: 0;
                height: 0;
                opacity: 1;
            }
            100% {
                top: 0px;
                left: 0px;
                width: 72px;
                height: 72px;
                opacity: 0;
            }
        }
        
        /* 动态数据流效果 */
        .data-stream {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .data-particle {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.5);
            width: 2px;
            height: 10px;
            animation: data-flow 8s linear infinite;
        }
        
        @keyframes data-flow {
            0% {
                transform: translateY(-100%);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(1000%);
                opacity: 0;
            }
        }
        
        /* 分子动画控制按钮 */
        .molecule-controls {
            position: absolute;
            bottom: 10px;
            right: 10px;
            z-index: 100;
            display: flex;
            gap: 5px;
        }
        
        .molecule-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.8);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .molecule-btn:hover {
            background: white;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        /* 会员卡片科技感效果 */
        .tech-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(13, 71, 161, 0.1) 100%);
            z-index: -1;
            overflow: hidden;
        }
        
        .tech-background::before,
        .tech-background::after {
            content: '';
            position: absolute;
            width: 200%;
            height: 200%;
            top: -50%;
            left: -50%;
            background-image: 
                linear-gradient(transparent 0%, transparent 49%, rgba(25, 118, 210, 0.1) 50%, transparent 51%, transparent 100%),
                linear-gradient(90deg, transparent 0%, transparent 49%, rgba(25, 118, 210, 0.1) 50%, transparent 51%, transparent 100%);
            background-size: 20px 20px;
            animation: grid-animation 30s linear infinite;
        }
        
        .tech-background::after {
            background-size: 40px 40px;
            animation-duration: 60s;
            opacity: 0.5;
        }
        
        @keyframes grid-animation {
            0% {
                transform: rotate(0);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        /* 脉冲效果 */
        .pulse-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: inherit;
            background-color: var(--member-color);
            opacity: 0.6;
            z-index: -1;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.6;
            }
            50% {
                transform: scale(1.2);
                opacity: 0;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        
        /* 浮动科技图标 */
        .floating-tech-icons {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: -1;
            overflow: hidden;
        }
        
        .floating-tech-icons i {
            position: absolute;
            color: rgba(25, 118, 210, 0.1);
            font-size: 1.5rem;
            animation: float-icons 15s linear infinite;
        }
        
        .floating-tech-icons i:nth-child(1) {
            top: 15%;
            left: 10%;
            font-size: 2rem;
            animation-duration: 20s;
        }
        
        .floating-tech-icons i:nth-child(2) {
            top: 40%;
            left: 85%;
            font-size: 1.8rem;
            animation-duration: 25s;
            animation-delay: 2s;
        }
        
        .floating-tech-icons i:nth-child(3) {
            top: 80%;
            left: 15%;
            font-size: 1.6rem;
            animation-duration: 18s;
            animation-delay: 5s;
        }
        
        .floating-tech-icons i:nth-child(4) {
            top: 20%;
            left: 75%;
            font-size: 2.2rem;
            animation-duration: 22s;
            animation-delay: 8s;
        }
        
        @keyframes float-icons {
            0% {
                transform: translate(0, 0) rotate(0deg);
            }
            25% {
                transform: translate(10px, 15px) rotate(90deg);
            }
            50% {
                transform: translate(5px, -10px) rotate(180deg);
            }
            75% {
                transform: translate(-10px, 5px) rotate(270deg);
            }
            100% {
                transform: translate(0, 0) rotate(360deg);
            }
        }
        
        /* 科技装饰元素 */
        .tech-decorator {
            position: absolute;
            width: 80px;
            height: 80px;
            z-index: 0;
            opacity: 0.1;
        }
        
        .tech-decorator.top-right {
            top: -20px;
            right: -20px;
            border-top: 2px solid var(--primary-color);
            border-right: 2px solid var(--primary-color);
        }
        
        .tech-decorator.bottom-left {
            bottom: -20px;
            left: -20px;
            border-bottom: 2px solid var(--primary-color);
            border-left: 2px solid var(--primary-color);
        }
        
        /* 电路线效果 */
        .circuit-lines {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 0;
            overflow: hidden;
            pointer-events: none;
        }
        
        .circuit-lines::before,
        .circuit-lines::after {
            content: '';
            position: absolute;
            background-color: rgba(25, 118, 210, 0.05);
        }
        
        .circuit-lines::before {
            width: 1px;
            height: 100%;
            left: 15%;
            top: 0;
            animation: circuit-pulse 3s infinite;
        }
        
        .circuit-lines::after {
            width: 100%;
            height: 1px;
            left: 0;
            top: 70%;
            animation: circuit-pulse 3s infinite 1.5s;
        }
        
        @keyframes circuit-pulse {
            0% {
                opacity: 0.1;
                box-shadow: 0 0 0 rgba(25, 118, 210, 0);
            }
            50% {
                opacity: 0.5;
                box-shadow: 0 0 5px rgba(25, 118, 210, 0.5);
            }
            100% {
                opacity: 0.1;
                box-shadow: 0 0 0 rgba(25, 118, 210, 0);
            }
        }
        
        /* 动态箭头 */
        .dynamic-arrow {
            display: inline-block;
            margin-left: 5px;
            transition: transform 0.3s ease;
        }
        
        a:hover .dynamic-arrow {
            transform: translateX(5px);
        }
        
        /* 卡片悬停效果增强 */
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
        }
        
        .card:hover .tech-decorator {
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">Materials Studio 答疑平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="qa.html"><i class="fas fa-question-circle"></i> 问答</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="resources.html"><i class="fas fa-book"></i> 学习资源</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="community.html"><i class="fas fa-users"></i> 社区</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light" href="register.html"><i class="fas fa-user-plus"></i> 注册</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 粒子背景 -->
    <div id="particles-js" style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index: 0;"></div>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6" data-aos="fade-right" data-aos-duration="1000">
                    <h1 class="hero-title">Materials Studio 专业答疑平台</h1>
                    <p class="hero-subtitle">为材料科学研究者提供专业的 Materials Studio 软件使用指导、问题解答和资源共享</p>
                    <div class="d-flex gap-3">
                        <a href="register.html" class="btn btn-light btn-lg px-4 py-2">立即注册</a>
                        <a href="qa.html" class="btn btn-outline-light btn-lg px-4 py-2">浏览问题</a>
                    </div>
                </div>
                <div class="col-md-6" data-aos="fade-left" data-aos-duration="1000">
                    <!-- 3D分子结构可视化 -->
                    <div id="molecule-viewer" style="height: 300px; width: 100%; position: relative; margin-bottom: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.2);"></div>
                    
                    <div class="hero-search-container">
                        <input type="text" class="hero-search-input" placeholder="搜索问题、关键词或主题...">
                        <button class="hero-search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="text-center mt-4">
                        <p class="mb-2">热门搜索:</p>
                        <div class="d-flex flex-wrap justify-content-center gap-2">
                            <a href="#" class="badge bg-light text-dark">分子动力学</a>
                            <a href="#" class="badge bg-light text-dark">晶体结构</a>
                            <a href="#" class="badge bg-light text-dark">密度泛函理论</a>
                            <a href="#" class="badge bg-light text-dark">力场参数</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 特色区域 -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold">我们的特色</h2>
                <p class="lead">专注于 Materials Studio 软件的使用与应用</p>
            </div>
            <div class="row g-4">
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <h3>专业问答</h3>
                        <p>由具有多年 Materials Studio 使用经验的专家提供解答，解决您在研究中遇到的各种问题</p>
                    </div>
                </div>
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3>教学视频</h3>
                        <p>提供详细的视频教程，从基础操作到高级应用，帮助您快速掌握软件使用技巧</p>
                    </div>
                </div>
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3>用户社区</h3>
                        <p>加入我们的用户社区，与其他研究者交流经验，分享您的研究成果和心得</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 会员区域 -->
    <section class="membership-section">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold">会员权益</h2>
                <p class="lead">加入会员，获取更多专业服务和资源</p>
            </div>
            <div class="row g-4">
                <div class="col-md-6" data-aos="fade-right">
                    <div class="membership-card">
                        <h3><i class="fas fa-user me-2"></i>普通用户</h3>
                        <ul class="list-unstyled mt-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 浏览公开问题及回答</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 提交自己的问题</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 参与社区讨论</li>
                            <li class="mb-2"><i class="fas fa-times text-danger me-2"></i> 无法查看专业视频教程</li>
                            <li class="mb-2"><i class="fas fa-times text-danger me-2"></i> 无法下载高级资源</li>
                        </ul>
                        <div class="text-center mt-4">
                            <a href="register.html" class="btn btn-outline-primary">免费注册</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" data-aos="fade-left">
                    <div class="membership-card position-relative overflow-hidden">
                        <!-- 添加动态科技背景 -->
                        <div class="tech-background"></div>
                        
                        <h3><i class="fas fa-crown text-warning me-2"></i>会员用户</h3>
                        <ul class="list-unstyled mt-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 浏览所有问题及专业解答</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 优先解答您提交的问题</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 观看专业视频教程</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 下载高级学习资源</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> 定期专家在线答疑</li>
                        </ul>
                        <div class="text-center mt-4">
                            <a href="membership.html" class="btn btn-warning position-relative">
                                升级会员
                                <!-- 添加脉冲效果 -->
                                <span class="pulse-effect"></span>
                            </a>
                        </div>
                        
                        <!-- 添加技术感浮动图标 -->
                        <div class="floating-tech-icons">
                            <i class="fas fa-atom"></i>
                            <i class="fas fa-microscope"></i>
                            <i class="fas fa-flask"></i>
                            <i class="fas fa-dna"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 热门问题区域 -->
    <section class="py-5 bg-white">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold">热门问题</h2>
                <p class="lead">探索用户最常提问的 Materials Studio 相关问题</p>
            </div>
            <div class="row g-4">
                <div class="col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="card h-100 border-0 shadow-sm position-relative">
                        <!-- 添加科技装饰元素 -->
                        <div class="tech-decorator top-right"></div>
                        <div class="tech-decorator bottom-left"></div>
                        
                        <div class="card-body">
                            <h5 class="card-title">如何在 Materials Studio 中正确设置分子动力学模拟参数？</h5>
                            <p class="card-text text-muted">分子动力学 · 2023-10-15</p>
                            <p class="card-text">对于不同的体系，分子动力学模拟参数的设置有所不同。本问题详细讨论了各种常见体系的参数设置方法...</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="badge bg-primary">已解答</span>
                                <a href="qa.html" class="btn btn-sm btn-outline-primary">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="card h-100 border-0 shadow-sm position-relative">
                        <!-- 添加科技装饰元素 -->
                        <div class="tech-decorator top-right"></div>
                        <div class="tech-decorator bottom-left"></div>
                        
                        <div class="card-body">
                            <h5 class="card-title">CASTEP 模块计算过程中出现 SCF 收敛问题如何解决？</h5>
                            <p class="card-text text-muted">CASTEP · 2023-10-10</p>
                            <p class="card-text">在进行 CASTEP 计算时，有时会遇到 SCF 收敛困难的问题。本答案提供了多种有效的解决方案...</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="badge bg-warning text-dark">会员内容</span>
                                <a href="qa.html" class="btn btn-sm btn-outline-primary">查看详情</a>
                            </div>
                        </div>
                        
                        <!-- 添加动态连接线 -->
                        <div class="circuit-lines"></div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-5">
                <a href="qa.html" class="btn btn-primary px-4 py-2 position-relative">
                    查看更多问题
                    <!-- 添加动态箭头效果 -->
                    <span class="dynamic-arrow"><i class="fas fa-chevron-right"></i></span>
                </a>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h4 class="footer-title">Materials Studio 答疑平台</h4>
                    <p>专注于为材料科学研究者提供专业的 Materials Studio 软件使用指导、问题解答和资源共享。</p>
                    <div class="social-links mt-3">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-qq"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">快速链接</h5>
                    <div class="footer-links">
                        <a href="index.html">首页</a>
                        <a href="qa.html">问答</a>
                        <a href="resources.html">资源</a>
                        <a href="community.html">社区</a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h5 class="footer-title">帮助</h5>
                    <div class="footer-links">
                        <a href="#">常见问题</a>
                        <a href="#">使用指南</a>
                        <a href="#">联系我们</a>
                        <a href="#">意见反馈</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5 class="footer-title">联系我们</h5>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p><i class="fas fa-phone me-2"></i> ************</p>
                    <p><i class="fas fa-map-marker-alt me-2"></i> 北京市海淀区中关村科技园</p>
                </div>
            </div>
            <hr class="mt-4 mb-3 bg-light">
            <div class="text-center py-3">
                <p class="mb-0">&copy; 2023 Materials Studio 答疑平台. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    
    <!-- API请求脚本 -->
    <script src="js/api/config.js"></script>
    <script src="js/api/utils.js"></script>
    <script src="js/api/auth.js"></script>
    <script src="js/api/content.js"></script>
    <script src="js/api/index.js"></script>

    <script>
        // 初始化AOS动画
            AOS.init({
                duration: 800,
                once: true
            });
            
        // 初始化粒子背景
        particlesJS('particles-bg', {
                particles: {
                number: { value: 80, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.5, random: false },
                size: { value: 3, random: true },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: "#ffffff",
                        opacity: 0.4,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 2,
                        direction: "none",
                        random: false,
                        straight: false,
                        out_mode: "out",
                    bounce: false
                    }
                },
                interactivity: {
                    detect_on: "canvas",
                    events: {
                    onhover: { enable: true, mode: "grab" },
                    onclick: { enable: true, mode: "push" },
                        resize: true
                    },
                    modes: {
                    grab: { distance: 140, line_linked: { opacity: 1 } },
                    push: { particles_nb: 4 }
                    }
                },
                retina_detect: true
            });
            
        // 在页面加载时检查用户登录状态并更新导航栏
        document.addEventListener('api:ready', async () => {
            try {
                updateNavbar();
                await loadFeaturedContent();
                await loadHotTopics();
            } catch (error) {
                console.error('页面初始化错误:', error);
            }
        });

        // 更新导航栏显示
        async function updateNavbar() {
            const userNavElement = document.getElementById('user-nav');
            
            if (window.api.auth.isLoggedIn()) {
                try {
                    // 获取用户信息
                    const user = await window.api.auth.getCurrentUser();
                    const membershipInfo = await window.api.user.getMembershipInfo();
                    
                    // 更新导航栏显示已登录状态
                    let memberBadge = '';
                    if (membershipInfo && membershipInfo.is_active) {
                        memberBadge = `<span class="member-badge">${membershipInfo.level_name || '会员'}</span>`;
                    }
                    
                    userNavElement.innerHTML = `
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle"></i> ${user.username} ${memberBadge}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.html"><i class="fas fa-id-card"></i> 个人资料</a></li>
                                <li><a class="dropdown-item" href="membership.html"><i class="fas fa-crown"></i> 会员中心</a></li>
                                <li><a class="dropdown-item" href="favorites.html"><i class="fas fa-star"></i> 我的收藏</a></li>
                                <li><a class="dropdown-item" href="my-questions.html"><i class="fas fa-question-circle"></i> 我的提问</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                            </ul>
                        </li>
                    `;
            
                    // 绑定退出登录按钮事件
                    document.getElementById('logout-btn').addEventListener('click', (event) => {
                        event.preventDefault();
                        window.api.auth.logout();
                        window.location.reload();
                    });
                } catch (error) {
                    console.error('获取用户信息失败:', error);
                    // 如果获取用户信息失败，可能是token无效，执行登出
                    window.api.auth.logout();
                    updateNavbarForGuest();
                }
            } else {
                updateNavbarForGuest();
            }
        }

        // 更新导航栏为游客状态
        function updateNavbarForGuest() {
            const userNavElement = document.getElementById('user-nav');
            userNavElement.innerHTML = `
                <li class="nav-item">
                    <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录/注册</a>
                </li>
            `;
        }

        // 加载首页推荐内容
        async function loadFeaturedContent() {
            try {
                const featuredContentElement = document.getElementById('featured-content');
                if (!featuredContentElement) return;
                
                const featuredContent = await window.api.content.getFeaturedContent();
                
                if (!featuredContent || featuredContent.length === 0) {
                    featuredContentElement.innerHTML = '<div class="col-12"><p class="text-center">暂无推荐内容</p></div>';
                    return;
                }
                
                let contentHTML = '';
                
                // 显示最多6个推荐内容
                const displayContent = featuredContent.slice(0, 6);
                
                displayContent.forEach(item => {
                    let premiumBadge = '';
                    if (item.is_premium) {
                        premiumBadge = '<span class="badge bg-warning text-dark ms-2"><i class="fas fa-crown"></i> 会员</span>';
                    }
                    
                    contentHTML += `
                        <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="${Math.floor(Math.random() * 300)}">
                            <div class="feature-card h-100">
                                <h5>${item.title} ${premiumBadge}</h5>
                                <p class="text-muted small">${new Date(item.created_at).toLocaleDateString()}</p>
                                <p>${item.description.substring(0, 100)}${item.description.length > 100 ? '...' : ''}</p>
                                <a href="${getItemUrl(item)}" class="btn btn-sm btn-primary mt-auto">查看详情</a>
                            </div>
                        </div>
                    `;
                });
                
                featuredContentElement.innerHTML = contentHTML;
            } catch (error) {
                console.error('加载推荐内容失败:', error);
                document.getElementById('featured-content').innerHTML = '<div class="col-12"><p class="text-center">加载推荐内容失败，请稍后重试</p></div>';
            }
        }

        // 获取不同类型内容的URL
        function getItemUrl(item) {
            switch(item.type) {
                case 'question':
                    return `qa-detail.html?id=${item.id}`;
                case 'article':
                    return `article.html?id=${item.id}`;
                case 'video':
                    return `video.html?id=${item.id}`;
                case 'resource':
                    return `resource.html?id=${item.id}`;
                default:
                    return '#';
            }
        }

        // 加载热门话题
        async function loadHotTopics() {
            try {
                const hotTopicsElement = document.getElementById('hot-topics');
                if (!hotTopicsElement) return;
                
                const hotTopics = await window.api.content.getHotTopics(5);
                
                if (!hotTopics || hotTopics.length === 0) {
                    hotTopicsElement.innerHTML = '<li class="list-group-item">暂无热门话题</li>';
                    return;
                }
                
                let topicsHTML = '';
                
                hotTopics.forEach(topic => {
                    topicsHTML += `
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <a href="qa.html?tag=${topic.tag}" class="text-decoration-none">${topic.name}</a>
                            <span class="badge bg-primary rounded-pill">${topic.count}</span>
                        </li>
                    `;
                });
                
                hotTopicsElement.innerHTML = topicsHTML;
            } catch (error) {
                console.error('加载热门话题失败:', error);
                document.getElementById('hot-topics').innerHTML = '<li class="list-group-item">加载热门话题失败，请稍后重试</li>';
            }
        }

        // 处理搜索功能
        document.getElementById('search-form').addEventListener('submit', function(event) {
            event.preventDefault();
            const query = document.getElementById('search-input').value.trim();
            
            if (query) {
                window.location.href = `search.html?q=${encodeURIComponent(query)}`;
            }
        });
    </script>
</body>
</html> 