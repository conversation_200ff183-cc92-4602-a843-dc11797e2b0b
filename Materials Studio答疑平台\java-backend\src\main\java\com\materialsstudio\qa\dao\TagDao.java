package com.materialsstudio.qa.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.materialsstudio.qa.entity.Tag;
import com.materialsstudio.qa.model.vo.TagVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 标签DAO接口
 */
public interface TagDao extends BaseMapper<Tag> {
    
    /**
     * 获取所有标签
     *
     * @return 标签列表
     */
    @Select("SELECT * FROM tag WHERE deleted = 0 ORDER BY count DESC")
    List<TagVO> getAllTags();
    
    /**
     * 获取热门标签
     *
     * @param limit 数量限制
     * @return 热门标签列表
     */
    @Select("SELECT * FROM tag WHERE deleted = 0 ORDER BY count DESC LIMIT #{limit}")
    List<TagVO> getHotTags(@Param("limit") Integer limit);
    
    /**
     * 根据问题ID获取标签列表
     *
     * @param questionId 问题ID
     * @return 标签列表
     */
    @Select("SELECT t.* FROM tag t " +
            "INNER JOIN question_tag qt ON t.id = qt.tag_id " +
            "WHERE qt.question_id = #{questionId} AND t.deleted = 0")
    List<TagVO> getTagsByQuestionId(@Param("questionId") Long questionId);
    
    /**
     * 增加标签使用次数
     *
     * @param id 标签ID
     * @return 影响行数
     */
    @Update("UPDATE tag SET count = count + 1 WHERE id = #{id}")
    int incrementCount(@Param("id") Long id);
} 