from fastapi import APIRouter

from app.api.endpoints import auth, users, qa, content, search

api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户"])
api_router.include_router(qa.router, prefix="/qa", tags=["问答"])
api_router.include_router(content.router, prefix="/content", tags=["内容"])
api_router.include_router(search.router, prefix="/search", tags=["搜索"]) 