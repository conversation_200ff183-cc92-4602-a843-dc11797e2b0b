from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy.orm import Session

from app.core.security import get_password_hash, verify_password, create_access_token
from app.models.user import User, UserProfile, Membership
from app.schemas.user import UserCreate, UserUpdate, UserProfileCreate, UserProfileUpdate, MembershipCreate, MembershipUpdate


def authenticate(db: Session, *, email: str, password: str) -> Optional[User]:
    """
    认证用户
    
    Args:
        db: 数据库会话
        email: 用户邮箱
        password: 用户密码
        
    Returns:
        认证成功的用户，如果认证失败则为None
    """
    user = get_by_email(db, email=email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def get_by_id(db: Session, user_id: int) -> Optional[User]:
    """
    根据ID获取用户
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        用户对象，如果不存在则为None
    """
    return db.query(User).filter(User.id == user_id).first()


def get_by_email(db: Session, email: str) -> Optional[User]:
    """
    根据邮箱获取用户
    
    Args:
        db: 数据库会话
        email: 用户邮箱
        
    Returns:
        用户对象，如果不存在则为None
    """
    return db.query(User).filter(User.email == email).first()


def get_by_username(db: Session, username: str) -> Optional[User]:
    """
    根据用户名获取用户
    
    Args:
        db: 数据库会话
        username: 用户名
        
    Returns:
        用户对象，如果不存在则为None
    """
    return db.query(User).filter(User.username == username).first()


def create(db: Session, *, obj_in: UserCreate) -> User:
    """
    创建新用户
    
    Args:
        db: 数据库会话
        obj_in: 用户创建模式
        
    Returns:
        创建的用户对象
    """
    db_obj = User(
        email=obj_in.email,
        username=obj_in.username,
        hashed_password=get_password_hash(obj_in.password),
        full_name=obj_in.full_name,
        is_active=obj_in.is_active,
        is_superuser=obj_in.is_superuser,
        avatar=obj_in.avatar
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    
    # 创建用户个人资料
    create_profile(db, UserProfileCreate(user_id=db_obj.id))
    
    # 创建用户会员信息（默认为免费会员）
    create_membership(db, MembershipCreate(
        user_id=db_obj.id,
        plan="free",
        start_date=datetime.utcnow(),
        end_date=None  # 免费会员无结束日期
    ))
    
    return db_obj


def update(db: Session, *, db_obj: User, obj_in: UserUpdate) -> User:
    """
    更新用户信息
    
    Args:
        db: 数据库会话
        db_obj: 要更新的用户对象
        obj_in: 用户更新模式
        
    Returns:
        更新后的用户对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    # 如果更新密码，需要将密码哈希
    if "password" in update_data and update_data["password"]:
        update_data["hashed_password"] = get_password_hash(update_data["password"])
        del update_data["password"]
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_profile(db: Session, user_id: int) -> Optional[UserProfile]:
    """
    获取用户个人资料
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        用户个人资料对象，如果不存在则为None
    """
    return db.query(UserProfile).filter(UserProfile.user_id == user_id).first()


def create_profile(db: Session, obj_in: UserProfileCreate) -> UserProfile:
    """
    创建用户个人资料
    
    Args:
        db: 数据库会话
        obj_in: 用户个人资料创建模式
        
    Returns:
        创建的用户个人资料对象
    """
    db_obj = UserProfile(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_profile(db: Session, *, db_obj: UserProfile, obj_in: UserProfileUpdate) -> UserProfile:
    """
    更新用户个人资料
    
    Args:
        db: 数据库会话
        db_obj: 要更新的用户个人资料对象
        obj_in: 用户个人资料更新模式
        
    Returns:
        更新后的用户个人资料对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_membership(db: Session, user_id: int) -> Optional[Membership]:
    """
    获取用户会员信息
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        用户会员信息对象，如果不存在则为None
    """
    return db.query(Membership).filter(Membership.user_id == user_id).first()


def create_membership(db: Session, obj_in: MembershipCreate) -> Membership:
    """
    创建用户会员信息
    
    Args:
        db: 数据库会话
        obj_in: 用户会员信息创建模式
        
    Returns:
        创建的用户会员信息对象
    """
    db_obj = Membership(**obj_in.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_membership(db: Session, *, db_obj: Membership, obj_in: MembershipUpdate) -> Membership:
    """
    更新用户会员信息
    
    Args:
        db: 数据库会话
        db_obj: 要更新的用户会员信息对象
        obj_in: 用户会员信息更新模式
        
    Returns:
        更新后的用户会员信息对象
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def activate_membership(db: Session, user_id: int, plan: str, duration_days: int) -> Membership:
    """
    激活用户会员
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        plan: 会员计划
        duration_days: 会员时长（天）
        
    Returns:
        更新后的用户会员信息对象
    """
    membership = get_membership(db, user_id)
    if not membership:
        # 如果用户没有会员信息，创建新的会员信息
        return create_membership(db, MembershipCreate(
            user_id=user_id,
            plan=plan,
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow() + timedelta(days=duration_days),
            is_active=True,
            payment_status="paid"
        ))
    
    # 更新用户会员信息
    membership.plan = plan
    membership.start_date = datetime.utcnow()
    membership.end_date = datetime.utcnow() + timedelta(days=duration_days)
    membership.is_active = True
    membership.payment_status = "paid"
    
    db.add(membership)
    db.commit()
    db.refresh(membership)
    return membership 