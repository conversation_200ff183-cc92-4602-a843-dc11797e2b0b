import os
from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse

from app.api.api import api_router
from app.config.settings import settings

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    description="Materials Studio 知识解答平台 API",
)

# 设置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 创建上传文件夹
os.makedirs(settings.UPLOAD_FOLDER, exist_ok=True)

# 挂载静态文件
app.mount("/uploads", StaticFiles(directory=settings.UPLOAD_FOLDER), name="uploads")


@app.get("/")
def read_root():
    """
    根路径访问
    
    Returns:
        欢迎信息
    """
    return {"message": "欢迎访问 Materials Studio 知识解答平台 API"}


@app.get("/health")
def health_check():
    """
    健康检查接口
    
    Returns:
        健康状态信息
    """
    return {"status": "healthy"}


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理
    
    Args:
        request: 请求对象
        exc: 异常
        
    Returns:
        错误响应
    """
    return JSONResponse(
        status_code=500,
        content={"detail": f"服务器内部错误: {str(exc)}"},
    )


if __name__ == "__main__":
    # 在开发环境中使用uvicorn运行
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 