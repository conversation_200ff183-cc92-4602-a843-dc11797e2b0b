# Materials Studio 知识解答平台 - 后端

本项目是 Materials Studio 知识解答平台的后端部分，基于 FastAPI 框架开发。

## 功能概述

- 用户认证（注册、登录、会员管理）
- 问答系统（提问、回答、评论、标签）
- 资源管理（上传、下载、分类）
- 术语词典
- 搜索功能

## 技术栈

- Python 3.9+
- FastAPI
- SQLAlchemy
- Pydantic
- SQLite (可替换为其他数据库)
- JWT 认证

## 目录结构

```
backend/
├── app/                      # 应用主目录
│   ├── api/                  # API 路由和端点
│   │   ├── endpoints/        # API 端点
│   │   └── api.py            # API 路由注册
│   ├── core/                 # 核心功能模块
│   │   ├── deps.py           # 依赖项
│   │   └── security.py       # 安全相关
│   ├── db/                   # 数据库相关
│   │   ├── init_db.py        # 数据库初始化
│   │   └── session.py        # 数据库会话
│   ├── models/               # 数据库模型
│   ├── schemas/              # Pydantic 模式
│   ├── services/             # 业务服务
│   └── utils/                # 工具函数
├── config/                   # 配置文件
├── tests/                    # 测试
├── .env                      # 环境变量（需要自行创建）
├── main.py                   # 应用入口
└── requirements.txt          # 依赖包列表
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 创建 .env 文件（可选）

创建一个 `.env` 文件，用于配置环境变量：

```
SECRET_KEY=your_secret_key
SQLALCHEMY_DATABASE_URI=sqlite:///./materials_studio.db
```

### 3. 初始化数据库

```bash
python -c "from app.db.init_db import init_db; from app.db.session import SessionLocal; init_db(SessionLocal())"
```

### 4. 运行应用

```bash
uvicorn main:app --reload
```

应用将在 http://127.0.0.1:8000 上运行。

## API 文档

启动应用后，可以访问以下地址查看 API 文档：

- Swagger UI: http://127.0.0.1:8000/api/v1/docs
- ReDoc: http://127.0.0.1:8000/api/v1/redoc

## 主要API概览

### 认证相关
- `POST /api/v1/auth/login`: 用户登录
- `POST /api/v1/auth/register`: 用户注册
- `GET /api/v1/auth/me`: 获取当前用户信息

### 用户相关
- `GET /api/v1/users/`: 获取用户列表(管理员)
- `GET /api/v1/users/{user_id}`: 获取用户信息
- `PUT /api/v1/users/me`: 更新当前用户信息
- `GET /api/v1/users/me/profile`: 获取当前用户个人资料
- `PUT /api/v1/users/me/profile`: 更新当前用户个人资料
- `GET /api/v1/users/me/membership`: 获取当前用户会员信息
- `PUT /api/v1/users/{user_id}/membership`: 更新用户会员信息(管理员)

### 问答相关
- `GET /api/v1/qa/questions/`: 获取问题列表
- `POST /api/v1/qa/questions/`: 创建新问题
- `GET /api/v1/qa/questions/{question_id}`: 获取问题详情
- `PUT /api/v1/qa/questions/{question_id}`: 更新问题
- `DELETE /api/v1/qa/questions/{question_id}`: 删除问题
- `POST /api/v1/qa/questions/{question_id}/answers/`: 创建回答
- `GET /api/v1/qa/answers/{answer_id}`: 获取回答详情
- `PUT /api/v1/qa/answers/{answer_id}`: 更新回答
- `DELETE /api/v1/qa/answers/{answer_id}`: 删除回答
- `POST /api/v1/qa/answers/{answer_id}/comments/`: 创建评论
- `GET /api/v1/qa/tags/`: 获取所有标签

### 内容相关
- `GET /api/v1/content/resources/`: 获取资源列表
- `POST /api/v1/content/resources/`: 上传新资源
- `GET /api/v1/content/resources/{resource_id}`: 获取资源详情
- `PUT /api/v1/content/resources/{resource_id}`: 更新资源信息
- `DELETE /api/v1/content/resources/{resource_id}`: 删除资源
- `GET /api/v1/content/resources/{resource_id}/download`: 下载资源
- `GET /api/v1/content/categories/`: 获取所有资源分类
- `GET /api/v1/content/glossary/`: 获取术语词典列表
- `POST /api/v1/content/glossary/`: 创建术语词典条目
- `GET /api/v1/content/glossary/{glossary_id}`: 获取术语词典条目详情

### 搜索相关
- `GET /api/v1/search/`: 综合搜索
- `GET /api/v1/search/questions/`: 搜索问题
- `GET /api/v1/search/resources/`: 搜索资源
- `GET /api/v1/search/glossary/`: 搜索术语词典

## 数据模型关系

### 用户相关
- `User`: 用户基本信息
- `UserProfile`: 用户个人资料
- `Membership`: 用户会员信息

### 问答相关
- `Question`: 问题
- `Answer`: 回答
- `Comment`: 评论
- `Tag`: 标签
- `QuestionAttachment`: 问题附件
- `AnswerAttachment`: 回答附件

### 内容相关
- `Resource`: 资源
- `ResourceCategory`: 资源分类
- `Glossary`: 术语词典
- `Notification`: 通知
- `SearchHistory`: 搜索历史
- `Favorite`: 收藏

## 项目特点

1. **模块化设计**: 清晰的目录结构和模块化设计，便于维护和扩展。
2. **类型安全**: 使用 Pydantic 进行数据验证和序列化，确保类型安全。
3. **依赖注入**: 利用 FastAPI 的依赖注入系统，简化代码并提高可测试性。
4. **安全性**: 实现了 JWT 认证，确保 API 的安全访问。
5. **文档自动生成**: 通过 FastAPI 自动生成 OpenAPI 文档。
6. **服务层模式**: 将业务逻辑封装在服务层，提高代码复用性和可维护性。
7. **权限控制**: 基于角色的访问控制，区分普通用户和管理员权限。
8. **会员系统**: 实现会员权益控制，部分内容仅对会员开放。

## 后续开发计划

1. 实现文件上传/下载功能
2. 添加全文搜索功能
3. 实现通知系统
4. 添加用户行为统计和分析
5. 引入缓存机制提高性能
6. 完善测试覆盖率

## 许可证

本项目采用 MIT 许可证。 