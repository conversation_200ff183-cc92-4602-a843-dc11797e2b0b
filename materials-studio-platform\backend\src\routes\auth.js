const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const database = require('../database/connection');
const redis = require('../utils/redis');
const config = require('../config');
const logger = require('../utils/logger');
const { asyncHandler, ValidationError, AuthenticationError, ConflictError } = require('../middleware/errorHandler');
const { verifyToken, checkRateLimit } = require('../middleware/auth');

const router = express.Router();

// 认证相关的速率限制
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: {
    error: '登录尝试过于频繁，请15分钟后再试',
    code: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 最多3次注册
  message: {
    error: '注册过于频繁，请1小时后再试',
    code: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 验证规则
const registerValidation = [
  body('username')
    .isLength({ min: 3, max: 20 })
    .withMessage('用户名长度必须在3-20个字符之间')
    .matches(/^[a-zA-Z][a-zA-Z0-9_]*$/)
    .withMessage('用户名必须以字母开头，只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('password')
    .isLength({ min: 8, max: 20 })
    .withMessage('密码长度必须在8-20个字符之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('密码必须包含大小写字母、数字和特殊字符'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('确认密码不匹配');
      }
      return true;
    })
];

const loginValidation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
];

// 生成JWT令牌
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId, type: 'access' },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );

  const refreshToken = jwt.sign(
    { userId, type: 'refresh' },
    config.jwt.refreshSecret,
    { expiresIn: config.jwt.refreshExpiresIn }
  );

  return { accessToken, refreshToken };
};

// 用户注册
router.post('/register', registerLimiter, registerValidation, asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('注册信息验证失败', errors.array());
  }

  const { username, email, phone, password } = req.body;

  // 模拟注册成功（演示模式）
  const userId = Date.now(); // 使用时间戳作为临时ID

  // 记录日志
  logger.logAuth('user_registered', userId, {
    username,
    email,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 生成令牌
  const { accessToken, refreshToken } = generateTokens(userId);

  res.status(201).json({
    success: true,
    message: '注册成功（演示模式）',
    data: {
      userId,
      username,
      email,
      realName: username,
      avatar: null,
      points: 100,
      contributions: 0,
      memberLevel: 0,
      memberEndDate: null,
      isMember: false,
      accessToken,
      refreshToken,
      expiresIn: config.jwt.expiresIn
    }
  });
}));

// 用户登录
router.post('/login', authLimiter, loginValidation, asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('登录信息验证失败', errors.array());
  }

  const { username, password } = req.body;

  // 演示模式：允许任何用户名和密码登录
  if (!username || !password) {
    throw new AuthenticationError('用户名和密码不能为空');
  }

  // 模拟用户数据
  const userId = username === 'admin' ? 1 : Date.now();
  const isAdmin = username === 'admin';

  // 记录日志
  logger.logAuth('user_login', userId, {
    username,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 生成令牌
  const { accessToken, refreshToken } = generateTokens(userId);

  res.json({
    success: true,
    message: '登录成功（演示模式）',
    data: {
      userId,
      username,
      email: `${username}@example.com`,
      realName: username,
      avatar: null,
      points: isAdmin ? 1000 : 100,
      contributions: isAdmin ? 50 : 0,
      memberLevel: isAdmin ? 2 : 0,
      memberEndDate: isAdmin ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) : null,
      isMember: isAdmin,
      isAdmin,
      accessToken,
      refreshToken,
      expiresIn: config.jwt.expiresIn
    }
  });
}));

// 刷新令牌
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new AuthenticationError('缺少刷新令牌');
  }

  try {
    const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret);
    
    if (decoded.type !== 'refresh') {
      throw new AuthenticationError('无效的刷新令牌');
    }

    // 检查Redis中的刷新令牌
    const storedToken = await redis.get(`refresh_token:${decoded.userId}`);
    if (storedToken !== refreshToken) {
      throw new AuthenticationError('刷新令牌已失效');
    }

    // 生成新的令牌
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(decoded.userId);

    // 更新Redis中的刷新令牌
    await redis.set(`refresh_token:${decoded.userId}`, newRefreshToken, 7 * 24 * 60 * 60);

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn: config.jwt.expiresIn
      }
    });

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      throw new AuthenticationError('无效的刷新令牌');
    }
    throw error;
  }
}));

// 用户登出
router.post('/logout', verifyToken, asyncHandler(async (req, res) => {
  const authHeader = req.headers.authorization;
  const token = authHeader.substring(7);

  // 将访问令牌加入黑名单
  const decoded = jwt.decode(token);
  const expiresIn = decoded.exp - Math.floor(Date.now() / 1000);
  if (expiresIn > 0) {
    await redis.set(`blacklist:${token}`, '1', expiresIn);
  }

  // 删除刷新令牌
  await redis.del(`refresh_token:${req.user.id}`);

  // 记录日志
  logger.logAuth('user_logout', req.user.id, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: '登出成功'
  });
}));

// 获取当前用户信息
router.get('/me', verifyToken, asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: req.user
  });
}));

module.exports = router;
