package com.materialsstudio.qa.config.security.filter;

import com.materialsstudio.qa.config.security.UserDetailsServiceImpl;
import com.materialsstudio.qa.utils.JwtUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Resource
    private JwtUtils jwtUtils;
    
    @Resource
    private UserDetailsServiceImpl userDetailsService;
    
    @Value("${jwt.header}")
    private String tokenHeader;
    
    @Value("${jwt.token-prefix}")
    private String tokenPrefix;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        // 获取请求头中的Authorization
        String authHeader = request.getHeader(tokenHeader);
        
        // 如果请求头中有token，则进行解析
        if (authHeader != null && authHeader.startsWith(tokenPrefix)) {
            // 截取token
            String token = authHeader.substring(tokenPrefix.length());
            
            try {
                // 从token中获取用户名
                String username = jwtUtils.getUsernameFromToken(token);
                
                // 如果用户名不为空且SecurityContext中没有认证信息
                if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    // 加载用户信息
                    UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                    
                    // 验证token是否有效
                    if (jwtUtils.validateToken(token, userDetails)) {
                        // 创建认证信息
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                userDetails, null, userDetails.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        
                        // 设置认证信息到SecurityContext
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                    }
                }
            } catch (Exception e) {
                logger.error("无法设置用户认证: " + e.getMessage(), e);
            }
        }
        
        chain.doFilter(request, response);
    }
} 