/**
 * utils.js - API通用工具和设置
 * 提供一些跨API通用的功能
 */

// API基础URL，从配置文件获取
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:8000/api/v1';

/**
 * API设置
 */
const apiSettings = {
    baseUrl: API_BASE_URL,
    timeout: 30000, // 请求超时时间，30秒
    defaultHeaders: {
        'Content-Type': 'application/json'
    },
    debug: true, // 开启调试模式
    logLevel: 'info' // 日志级别: debug, info, warn, error
};

/**
 * 记录API日志
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
function logAPI(level, message, data = null) {
    const levels = {
        debug: 0,
        info: 1,
        warn: 2,
        error: 3
    };

    const currentLevel = levels[apiSettings.logLevel] || 0;
    
    if (levels[level] >= currentLevel && apiSettings.debug) {
        const logPrefix = `[API ${level.toUpperCase()}]`;
        
        switch(level) {
            case 'error':
                console.error(logPrefix, message, data || '');
                break;
            case 'warn':
                console.warn(logPrefix, message, data || '');
                break;
            case 'info':
                console.info(logPrefix, message, data || '');
                break;
            default:
                console.log(logPrefix, message, data || '');
        }
    }
}

/**
 * 获取授权请求头
 * @returns {Object} - 包含Authorization头的对象
 */
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
}

/**
 * 解析API响应
 * @param {Response} response - Fetch API的响应对象
 * @returns {Promise} - 解析后的响应数据
 */
async function parseResponse(response) {
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || `请求失败: ${response.status}`);
        }
        
        return data;
    } else if (contentType && contentType.includes('text/')) {
        const text = await response.text();
        
        if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`);
        }
        
        return text;
    } else {
        if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`);
        }
        
        return await response.blob();
    }
}

/**
 * 发送API请求
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise} - 包含响应数据的Promise
 */
async function apiRequest(url, options = {}) {
    const requestUrl = url.startsWith('http') ? url : `${apiSettings.baseUrl}${url}`;
    
    // 合并默认头部和提供的头部
    const headers = {
        ...apiSettings.defaultHeaders,
        ...options.headers
    };
    
    // 如果是授权请求，添加授权头
    if (options.auth !== false) {
        Object.assign(headers, getAuthHeaders());
    }
    
    // 请求配置
    const requestOptions = {
        ...options,
        headers
    };
    
    // 添加超时处理
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), apiSettings.timeout);
    requestOptions.signal = controller.signal;
    
    // 记录请求信息
    logAPI('info', `${options.method || 'GET'} ${requestUrl}`, { 
        headers: headers, 
        body: options.body ? JSON.parse(options.body) : undefined 
    });
    
    try {
        const startTime = Date.now();
        const response = await fetch(requestUrl, requestOptions);
        const endTime = Date.now();
        clearTimeout(timeoutId);
        
        // 记录响应信息
        logAPI('info', `响应: ${response.status} ${response.statusText}`, { 
            url: requestUrl, 
            time: `${endTime - startTime}ms` 
        });
        
        const result = await parseResponse(response);
        
        // 记录响应数据
        logAPI('debug', '响应数据:', result);
        
        return result;
    } catch (error) {
        if (error.name === 'AbortError') {
            logAPI('error', `请求超时: ${requestUrl}`);
            throw new Error('请求超时');
        }
        logAPI('error', `请求错误: ${requestUrl}`, error);
        throw error;
    }
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {Object} params - 查询参数
 * @param {Object} options - 请求选项
 * @returns {Promise} - 包含响应数据的Promise
 */
async function get(url, params = {}, options = {}) {
    // 构建查询字符串
    const queryParams = new URLSearchParams();
    
    Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
            queryParams.append(key, params[key]);
        }
    });
    
    const queryString = queryParams.toString();
    const requestUrl = queryString ? `${url}?${queryString}` : url;
    
    return apiRequest(requestUrl, {
        method: 'GET',
        ...options
    });
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 请求选项
 * @returns {Promise} - 包含响应数据的Promise
 */
async function post(url, data = {}, options = {}) {
    return apiRequest(url, {
        method: 'POST',
        body: JSON.stringify(data),
        ...options
    });
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 请求选项
 * @returns {Promise} - 包含响应数据的Promise
 */
async function put(url, data = {}, options = {}) {
    return apiRequest(url, {
        method: 'PUT',
        body: JSON.stringify(data),
        ...options
    });
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise} - 包含响应数据的Promise
 */
async function del(url, options = {}) {
    return apiRequest(url, {
        method: 'DELETE',
        ...options
    });
}

/**
 * 上传文件
 * @param {string} url - 请求URL
 * @param {FormData} formData - 包含文件的FormData对象
 * @param {Object} options - 请求选项
 * @returns {Promise} - 包含响应数据的Promise
 */
async function uploadFile(url, formData, options = {}) {
    // 上传文件不设置Content-Type，让浏览器自动设置包含boundary的multipart/form-data
    const headers = {
        ...options.headers
    };
    
    delete headers['Content-Type'];
    
    return apiRequest(url, {
        method: 'POST',
        body: formData,
        headers,
        ...options
    });
}

/**
 * 检查API服务器状态
 * @returns {Promise<boolean>} - 服务器是否可用
 */
async function checkServerStatus() {
    try {
        logAPI('info', '检查服务器状态...');
        
        // 使用AbortController实现超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
        
        const response = await fetch(`${apiSettings.baseUrl}/health`, {
            method: 'GET',
            signal: controller.signal,
            headers: apiSettings.defaultHeaders
        });
        
        clearTimeout(timeoutId);
        
        if (response.ok) {
            const data = await response.text();
            logAPI('info', `服务器状态: 可用`, data);
            return true;
        } else {
            logAPI('info', `服务器状态: 不可用 (${response.status})`);
            return false;
        }
    } catch (error) {
        logAPI('error', '检查服务器状态错误:', error);
        return false;
    }
}

/**
 * 设置API基础URL
 * @param {string} baseUrl - 新的API基础URL
 */
function setBaseUrl(baseUrl) {
    apiSettings.baseUrl = baseUrl;
    logAPI('info', `API基础URL已设置为: ${baseUrl}`);
}

/**
 * 设置API超时时间
 * @param {number} timeout - 超时时间（毫秒）
 */
function setTimeout(timeout) {
    apiSettings.timeout = timeout;
    logAPI('info', `API超时时间已设置为: ${timeout}ms`);
}

/**
 * 设置调试模式
 * @param {boolean} enabled - 是否启用调试模式
 */
function setDebugMode(enabled) {
    apiSettings.debug = enabled;
    logAPI('info', `API调试模式: ${enabled ? '开启' : '关闭'}`);
}

/**
 * 设置日志级别
 * @param {string} level - 日志级别: debug, info, warn, error
 */
function setLogLevel(level) {
    apiSettings.logLevel = level;
    logAPI('info', `API日志级别已设置为: ${level}`);
}

// 导出API工具函数
window.apiUtils = {
    getAuthHeaders,
    apiRequest,
    get,
    post,
    put,
    delete: del,
    uploadFile,
    checkServerStatus,
    setBaseUrl,
    setTimeout,
    setDebugMode,
    setLogLevel,
    settings: apiSettings,
    log: logAPI
}; 