from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship

from app.db.session import Base


class Resource(Base):
    """资源模型"""
    __tablename__ = "resources"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    file_path = Column(String(255), nullable=False)
    file_size = Column(Integer)
    file_type = Column(String(50))
    download_count = Column(Integer, default=0)
    is_premium = Column(Boolean, default=False)  # 是否为会员专属资源
    category_id = Column(Integer, ForeignKey("resource_categories.id"))
    uploader_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    category = relationship("ResourceCategory", back_populates="resources")
    uploader = relationship("User", back_populates="resources")
    favorites = relationship("Favorite", back_populates="resource")


class ResourceCategory(Base):
    """资源分类模型"""
    __tablename__ = "resource_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True)
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    resources = relationship("Resource", back_populates="category")


class Glossary(Base):
    """术语词典模型"""
    __tablename__ = "glossary"

    id = Column(Integer, primary_key=True, index=True)
    term = Column(String(100), unique=True, index=True)
    definition = Column(Text, nullable=False)
    example = Column(Text, nullable=True)
    related_terms = Column(String(255), nullable=True)
    category = Column(String(100), nullable=True)
    is_premium = Column(Boolean, default=False)  # 是否为会员专属内容
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Favorite(Base):
    """收藏模型"""
    __tablename__ = "favorites"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    question_id = Column(Integer, ForeignKey("questions.id", ondelete="CASCADE"), nullable=True)
    resource_id = Column(Integer, ForeignKey("resources.id", ondelete="CASCADE"), nullable=True)
    glossary_id = Column(Integer, ForeignKey("glossary.id", ondelete="CASCADE"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="favorites")
    question = relationship("Question", back_populates="favorites")
    resource = relationship("Resource", back_populates="favorites")
    glossary = relationship("Glossary")


class SearchHistory(Base):
    """搜索历史模型"""
    __tablename__ = "search_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    query = Column(String(255), nullable=False)
    search_type = Column(String(50))  # all, question, resource, glossary
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="search_histories")


class Notification(Base):
    """通知模型"""
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    title = Column(String(100), nullable=False)
    content = Column(Text, nullable=False)
    is_read = Column(Boolean, default=False)
    notification_type = Column(String(50))  # answer, comment, system
    reference_id = Column(Integer, nullable=True)  # 关联的对象ID（问题、回答等）
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="notifications") 