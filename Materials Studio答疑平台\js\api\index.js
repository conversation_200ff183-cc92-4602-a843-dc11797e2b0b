/**
 * index.js - API入口文件
 * 集成所有API模块，提供统一的访问入口
 */

// 确保依赖的文件已加载
document.addEventListener('DOMContentLoaded', () => {
    // 检查API实用工具是否已加载
    if (!window.apiUtils) {
        console.error('API实用工具未加载，请确保utils.js已加载');
        return;
    }
    
    // 设置API基础URL（如果需要根据环境变量设置）
    const apiBaseUrl = window.API_BASE_URL || 'http://localhost:8000/api/v1';
    window.apiUtils.setBaseUrl(apiBaseUrl);
    
    // 初始化API集合
    window.api = {
        // 从各个模块中导入API
        auth: window.authAPI || {},
        qa: window.qaAPI || {},
        user: window.userAPI || {},
        content: window.contentAPI || {},
        utils: window.apiUtils || {}
    };
    
    // 移除全局变量，只保留统一的api入口
    if (window.authAPI) delete window.authAPI;
    if (window.qaAPI) delete window.qaAPI;
    if (window.userAPI) delete window.userAPI;
    if (window.contentAPI) delete window.contentAPI;
    if (window.apiUtils) delete window.apiUtils;
    
    // 检查服务器状态
    window.api.utils.checkServerStatus()
        .then(isAvailable => {
            if (!isAvailable) {
                console.warn('API服务器不可用，某些功能可能无法正常工作');
            } else {
                console.log('API服务器已连接');
            }
        })
        .catch(error => {
            console.error('检查API服务器状态时出错:', error);
        });
    
    // 触发API就绪事件
    const apiReadyEvent = new CustomEvent('api:ready', { detail: { api: window.api } });
    document.dispatchEvent(apiReadyEvent);
});

// 导出API版本信息
const API_VERSION = {
    version: '1.0.0',
    name: 'Materials Studio 答疑平台 API',
    modules: ['auth', 'qa', 'user', 'content', 'utils']
};

// 设置全局API版本
window.API_VERSION = API_VERSION; 