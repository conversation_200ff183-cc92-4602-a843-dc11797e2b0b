package com.materialsstudio.qa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问题实体类
 */
@Data
@TableName("question")
public class Question implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 问题ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 问题标题
     */
    private String title;
    
    /**
     * 问题内容
     */
    private String content;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 是否为会员专属：0-否，1-是
     */
    private Integer isPremium;
    
    /**
     * 查看次数
     */
    private Integer viewCount;
    
    /**
     * 状态：0-未解决，1-已解决
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
} 