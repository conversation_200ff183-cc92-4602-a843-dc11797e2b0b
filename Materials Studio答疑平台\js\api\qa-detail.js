/**
 * qa-detail.js - 问题详情页面的脚本
 * 处理问题详情展示、回答列表和提交回答功能
 */

// 全局变量
let currentQuestionId = null;
let questionData = null;
let answerList = [];
let currentSort = 'votes'; // 默认排序方式
let markdownEditor = null;

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 初始化AOS动画库
    AOS.init({
        duration: 800,
        once: true
    });
    
    // 从URL获取问题ID
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('id')) {
        currentQuestionId = urlParams.get('id');
        
        // 创建一个自定义事件，表示API加载完成
        const apiReadyEvent = new CustomEvent('api:ready');
        
        // 监听API加载完成事件
        document.addEventListener('api:ready', async () => {
            try {
                // 更新导航栏状态
                updateNavbar();
                
                // 加载问题详情
                await loadQuestionDetail();
                
                // 加载回答列表
                await loadAnswers();
                
                // 加载相关问题
                loadRelatedQuestions();
                
                // 加载热门标签
                loadPopularTags();
                
                // 初始化回答编辑器
                initAnswerEditor();
                
                // 设置事件监听器
                setupEventListeners();
                
                // 增加问题浏览计数
                incrementViewCount();
                
            } catch (error) {
                console.error('页面初始化错误:', error);
                showError('加载页面数据失败，请稍后重试');
            }
        });
        
        // 在页面加载后模拟API加载完成事件
        setTimeout(() => {
            document.dispatchEvent(apiReadyEvent);
        }, 100);
    } else {
        // 未提供问题ID，显示错误信息
        document.getElementById('question-detail').innerHTML = `
            <div class="alert alert-danger m-4">
                <i class="fas fa-exclamation-circle me-2"></i> 未提供有效的问题ID，请返回<a href="qa.html" class="alert-link">问答列表</a>选择问题
            </div>
        `;
    }
});

/**
 * 更新导航栏显示
 */
async function updateNavbar() {
    const userNavElement = document.getElementById('user-nav');
    
    if (window.authAPI && window.authAPI.isLoggedIn()) {
        try {
            // 获取用户信息
            const user = await window.authAPI.getCurrentUser();
            const membershipInfo = await window.userAPI.getMembershipInfo();
            
            // 更新导航栏显示已登录状态
            let memberBadge = '';
            if (membershipInfo && membershipInfo.is_active) {
                memberBadge = `<span class="member-badge">${membershipInfo.level_name || '会员'}</span>`;
            }
            
            userNavElement.innerHTML = `
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle"></i> ${user.username} ${memberBadge}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.html"><i class="fas fa-id-card"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="membership.html"><i class="fas fa-crown"></i> 会员中心</a></li>
                        <li><a class="dropdown-item" href="favorites.html"><i class="fas fa-star"></i> 我的收藏</a></li>
                        <li><a class="dropdown-item" href="my-questions.html"><i class="fas fa-question-circle"></i> 我的提问</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </li>
            `;
            
            // 绑定退出登录按钮事件
            document.getElementById('logout-btn').addEventListener('click', (event) => {
                event.preventDefault();
                window.authAPI.logout();
                window.location.reload();
            });
            
            // 显示回答表单，隐藏登录提示
            document.getElementById('submit-answer-form').style.display = 'block';
            document.getElementById('submit-answer-login-message').style.display = 'none';
        } catch (error) {
            console.error('获取用户信息失败:', error);
            // 如果获取用户信息失败，可能是token无效，执行登出
            window.authAPI.logout();
            updateNavbarForGuest();
        }
    } else {
        updateNavbarForGuest();
    }
}

/**
 * 更新导航栏为游客状态
 */
function updateNavbarForGuest() {
    const userNavElement = document.getElementById('user-nav');
    userNavElement.innerHTML = `
        <li class="nav-item">
            <a class="nav-link" href="login.html"><i class="fas fa-sign-in-alt"></i> 登录</a>
        </li>
        <li class="nav-item">
            <a class="btn btn-outline-light" href="register.html"><i class="fas fa-user-plus"></i> 注册</a>
        </li>
    `;
    
    // 隐藏回答表单，显示登录提示
    document.getElementById('submit-answer-form').style.display = 'none';
    document.getElementById('submit-answer-login-message').style.display = 'block';
}

/**
 * 加载问题详情
 */
async function loadQuestionDetail() {
    try {
        showLoading(true);
        
        // 调用API获取问题详情
        questionData = await window.api.qa.getQuestion(currentQuestionId);
        
        // 更新页面标题
        document.title = `${questionData.title} - Materials Studio 答疑平台`;
        document.querySelector('.question-title-text').textContent = questionData.title;
        
        // 构建问题详情HTML
        const questionHTML = buildQuestionDetailHTML(questionData);
        
        // 更新DOM
        document.getElementById('question-detail').innerHTML = questionHTML;
        
        // 更新侧边栏信息
        updateQuestionSidebar(questionData);
        
        // 初始化代码高亮
        document.querySelectorAll('pre code').forEach((el) => {
            hljs.highlightElement(el);
        });
        
        // 处理会员内容的显示
        handleMemberContent();
        
    } catch (error) {
        console.error('加载问题详情失败:', error);
        document.getElementById('question-detail').innerHTML = `
            <div class="alert alert-danger m-4">
                <i class="fas fa-exclamation-circle me-2"></i> 加载问题详情失败，请稍后重试
            </div>
        `;
    } finally {
        showLoading(false);
    }
}

/**
 * 构建问题详情HTML
 * @param {Object} question - 问题数据
 * @returns {string} - 问题详情HTML
 */
function buildQuestionDetailHTML(question) {
    // 格式化日期
    const createdDate = new Date(question.created_at).toLocaleString();
    
    // 处理标签
    let tagsHTML = '';
    if (question.tags && question.tags.length > 0) {
        question.tags.forEach(tag => {
            tagsHTML += `<a href="qa.html?tag=${tag.id}" class="question-tag tag-${tag.category || 'default'}">${tag.name}</a>`;
        });
    }
    
    // 准备状态标签
    const statusBadges = [];
    if (question.is_solved) {
        statusBadges.push('<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>已解决</span>');
    }
    if (question.is_premium) {
        statusBadges.push('<span class="badge bg-warning text-dark"><i class="fas fa-crown me-1"></i>会员内容</span>');
    }
    
    // 构建问题内容，转换Markdown为HTML
    let contentHTML = '';
    try {
        contentHTML = marked.parse(question.content);
    } catch (e) {
        contentHTML = `<p>${question.content}</p>`;
    }
    
    // 处理媒体内容
    let mediaHTML = '';
    if (question.media && question.media.length > 0) {
        mediaHTML = '<div class="media-container mt-4">';
        question.media.forEach(media => {
            if (media.type === 'image') {
                mediaHTML += `<img src="${media.url}" alt="${media.description || '图片'}" class="img-fluid mb-3">`;
                if (media.description) {
                    mediaHTML += `<p class="text-center text-muted small mb-4">${media.description}</p>`;
                }
            } else if (media.type === 'video') {
                mediaHTML += `
                    <div class="ratio ratio-16x9 mb-3">
                        <video controls>
                            <source src="${media.url}" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                `;
                if (media.description) {
                    mediaHTML += `<p class="text-center text-muted small mb-4">${media.description}</p>`;
                }
            }
        });
        mediaHTML += '</div>';
    }
    
    // 构建作者信息
    const authorInitial = question.author.username.charAt(0).toUpperCase();
    
    return `
        <div class="question-header">
            <h1 class="question-title">${question.title}</h1>
            <div class="question-meta">
                <span><i class="fas fa-clock me-1"></i>${createdDate}</span>
                <span><i class="fas fa-eye me-1"></i>${question.view_count || 0} 次浏览</span>
                <span><i class="fas fa-comment me-1"></i>${question.answer_count || 0} 个回答</span>
                ${statusBadges.length > 0 ? '<span>' + statusBadges.join(' ') + '</span>' : ''}
            </div>
        </div>
        <div class="question-body">
            <div class="question-content">
                ${contentHTML}
            </div>
            ${mediaHTML}
            <div class="question-tags mt-4">
                ${tagsHTML}
            </div>
            <div class="author-info">
                <div class="author-avatar">
                    ${question.author.avatar_url ? `<img src="${question.author.avatar_url}" alt="${question.author.username}" class="img-fluid">` : authorInitial}
                </div>
                <div>
                    <div class="author-name">${question.author.username}</div>
                    <div class="author-role">${question.author.role || '用户'}</div>
                </div>
                <div class="ms-auto">
                    <button class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-user-plus me-1"></i>关注
                    </button>
                </div>
            </div>
        </div>
        <div class="question-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button class="action-btn" id="vote-up-btn" title="这个问题有用">
                        <i class="fas fa-thumbs-up"></i> ${question.vote_up || 0}
                    </button>
                    <button class="action-btn" id="vote-down-btn" title="这个问题没用">
                        <i class="fas fa-thumbs-down"></i> ${question.vote_down || 0}
                    </button>
                </div>
                <div>
                    <button class="action-btn" id="share-question-btn" title="分享问题">
                        <i class="fas fa-share-alt"></i> 分享
                    </button>
                    <button class="action-btn" id="report-question-btn" title="举报问题">
                        <i class="fas fa-flag"></i> 举报
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * 更新问题侧边栏信息
 * @param {Object} question - 问题数据
 */
function updateQuestionSidebar(question) {
    // 更新问题状态信息
    document.getElementById('question-date').textContent = new Date(question.created_at).toLocaleDateString();
    document.getElementById('question-views').textContent = question.view_count || 0;
    document.getElementById('question-answer-count').textContent = question.answer_count || 0;
    
    // 更新问题状态
    let statusText = question.is_solved ? '已解决' : '待解答';
    let statusClass = question.is_solved ? 'text-success' : 'text-primary';
    if (question.is_premium) {
        statusText += ' (会员内容)';
    }
    document.getElementById('question-status').innerHTML = `<span class="${statusClass}">${statusText}</span>`;
    
    // 更新收藏按钮状态
    const favoriteBtn = document.getElementById('favorite-btn');
    if (question.is_favorited) {
        favoriteBtn.innerHTML = '<i class="fas fa-star me-2"></i>已收藏';
        favoriteBtn.classList.remove('btn-outline-primary');
        favoriteBtn.classList.add('btn-primary');
    } else {
        favoriteBtn.innerHTML = '<i class="far fa-star me-2"></i>收藏问题';
        favoriteBtn.classList.add('btn-outline-primary');
        favoriteBtn.classList.remove('btn-primary');
    }
    
    // 更新作者信息
    const author = question.author;
    document.getElementById('author-name').textContent = author.username;
    document.getElementById('author-role').textContent = author.role || '用户';
    
    if (author.stats) {
        document.getElementById('author-questions').textContent = author.stats.question_count || 0;
        document.getElementById('author-answers').textContent = author.stats.answer_count || 0;
        document.getElementById('author-joined').textContent = new Date(author.created_at).toLocaleDateString();
    }
    
    // 设置作者头像
    const authorAvatar = document.getElementById('author-avatar');
    if (author.avatar_url) {
        authorAvatar.innerHTML = `<img src="${author.avatar_url}" alt="${author.username}" class="img-fluid">`;
    } else {
        authorAvatar.textContent = author.username.charAt(0).toUpperCase();
    }
}

/**
 * 处理会员内容的显示
 */
async function handleMemberContent() {
    // 检查用户是否登录且是会员
    let isMember = false;
    
    if (window.authAPI && window.authAPI.isLoggedIn()) {
        try {
            const membershipInfo = await window.userAPI.getMembershipInfo();
            isMember = membershipInfo && membershipInfo.is_active;
        } catch (error) {
            console.error('获取会员信息失败:', error);
        }
    }
    
    // 处理问题内容中的会员专享部分
    const memberContentElements = document.querySelectorAll('.member-content');
    memberContentElements.forEach(element => {
        const overlay = element.querySelector('.member-content-overlay');
        if (overlay) {
            if (isMember) {
                // 会员用户，移除遮罩
                overlay.style.display = 'none';
            } else {
                // 非会员用户，保留遮罩
                overlay.style.display = 'flex';
            }
        }
    });
}

/**
 * 加载回答列表
 */
async function loadAnswers() {
    try {
        if (!currentQuestionId) return;
        
        showAnswersLoading(true);
        
        // 调用API获取问题的回答列表
        const result = await fetchAnswers();
        answerList = result.items || [];
        
        // 更新回答计数
        document.getElementById('answers-count').textContent = `回答 (${answerList.length})`;
        
        // 排序回答
        sortAnswers();
        
        // 显示回答列表
        displayAnswers();
        
    } catch (error) {
        console.error('加载回答列表失败:', error);
        document.getElementById('answers-container').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> 加载回答列表失败，请稍后重试
            </div>
        `;
    } finally {
        showAnswersLoading(false);
    }
}

/**
 * 获取问题的回答列表
 * @returns {Promise<Object>} - 包含回答列表的Promise
 */
async function fetchAnswers() {
    // 这里使用模拟数据，实际开发时替换为真实API调用
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                items: questionData.answers || []
            });
        }, 500);
    });
}

/**
 * 根据当前排序方式对回答进行排序
 */
function sortAnswers() {
    if (!answerList || answerList.length === 0) return;
    
    switch (currentSort) {
        case 'votes':
            // 按投票数排序，已采纳的回答始终排在最前
            answerList.sort((a, b) => {
                if (a.is_accepted !== b.is_accepted) {
                    return a.is_accepted ? -1 : 1;
                }
                return (b.vote_up - b.vote_down) - (a.vote_up - a.vote_down);
            });
            break;
            
        case 'newest':
            // 按时间倒序排序，最新的排在前面，已采纳的回答始终排在最前
            answerList.sort((a, b) => {
                if (a.is_accepted !== b.is_accepted) {
                    return a.is_accepted ? -1 : 1;
                }
                return new Date(b.created_at) - new Date(a.created_at);
            });
            break;
            
        case 'oldest':
            // 按时间正序排序，最早的排在前面，已采纳的回答始终排在最前
            answerList.sort((a, b) => {
                if (a.is_accepted !== b.is_accepted) {
                    return a.is_accepted ? -1 : 1;
                }
                return new Date(a.created_at) - new Date(b.created_at);
            });
            break;
    }
}

/**
 * 显示回答列表
 */
function displayAnswers() {
    const container = document.getElementById('answers-container');
    
    if (!answerList || answerList.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> 暂无回答，快来成为第一个回答者吧！
            </div>
        `;
        return;
    }
    
    let answersHTML = '';
    
    answerList.forEach(answer => {
        answersHTML += buildAnswerHTML(answer);
    });
    
    container.innerHTML = answersHTML;
    
    // 初始化代码高亮
    document.querySelectorAll('pre code').forEach((el) => {
        hljs.highlightElement(el);
    });
    
    // 处理会员内容
    handleMemberContent();
    
    // 添加回答交互事件
    addAnswerInteractions();
}

/**
 * 构建单个回答的HTML
 * @param {Object} answer - 回答数据
 * @returns {string} - 回答HTML
 */
function buildAnswerHTML(answer) {
    // 格式化日期
    const createdDate = new Date(answer.created_at).toLocaleString();
    
    // 构建回答内容，转换Markdown为HTML
    let contentHTML = '';
    try {
        contentHTML = marked.parse(answer.content);
    } catch (e) {
        contentHTML = `<p>${answer.content}</p>`;
    }
    
    // 处理会员内容
    let finalContent = contentHTML;
    if (answer.is_premium) {
        finalContent = `
            <div class="member-content">
                ${contentHTML}
                <div class="member-content-overlay">
                    <div class="member-content-message">
                        <h5><i class="fas fa-lock me-2"></i>会员专享内容</h5>
                        <p>成为会员后可查看完整解答</p>
                    </div>
                    <a href="membership.html" class="btn btn-warning">升级会员</a>
                </div>
            </div>
        `;
    }
    
    // 处理媒体内容
    let mediaHTML = '';
    if (answer.media && answer.media.length > 0) {
        mediaHTML = '<div class="media-container mt-4">';
        answer.media.forEach(media => {
            if (media.type === 'image') {
                mediaHTML += `<img src="${media.url}" alt="${media.description || '图片'}" class="img-fluid mb-3">`;
                if (media.description) {
                    mediaHTML += `<p class="text-center text-muted small mb-4">${media.description}</p>`;
                }
            } else if (media.type === 'video') {
                mediaHTML += `
                    <div class="ratio ratio-16x9 mb-3">
                        <video controls>
                            <source src="${media.url}" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                `;
                if (media.description) {
                    mediaHTML += `<p class="text-center text-muted small mb-4">${media.description}</p>`;
                }
            }
        });
        mediaHTML += '</div>';
    }
    
    // 构建作者信息
    const authorInitial = answer.author.username.charAt(0).toUpperCase();
    
    // 最佳回答标志
    const bestAnswerBadge = answer.is_accepted ? 
        '<span class="best-answer-badge"><i class="fas fa-check-circle me-1"></i>最佳回答</span>' : '';
    
    return `
        <div class="answer-card" data-answer-id="${answer.id}" data-aos="fade-up">
            <div class="answer-header">
                <div class="answer-meta">
                    <span><i class="fas fa-user me-1"></i>${answer.author.username}</span>
                    <span class="ms-3"><i class="fas fa-clock me-1"></i>${createdDate}</span>
                </div>
                ${bestAnswerBadge}
            </div>
            <div class="answer-body">
                <div class="answer-content">
                    ${finalContent}
                </div>
                ${mediaHTML}
                
                <!-- 评论部分 -->
                <div class="comments-section" id="comments-${answer.id}">
                    ${buildCommentsHTML(answer.comments)}
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-secondary add-comment-btn" data-answer-id="${answer.id}">
                            <i class="fas fa-comment me-1"></i>添加评论
                        </button>
                        <div class="add-comment-form mt-2" style="display: none;">
                            <textarea class="form-control form-control-sm comment-text" rows="2" placeholder="输入您的评论..."></textarea>
                            <div class="d-flex justify-content-end mt-2">
                                <button class="btn btn-sm btn-secondary me-2 cancel-comment-btn">取消</button>
                                <button class="btn btn-sm btn-primary submit-comment-btn">提交评论</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="answer-footer">
                <div>
                    <button class="action-btn vote-up-btn" data-answer-id="${answer.id}" title="这个回答有用">
                        <i class="fas fa-thumbs-up"></i> ${answer.vote_up || 0}
                    </button>
                    <button class="action-btn vote-down-btn" data-answer-id="${answer.id}" title="这个回答没用">
                        <i class="fas fa-thumbs-down"></i> ${answer.vote_down || 0}
                    </button>
                </div>
                <div>
                    ${!answer.is_accepted && questionData.is_author ? 
                        `<button class="btn btn-sm btn-success accept-answer-btn" data-answer-id="${answer.id}">
                            <i class="fas fa-check-circle me-1"></i>采纳为最佳答案
                        </button>` : ''
                    }
                    <button class="action-btn share-answer-btn" data-answer-id="${answer.id}" title="分享回答">
                        <i class="fas fa-share-alt"></i>
                    </button>
                    <button class="action-btn report-answer-btn" data-answer-id="${answer.id}" title="举报回答">
                        <i class="fas fa-flag"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * 构建评论HTML
 * @param {Array} comments - 评论数组
 * @returns {string} - 评论HTML
 */
function buildCommentsHTML(comments) {
    if (!comments || comments.length === 0) {
        return '<div class="text-muted small">暂无评论</div>';
    }
    
    let commentsHTML = '<h6 class="mt-3 mb-2">评论:</h6>';
    
    comments.forEach(comment => {
        const createdDate = new Date(comment.created_at).toLocaleString();
        
        commentsHTML += `
            <div class="comment">
                <div class="comment-meta">
                    <span class="comment-author">${comment.author.username}</span>
                    <span class="comment-date">${createdDate}</span>
                </div>
                <div class="comment-content">${comment.content}</div>
            </div>
        `;
    });
    
    return commentsHTML;
}

/**
 * 为回答添加交互事件
 */
function addAnswerInteractions() {
    // 投票按钮事件
    document.querySelectorAll('.vote-up-btn').forEach(btn => {
        btn.addEventListener('click', async (event) => {
            event.preventDefault();
            const answerId = btn.getAttribute('data-answer-id');
            await voteAnswer(answerId, 'up');
        });
    });
    
    document.querySelectorAll('.vote-down-btn').forEach(btn => {
        btn.addEventListener('click', async (event) => {
            event.preventDefault();
            const answerId = btn.getAttribute('data-answer-id');
            await voteAnswer(answerId, 'down');
        });
    });
    
    // 采纳回答按钮事件
    document.querySelectorAll('.accept-answer-btn').forEach(btn => {
        btn.addEventListener('click', async (event) => {
            event.preventDefault();
            const answerId = btn.getAttribute('data-answer-id');
            await acceptAnswer(answerId);
        });
    });
    
    // 添加评论按钮事件
    document.querySelectorAll('.add-comment-btn').forEach(btn => {
        btn.addEventListener('click', (event) => {
            event.preventDefault();
            const answerId = btn.getAttribute('data-answer-id');
            const formElement = btn.nextElementSibling;
            
            // 显示评论表单
            formElement.style.display = 'block';
            btn.style.display = 'none';
            
            // 聚焦到文本框
            formElement.querySelector('textarea').focus();
        });
    });
    
    // 取消评论按钮事件
    document.querySelectorAll('.cancel-comment-btn').forEach(btn => {
        btn.addEventListener('click', (event) => {
            event.preventDefault();
            const formElement = btn.closest('.add-comment-form');
            const addCommentBtn = formElement.previousElementSibling;
            
            // 隐藏评论表单，显示添加评论按钮
            formElement.style.display = 'none';
            addCommentBtn.style.display = 'inline-block';
            
            // 清空文本框
            formElement.querySelector('textarea').value = '';
        });
    });
    
    // 提交评论按钮事件
    document.querySelectorAll('.submit-comment-btn').forEach(btn => {
        btn.addEventListener('click', async (event) => {
            event.preventDefault();
            const formElement = btn.closest('.add-comment-form');
            const addCommentBtn = formElement.previousElementSibling;
            const textarea = formElement.querySelector('textarea');
            const answerId = addCommentBtn.getAttribute('data-answer-id');
            const content = textarea.value.trim();
            
            if (content) {
                await addComment(answerId, content);
                
                // 隐藏评论表单，显示添加评论按钮
                formElement.style.display = 'none';
                addCommentBtn.style.display = 'inline-block';
                
                // 清空文本框
                textarea.value = '';
            }
        });
    });
}

/**
 * 对回答进行投票
 * @param {string} answerId - 回答ID
 * @param {string} voteType - 投票类型（up/down）
 */
async function voteAnswer(answerId, voteType) {
    try {
        if (!window.authAPI || !window.authAPI.isLoggedIn()) {
            showLoginPrompt();
            return;
        }
        
        // 调用实际的投票API
        await window.api.qa.voteAnswer(answerId, voteType);
        
        // 更新前端显示
        const answerElement = document.querySelector(`.answer-card[data-answer-id="${answerId}"]`);
        const voteBtn = answerElement.querySelector(`.vote-${voteType}-btn`);
        const currentVotes = parseInt(voteBtn.textContent.trim());
        voteBtn.innerHTML = `<i class="fas fa-thumbs-${voteType}"></i> ${currentVotes + 1}`;
        voteBtn.classList.add('active');
        
        showMessage(`投票成功！`, 'success');
    } catch (error) {
        console.error(`投票失败:`, error);
        showMessage(`投票失败: ${error.message}`, 'danger');
    }
}

/**
 * 采纳回答为最佳答案
 * @param {string} answerId - 回答ID
 */
async function acceptAnswer(answerId) {
    try {
        if (!window.authAPI || !window.authAPI.isLoggedIn()) {
            showLoginPrompt();
            return;
        }
        
        // 调用实际的采纳API
        await window.api.qa.acceptAnswer(currentQuestionId, answerId);
        
        // 更新问题状态
        questionData.is_solved = true;
        
        // 更新所有回答的采纳状态
        answerList.forEach(answer => {
            answer.is_accepted = answer.id.toString() === answerId.toString();
        });
        
        // 重新排序并显示回答
        sortAnswers();
        displayAnswers();
        
        // 更新侧边栏
        updateQuestionSidebar(questionData);
        
        showMessage(`已采纳为最佳答案！`, 'success');
    } catch (error) {
        console.error(`采纳回答失败:`, error);
        showMessage(`采纳回答失败: ${error.message}`, 'danger');
    }
}

/**
 * 添加评论
 * @param {string} answerId - 回答ID
 * @param {string} content - 评论内容
 */
async function addComment(answerId, content) {
    try {
        if (!window.authAPI || !window.authAPI.isLoggedIn()) {
            showLoginPrompt();
            return;
        }
        
        // 调用实际的添加评论API
        let newComment;
        try {
            newComment = await window.api.qa.addComment(answerId, content);
        } catch (apiError) {
            console.error('API调用失败，使用模拟数据:', apiError);
            // 模拟新评论数据作为备用
            newComment = {
                id: Date.now().toString(),
                content: content,
                created_at: new Date().toISOString(),
                author: {
                    username: '当前用户' // 实际应从用户数据获取
                }
            };
        }
        
        // 更新对应回答的评论
        const answer = answerList.find(a => a.id.toString() === answerId.toString());
        if (answer) {
            if (!answer.comments) {
                answer.comments = [];
            }
            answer.comments.push(newComment);
            
            // 更新评论区域
            const commentsContainer = document.getElementById(`comments-${answerId}`);
            commentsContainer.innerHTML = buildCommentsHTML(answer.comments);
            
            // 重新添加添加评论按钮
            commentsContainer.innerHTML += `
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-secondary add-comment-btn" data-answer-id="${answerId}">
                        <i class="fas fa-comment me-1"></i>添加评论
                    </button>
                    <div class="add-comment-form mt-2" style="display: none;">
                        <textarea class="form-control form-control-sm comment-text" rows="2" placeholder="输入您的评论..."></textarea>
                        <div class="d-flex justify-content-end mt-2">
                            <button class="btn btn-sm btn-secondary me-2 cancel-comment-btn">取消</button>
                            <button class="btn btn-sm btn-primary submit-comment-btn">提交评论</button>
                        </div>
                    </div>
                </div>
            `;
            
            // 重新绑定评论相关事件
            addAnswerInteractions();
        }
        
        showMessage(`评论已添加！`, 'success');
    } catch (error) {
        console.error(`添加评论失败:`, error);
        showMessage(`添加评论失败: ${error.message}`, 'danger');
    }
}

/**
 * 初始化回答编辑器
 */
function initAnswerEditor() {
    if (!document.getElementById('answer-content')) return;
    
    // 初始化SimpleMDE编辑器
    markdownEditor = new SimpleMDE({
        element: document.getElementById('answer-content'),
        spellChecker: false,
        placeholder: '支持Markdown格式，可以插入代码、图片和链接...',
        status: ['lines', 'words', 'cursor'],
        toolbar: [
            'bold', 'italic', 'heading', '|', 
            'code', 'quote', 'unordered-list', 'ordered-list', '|',
            'link', 'image', 'table', '|',
            'preview', 'side-by-side', 'fullscreen', '|',
            'guide'
        ]
    });
    
    // 绑定提交回答按钮事件
    document.getElementById('submit-answer-btn').addEventListener('click', submitAnswer);
}

/**
 * 提交回答
 */
async function submitAnswer() {
    try {
        if (!window.authAPI || !window.authAPI.isLoggedIn()) {
            showLoginPrompt();
            return;
        }
        
        const content = markdownEditor.value().trim();
        if (!content) {
            showMessage('回答内容不能为空', 'warning');
            return;
        }
        
        const isPremium = document.getElementById('is-premium-answer').checked;
        
        // 显示加载状态
        const submitBtn = document.getElementById('submit-answer-btn');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
        submitBtn.disabled = true;
        
        // 构建回答数据
        const answerData = {
            content: content,
            is_premium: isPremium
        };
        
        // 调用API提交回答
        const newAnswer = await window.api.qa.createAnswer(currentQuestionId, answerData);
        
        // 清空编辑器
        markdownEditor.value('');
        document.getElementById('is-premium-answer').checked = false;
        
        // 添加新回答到列表并重新显示
        if (answerList) {
            answerList.push(newAnswer);
            sortAnswers();
            displayAnswers();
        }
        
        // 更新回答计数
        questionData.answer_count = (questionData.answer_count || 0) + 1;
        document.getElementById('answers-count').textContent = `回答 (${answerList.length})`;
        document.getElementById('question-answer-count').textContent = questionData.answer_count;
        
        // 显示成功消息
        showMessage('回答已提交成功！', 'success');
        
        // 滚动到回答列表
        document.querySelector('.answers-section').scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
        console.error('提交回答失败:', error);
        showMessage(`提交回答失败: ${error.message}`, 'danger');
    } finally {
        // 恢复按钮状态
        const submitBtn = document.getElementById('submit-answer-btn');
        submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>提交回答';
        submitBtn.disabled = false;
    }
}

/**
 * 加载相关问题
 */
async function loadRelatedQuestions() {
    try {
        if (!currentQuestionId) return;
        
        // 这里使用模拟数据，实际开发时替换为真实API调用
        // const result = await window.qaAPI.getRelatedQuestions(currentQuestionId);
        
        // 这里使用模拟数据，实际开发时替换为真实API调用
        // const result = await window.api.qa.getRelatedQuestions(currentQuestionId);
        
        // 模拟相关问题数据
        const relatedQuestions = [
            {
                id: '101',
                title: 'Materials Studio中如何设置CASTEP计算的收敛参数？',
                created_at: '2023-10-02T10:30:00Z',
                answer_count: 3
            },
            {
                id: '102',
                title: '如何在Materials Studio中正确导入晶体结构？',
                created_at: '2023-09-28T14:15:00Z',
                answer_count: 2
            },
            {
                id: '103',
                title: 'Materials Studio的力场选择原则是什么？',
                created_at: '2023-09-25T09:45:00Z',
                answer_count: 5
            }
        ];
        
        let relatedQuestionsHTML = '';
        
        if (relatedQuestions && relatedQuestions.length > 0) {
            relatedQuestions.forEach(question => {
                const createdDate = new Date(question.created_at).toLocaleDateString();
                
                relatedQuestionsHTML += `
                    <div class="related-question">
                        <h6 class="related-question-title">
                            <a href="qa-detail.html?id=${question.id}">${question.title}</a>
                        </h6>
                        <div class="related-question-meta">
                            <span><i class="fas fa-calendar-alt me-1"></i>${createdDate}</span>
                            <span class="ms-2"><i class="fas fa-comment me-1"></i>${question.answer_count} 回答</span>
                        </div>
                    </div>
                `;
            });
        } else {
            relatedQuestionsHTML = '<p class="text-muted">暂无相关问题</p>';
        }
        
        document.getElementById('related-questions').innerHTML = relatedQuestionsHTML;
    } catch (error) {
        console.error('加载相关问题失败:', error);
        document.getElementById('related-questions').innerHTML = '<p class="text-muted">加载相关问题失败</p>';
    }
}

/**
 * 加载热门标签
 */
async function loadPopularTags() {
    try {
        // 这里使用模拟数据，实际开发时替换为真实API调用
        // const tags = await window.qaAPI.getTags();
        
        // 这里使用模拟数据，实际开发时替换为真实API调用
        // const tags = await window.api.qa.getTags();
        
        // 模拟热门标签数据
        const tags = [
            { id: '1', name: '力场选择', category: 'method', count: 42 },
            { id: '2', name: '计算参数', category: 'parameter', count: 38 },
            { id: '3', name: '错误解决', category: 'error', count: 35 },
            { id: '4', name: '结果分析', category: 'analysis', count: 29 },
            { id: '5', name: '模型构建', category: 'method', count: 24 }
        ];
        
        let tagsHTML = '';
        
        if (tags && tags.length > 0) {
            tags.forEach(tag => {
                tagsHTML += `<a href="qa.html?tag=${tag.id}" class="question-tag tag-${tag.category || 'default'}">${tag.name}</a>`;
            });
        }
        
        document.getElementById('popular-tags').innerHTML = tagsHTML;
    } catch (error) {
        console.error('加载热门标签失败:', error);
    }
}

/**
 * 增加问题浏览计数
 */
async function incrementViewCount() {
    try {
        if (!currentQuestionId) return;
        
        // 这里应调用实际的API来增加浏览计数
        // await window.qaAPI.incrementViewCount(currentQuestionId);

        // 这里应调用实际的API来增加浏览计数
        // await window.api.qa.incrementViewCount(currentQuestionId);
        
        // 模拟更新浏览计数
        setTimeout(() => {
            const viewCountElement = document.getElementById('question-views');
            if (viewCountElement) {
                const currentCount = parseInt(viewCountElement.textContent);
                viewCountElement.textContent = currentCount + 1;
            }
        }, 2000);
    } catch (error) {
        console.error('增加浏览计数失败:', error);
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 收藏按钮点击事件
    document.getElementById('favorite-btn').addEventListener('click', async (event) => {
        event.preventDefault();
        
        if (!window.authAPI || !window.authAPI.isLoggedIn()) {
            showLoginPrompt();
            return;
        }
        
        try {
            const isFavorited = questionData.is_favorited;
            
            if (isFavorited) {
                // 取消收藏
                // await window.qaAPI.removeFromFavorites(currentQuestionId);
                questionData.is_favorited = false;
            } else {
                // 添加收藏
                // await window.qaAPI.addToFavorites(currentQuestionId);
                questionData.is_favorited = true;
            }
            
            // 更新按钮状态
            updateQuestionSidebar(questionData);
            
            showMessage(isFavorited ? '已取消收藏' : '已添加到收藏', 'success');
        } catch (error) {
            console.error('收藏操作失败:', error);
            showMessage(`收藏操作失败: ${error.message}`, 'danger');
        }
    });
    
    // 分享按钮点击事件
    document.getElementById('share-btn').addEventListener('click', (event) => {
        event.preventDefault();
        
        // 构建分享链接
        const shareUrl = window.location.href;
        const shareTitle = document.querySelector('.question-title-text').textContent;
        
        // 显示分享对话框
        showShareDialog(shareUrl, shareTitle);
    });
    
    // 问题投票按钮
    document.getElementById('vote-up-btn').addEventListener('click', async (event) => {
        event.preventDefault();
        
        if (!window.authAPI || !window.authAPI.isLoggedIn()) {
            showLoginPrompt();
            return;
        }
        
        try {
            // await window.qaAPI.voteQuestion(currentQuestionId, 'up');
            
            // 更新前端显示
            const btn = event.currentTarget;
            const currentVotes = parseInt(btn.textContent.trim());
            btn.innerHTML = `<i class="fas fa-thumbs-up"></i> ${currentVotes + 1}`;
            btn.classList.add('active');
            
            showMessage('投票成功！', 'success');
        } catch (error) {
            console.error('投票失败:', error);
            showMessage(`投票失败: ${error.message}`, 'danger');
        }
    });
    
    document.getElementById('vote-down-btn').addEventListener('click', async (event) => {
        event.preventDefault();
        
        if (!window.authAPI || !window.authAPI.isLoggedIn()) {
            showLoginPrompt();
            return;
        }
        
        try {
            // await window.qaAPI.voteQuestion(currentQuestionId, 'down');
            
            // 更新前端显示
            const btn = event.currentTarget;
            const currentVotes = parseInt(btn.textContent.trim());
            btn.innerHTML = `<i class="fas fa-thumbs-down"></i> ${currentVotes + 1}`;
            btn.classList.add('active');
            
            showMessage('投票成功！', 'success');
        } catch (error) {
            console.error('投票失败:', error);
            showMessage(`投票失败: ${error.message}`, 'danger');
        }
    });
    
    // 回答排序方式切换
    document.querySelectorAll('.dropdown-menu a[data-sort]').forEach(item => {
        item.addEventListener('click', (event) => {
            event.preventDefault();
            
            // 更新当前排序方式
            const newSort = event.currentTarget.getAttribute('data-sort');
            if (newSort !== currentSort) {
                currentSort = newSort;
                
                // 更新激活状态
                document.querySelectorAll('.dropdown-menu a[data-sort]').forEach(el => {
                    el.classList.remove('active');
                });
                event.currentTarget.classList.add('active');
                
                // 更新按钮文本
                const sortTexts = {
                    'votes': '按投票数',
                    'newest': '最新回答',
                    'oldest': '最早回答'
                };
                document.getElementById('sortAnswersDropdown').textContent = sortTexts[currentSort] || '排序方式';
                
                // 重新排序并显示回答
                sortAnswers();
                displayAnswers();
            }
        });
    });
}

/**
 * 显示分享对话框
 * @param {string} url - 分享链接
 * @param {string} title - 分享标题
 */
function showShareDialog(url, title) {
    // 构建分享对话框HTML
    const modalHTML = `
        <div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="shareModalLabel">分享问题</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>分享这个问题到：</p>
                        <div class="d-flex justify-content-around mb-4">
                            <a href="https://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}" target="_blank" class="btn btn-outline-danger" title="分享到微博">
                                <i class="fab fa-weibo fa-lg"></i>
                            </a>
                            <a href="javascript:void(0);" class="btn btn-outline-success copy-wechat" title="复制链接分享到微信">
                                <i class="fab fa-weixin fa-lg"></i>
                            </a>
                            <a href="https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}" target="_blank" class="btn btn-outline-primary" title="分享到QQ">
                                <i class="fab fa-qq fa-lg"></i>
                            </a>
                            <a href="javascript:void(0);" class="btn btn-outline-secondary copy-link" title="复制链接">
                                <i class="fas fa-link fa-lg"></i>
                            </a>
                        </div>
                        <div class="input-group">
                            <input type="text" class="form-control" id="share-url" value="${url}" readonly>
                            <button class="btn btn-outline-primary copy-button" type="button">复制</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 添加对话框到页面
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;
    document.body.appendChild(modalContainer);
    
    // 显示对话框
    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();
    
    // 绑定复制链接按钮事件
    document.querySelector('.copy-button').addEventListener('click', () => {
        const shareUrl = document.getElementById('share-url');
        shareUrl.select();
        document.execCommand('copy');
        showMessage('链接已复制到剪贴板', 'success');
    });
    
    // 复制链接按钮
    document.querySelector('.copy-link').addEventListener('click', () => {
        const shareUrl = document.getElementById('share-url');
        shareUrl.select();
        document.execCommand('copy');
        showMessage('链接已复制到剪贴板', 'success');
    });
    
    // 复制到微信按钮
    document.querySelector('.copy-wechat').addEventListener('click', () => {
        const shareUrl = document.getElementById('share-url');
        shareUrl.select();
        document.execCommand('copy');
        showMessage('链接已复制，请粘贴到微信中分享', 'success');
    });
    
    // 对话框关闭后清理
    document.getElementById('shareModal').addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modalContainer);
    });
}

/**
 * 显示登录提示
 */
function showLoginPrompt() {
    showMessage('请先登录后再进行操作', 'info');
    setTimeout(() => {
        window.location.href = `login.html?redirect=${encodeURIComponent(window.location.href)}`;
    }, 1500);
}

/**
 * 显示消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型（success, info, warning, danger）
 */
function showMessage(message, type = 'info') {
    // 创建消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.maxWidth = '300px';
    alertDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // 添加到页面
    document.body.appendChild(alertDiv);
    
    // 自动关闭
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alertDiv);
        bsAlert.close();
    }, 3000);
    
    // 移除元素
    alertDiv.addEventListener('closed.bs.alert', () => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    });
}

/**
 * 显示加载中状态
 * @param {boolean} isLoading - 是否显示加载状态
 */
function showLoading(isLoading) {
    // 这里可以根据需要实现加载状态的显示与隐藏
    // 例如显示/隐藏一个加载指示器
}

/**
 * 显示回答列表加载状态
 * @param {boolean} isLoading - 是否显示加载状态
 */
function showAnswersLoading(isLoading) {
    const container = document.getElementById('answers-container');
    
    if (isLoading) {
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3">正在加载回答...</p>
            </div>
        `;
    }
} 