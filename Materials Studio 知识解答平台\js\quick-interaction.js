/**
 * Materials Studio 知识解答平台 - 快速交互功能模块
 * 用于处理全站通用的交互功能，包括模态框、表单提交、搜索建议等
 */

class QuickInteraction {
    constructor() {
        this.initialized = false;
        this.modals = {};
        this.notifications = [];
        this.searchHistory = JSON.parse(localStorage.getItem('ms_search_history') || '[]');
        this.initComponents();
        this.bindEvents();
        this.setupSearchSuggestions();
        this.loadSearchHistory();
        this.setupFormValidation();
        this.setupMarkdownEditors();
    }

    /**
     * 初始化组件
     */
    initComponents() {
        // 搜索相关组件
        this.searchInput = document.getElementById('mainSearchInput') || document.querySelector('.search-input');
        this.searchSuggestions = document.getElementById('searchSuggestions');
        this.voiceSearchBtn = document.getElementById('voiceSearchBtn');
        this.quickSearchBtn = document.querySelector('.quick-search-btn');
        
        // 快速提问相关组件
        this.quickAskBtn = document.querySelector('[data-bs-target="#quickAskModal"]');
        this.submitQuestionBtn = document.getElementById('submitQuestion');
        
        // 快速反馈相关组件
        this.quickFeedbackBtn = document.querySelector('[data-bs-target="#quickFeedbackModal"]');
        this.submitFeedbackBtn = document.getElementById('submitFeedback');
        
        // 回答问题相关组件
        this.answerBtns = document.querySelectorAll('.answer-btn');
        this.submitAnswerBtn = document.getElementById('submitAnswer');
        
        // 搜索历史相关组件
        this.searchHistoryItems = document.querySelectorAll('.search-history-item');
        this.clearHistoryBtn = document.querySelector('.search-history button');
        
        // 初始化toast通知容器
        this.initToastContainer();
        
        // 初始化文件上传预览组件
        this.initFilePreviewComponents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 搜索相关事件
        if (this.searchInput) {
            this.searchInput.addEventListener('focus', () => this.handleSearchFocus());
            this.searchInput.addEventListener('blur', () => this.handleSearchBlur());
            this.searchInput.addEventListener('input', (e) => this.handleSearchInput(e));
            this.searchInput.addEventListener('keypress', (e) => this.handleSearchKeypress(e));
        }
        
        // 语音搜索
        if (this.voiceSearchBtn) {
            this.voiceSearchBtn.addEventListener('click', () => this.handleVoiceSearch());
        }
        
        // 问题提交
        if (this.submitQuestionBtn) {
            this.submitQuestionBtn.addEventListener('click', () => this.handleQuestionSubmit());
        }
        
        // 反馈提交
        if (this.submitFeedbackBtn) {
            this.submitFeedbackBtn.addEventListener('click', () => this.handleFeedbackSubmit());
        }
        
        // 回答提交
        if (this.submitAnswerBtn) {
            this.submitAnswerBtn.addEventListener('click', () => this.handleAnswerSubmit());
        }
        
        // 回答按钮
        if (this.answerBtns) {
            this.answerBtns.forEach(btn => {
                btn.addEventListener('click', (e) => this.handleAnswerButtonClick(e));
            });
        }
        
        // 搜索历史项点击
        if (this.searchHistoryItems) {
            this.searchHistoryItems.forEach(item => {
                item.addEventListener('click', (e) => this.handleHistoryItemClick(e));
            });
        }
        
        // 清除历史记录
        if (this.clearHistoryBtn) {
            this.clearHistoryBtn.addEventListener('click', () => this.handleClearHistory());
        }
        
        // 标签选择器
        const tagItems = document.querySelectorAll('.tag-item');
        if (tagItems) {
            tagItems.forEach(item => {
                item.addEventListener('click', (e) => this.handleTagSelection(e));
            });
        }

        // 绑定所有模态框显示事件
        this.bindModalEvents();
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        document.querySelectorAll('.modal').forEach(modalEl => {
            // 绑定模态框显示事件
            modalEl.addEventListener('shown.bs.modal', (e) => {
                // 自动聚焦第一个输入框
                const firstInput = modalEl.querySelector('input:not([type="hidden"]):not([disabled]), textarea:not([disabled])');
                if (firstInput) {
                    firstInput.focus();
                }
            });

            // 绑定模态框关闭前事件，用于表单验证
            modalEl.addEventListener('hide.bs.modal', (e) => {
                const activeForm = modalEl.querySelector('form.needs-validation');
                if (activeForm && activeForm.classList.contains('dirty')) {
                    if (!confirm('您有未保存的更改，确定要关闭吗？')) {
                        e.preventDefault();
                    }
                }
            });
        });
    }

    /**
     * 设置表单验证
     */
    setupFormValidation() {
        // 添加Bootstrap表单验证
        const forms = document.querySelectorAll('.needs-validation');
        
        forms.forEach(form => {
            // 标记表单内容变化
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    form.classList.add('dirty');
                });
            });
            
            // 提交前验证
            form.addEventListener('submit', event => {
                // 检查是否有Markdown编辑器
                const mdEditors = form.querySelectorAll('.CodeMirror');
                mdEditors.forEach(editor => {
                    const cmInstance = editor.CodeMirror;
                    if (cmInstance) {
                        const textareaId = editor.closest('.md-container').querySelector('textarea').id;
                        const textarea = document.getElementById(textareaId);
                        if (textarea) {
                            textarea.value = cmInstance.getValue();
                        }
                    }
                });
                
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // 显示表单验证错误
                    this.showValidationFeedback(form);
                }
                
                form.classList.add('was-validated');
            });
            
            // 为所有表单按钮添加验证
            const submitBtns = document.querySelectorAll(`button[form="${form.id}"], button[type="submit"]`);
            submitBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    if (!form.checkValidity()) {
                        // 显示表单验证错误
                        this.showValidationFeedback(form);
                        form.classList.add('was-validated');
                    }
                });
            });
            
            // 添加实时验证
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    if (input.checkValidity()) {
                        input.classList.remove('is-invalid');
                        input.classList.add('is-valid');
                    } else {
                        input.classList.remove('is-valid');
                        input.classList.add('is-invalid');
                    }
                });
            });
            
            // 自动保存草稿功能
            const autosaveInputs = form.querySelectorAll('[data-autosave]');
            if (autosaveInputs.length > 0) {
                // 为表单添加自动保存功能
                this.setupAutosave(form);
            }
        });
    }

    /**
     * 设置自动保存草稿功能
     * @param {HTMLFormElement} form - 表单元素
     */
    setupAutosave(form) {
        const formId = form.id || `form_${Math.random().toString(36).substr(2, 9)}`;
        if (!form.id) {
            form.id = formId;
        }
        
        // 自动保存延迟（毫秒）
        const autosaveDelay = 2000;
        let autosaveTimeout;
        
        // 自动保存事件
        const autosaveHandler = () => {
            clearTimeout(autosaveTimeout);
            
            autosaveTimeout = setTimeout(() => {
                const autosaveData = {};
                
                // 收集需要自动保存的字段数据
                form.querySelectorAll('[data-autosave]').forEach(input => {
                    const name = input.name || input.id;
                    let value = input.value;
                    
                    // 检查是否为Markdown编辑器
                    const mdContainer = input.closest('.CodeMirror');
                    if (mdContainer && mdContainer.CodeMirror) {
                        value = mdContainer.CodeMirror.getValue();
                    }
                    
                    if (name) {
                        autosaveData[name] = value;
                    }
                });
                
                if (Object.keys(autosaveData).length > 0) {
                    localStorage.setItem(`autosave_${formId}`, JSON.stringify({
                        data: autosaveData,
                        timestamp: new Date().toISOString()
                    }));
                    
                    this.showNotification('草稿已自动保存', 'info', 1000);
                }
            }, autosaveDelay);
        };
        
        // 尝试恢复自动保存的数据
        const savedData = localStorage.getItem(`autosave_${formId}`);
        if (savedData) {
            try {
                const parsedData = JSON.parse(savedData);
                const timestamp = new Date(parsedData.timestamp);
                const now = new Date();
                
                // 如果自动保存的数据不超过24小时，则恢复
                if ((now - timestamp) < 24 * 60 * 60 * 1000) {
                    const restoreData = confirm(`发现${this.getTimeAgo(timestamp)}保存的草稿，是否恢复？`);
                    
                    if (restoreData) {
                        Object.entries(parsedData.data).forEach(([name, value]) => {
                            const input = form.querySelector(`[name="${name}"], #${name}`);
                            if (input) {
                                input.value = value;
                                
                                // 如果是Markdown编辑器，需要特殊处理
                                if (input.classList.contains('markdown-editor')) {
                                    // 待编辑器初始化后设置值
                                    setTimeout(() => {
                                        const mdContainer = input.closest('.md-container');
                                        if (mdContainer && mdContainer.querySelector('.CodeMirror')) {
                                            mdContainer.querySelector('.CodeMirror').CodeMirror.setValue(value);
                                        }
                                    }, 500);
                                }
                            }
                        });
                        
                        this.showNotification('草稿已恢复', 'success');
                    } else {
                        // 如果用户拒绝恢复，则删除自动保存的数据
                        localStorage.removeItem(`autosave_${formId}`);
                    }
                } else {
                    // 如果自动保存的数据超过24小时，则删除
                    localStorage.removeItem(`autosave_${formId}`);
                }
            } catch (error) {
                console.error('恢复自动保存数据失败:', error);
                localStorage.removeItem(`autosave_${formId}`);
            }
        }
        
        // 为表单中的输入元素添加自动保存事件
        form.querySelectorAll('[data-autosave]').forEach(input => {
            input.addEventListener('input', autosaveHandler);
            
            // 对于Markdown编辑器，需要特殊处理
            if (input.classList.contains('markdown-editor')) {
                setTimeout(() => {
                    const mdContainer = input.closest('.md-container');
                    if (mdContainer && mdContainer.querySelector('.CodeMirror')) {
                        mdContainer.querySelector('.CodeMirror').CodeMirror.on('change', autosaveHandler);
                    }
                }, 500);
            }
        });
        
        // 表单提交后清除自动保存的数据
        form.addEventListener('submit', () => {
            localStorage.removeItem(`autosave_${formId}`);
        });
    }

    /**
     * 显示表单验证反馈
     * @param {HTMLFormElement} form - 表单元素
     */
    showValidationFeedback(form) {
        // 找到第一个无效的输入框
        const firstInvalid = form.querySelector(':invalid');
        if (firstInvalid) {
            // 滚动到该元素
            firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // 聚焦该元素
            firstInvalid.focus();
            
            // 显示错误提示
            const errorMessage = firstInvalid.dataset.errorMessage || '请检查输入内容';
            this.showNotification(errorMessage, 'error');
        }
    }

    /**
     * 设置搜索建议功能
     */
    setupSearchSuggestions() {
        if (!this.searchSuggestions) return;
        
        const suggestionItems = this.searchSuggestions.querySelectorAll('.search-suggestion-item');
        if (suggestionItems) {
            suggestionItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    if (this.searchInput) {
                        this.searchInput.value = e.currentTarget.textContent.trim();
                        this.searchSuggestions.style.display = 'none';
                        this.performSearch(this.searchInput.value);
                    }
                });
            });
        }
    }

    /**
     * 初始化Toast通知容器
     */
    initToastContainer() {
        // 检查是否已存在toast容器
        if (!document.getElementById('toastContainer')) {
            const toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            toastContainer.style.zIndex = '1100';
            document.body.appendChild(toastContainer);
        }
    }

    /**
     * 显示通知消息
     * @param {string} message - 通知内容
     * @param {string} type - 通知类型 (success, error, warning, info)
     * @param {number} duration - 显示时长，默认3000毫秒
     */
    showNotification(message, type = 'success', duration = 3000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;
        
        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : 
                        type === 'error' ? 'bg-danger' :
                        type === 'warning' ? 'bg-warning text-dark' : 'bg-info text-dark';
        
        const iconClass = type === 'success' ? 'fas fa-check-circle' : 
                         type === 'error' ? 'fas fa-exclamation-circle' :
                         type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
        
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="${iconClass} me-2"></i> ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: duration });
        toast.show();
        
        // 自动移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    /**
     * 处理搜索框获得焦点
     */
    handleSearchFocus() {
        if (this.searchSuggestions) {
            this.searchSuggestions.style.display = 'block';
        }
    }

    /**
     * 处理搜索框失去焦点
     */
    handleSearchBlur() {
        if (this.searchSuggestions) {
            // 延迟隐藏，以便可以点击建议
            setTimeout(() => {
                this.searchSuggestions.style.display = 'none';
            }, 200);
        }
    }

    /**
     * 处理搜索框输入
     * @param {Event} e - 输入事件
     */
    handleSearchInput(e) {
        const query = e.target.value.trim();
        
        // 如果启用了实时搜索建议，这里可以调用API获取建议
        if (query.length >= 2 && this.searchSuggestions) {
            // 这里可以调用API获取搜索建议
            // 示例仅显示已有的建议
            this.searchSuggestions.style.display = 'block';
            
            // 实时更新搜索建议
            this.updateSearchSuggestions(query);
        } else if (this.searchSuggestions) {
            this.searchSuggestions.style.display = 'none';
        }
    }

    /**
     * 更新搜索建议
     * @param {string} query - 搜索关键词
     */
    updateSearchSuggestions(query) {
        if (!this.searchSuggestions) return;
        
        // 实际应用中，这里会从API获取搜索建议
        // 这里仅作简单过滤示例
        const items = this.searchSuggestions.querySelectorAll('.search-suggestion-item');
        let hasVisibleItem = false;
        
        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(query.toLowerCase())) {
                item.style.display = 'block';
                hasVisibleItem = true;
                
                // 高亮匹配部分
                const regex = new RegExp(`(${query})`, 'gi');
                const textContent = item.textContent;
                item.innerHTML = textContent.replace(regex, '<strong>$1</strong>');
            } else {
                item.style.display = 'none';
            }
        });
        
        // 如果没有匹配项，添加一个搜索选项
        if (!hasVisibleItem) {
            // 检查是否已经有搜索选项
            const searchItem = this.searchSuggestions.querySelector('.search-direct-item');
            if (searchItem) {
                searchItem.innerHTML = `<i class="fas fa-search me-2"></i>搜索 "<strong>${query}</strong>"`;
                searchItem.style.display = 'block';
            } else {
                // 创建新的搜索选项
                const newItem = document.createElement('div');
                newItem.className = 'search-suggestion-item search-direct-item';
                newItem.innerHTML = `<i class="fas fa-search me-2"></i>搜索 "<strong>${query}</strong>"`;
                
                newItem.addEventListener('click', () => {
                    if (this.searchInput) {
                        this.searchInput.value = query;
                        this.searchSuggestions.style.display = 'none';
                        this.performSearch(query);
                    }
                });
                
                this.searchSuggestions.appendChild(newItem);
            }
        }
    }

    /**
     * 处理搜索框按键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleSearchKeypress(e) {
        if (e.key === 'Enter') {
            const query = e.target.value.trim();
            if (query) {
                this.performSearch(query);
            }
        }
    }

    /**
     * 执行搜索
     * @param {string} query - 搜索关键词
     */
    performSearch(query) {
        // 保存到搜索历史
        this.saveSearchHistory(query);
        
        // 显示搜索中状态
        this.showNotification('正在搜索: ' + query, 'info');
        
        // 实际应用中，这里会发送请求到后端
        // 示例中简单重定向到搜索页面
        if (window.location.pathname.includes('search.html')) {
            // 如果已经在搜索页面，触发搜索逻辑
            const searchStats = document.getElementById('searchStats');
            if (searchStats) {
                searchStats.textContent = '搜索中...';
                
                // 模拟搜索延迟
                setTimeout(() => {
                    searchStats.textContent = `找到约 ${Math.floor(Math.random() * 200) + 10} 条结果 (${(Math.random() * 0.5 + 0.2).toFixed(2)} 秒)`;
                    this.showNotification('搜索完成', 'success');
                }, 800);
            }
        } else {
            // 跳转到搜索页面
            window.location.href = 'search.html?q=' + encodeURIComponent(query);
        }
    }

    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
        const historyContainer = document.querySelector('.search-history');
        if (!historyContainer) return;
        
        // 获取搜索历史
        const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        
        // 清空现有历史项
        const historyItems = historyContainer.querySelectorAll('.search-history-item');
        historyItems.forEach(item => item.remove());
        
        // 移除空历史提示
        const emptyMessage = historyContainer.querySelector('.text-muted');
        if (emptyMessage) emptyMessage.remove();
        
        // 添加历史项
        if (searchHistory.length > 0) {
            const container = document.createElement('div');
            container.className = 'search-history-items';
            
            searchHistory.forEach(item => {
                const timeAgo = this.getTimeAgo(new Date(item.time));
                
                const historyItem = document.createElement('div');
                historyItem.className = 'search-history-item';
                historyItem.innerHTML = `
                    <i class="fas fa-history text-muted"></i>
                    <span class="history-text">${item.text}</span>
                    <span class="history-time">${timeAgo}</span>
                `;
                
                // 添加点击事件
                historyItem.addEventListener('click', () => {
                    if (this.searchInput) {
                        this.searchInput.value = item.text;
                    }
                    
                    // 隐藏历史记录
                    const searchHistory = document.getElementById('searchHistory');
                    const bsCollapse = bootstrap.Collapse.getInstance(searchHistory);
                    if (bsCollapse) bsCollapse.hide();
                    
                    // 执行搜索
                    this.performSearch(item.text);
                });
                
                container.appendChild(historyItem);
            });
            
            // 找到清除历史按钮，并在其前面插入历史项
            const clearBtn = historyContainer.querySelector('button').parentNode;
            historyContainer.insertBefore(container, clearBtn);
            
            // 更新搜索历史项引用
            this.searchHistoryItems = historyContainer.querySelectorAll('.search-history-item');
        } else {
            // 添加空历史提示
            const emptyMessage = document.createElement('p');
            emptyMessage.className = 'text-center text-muted my-3';
            emptyMessage.textContent = '暂无搜索历史';
            
            // 找到清除历史按钮，并在其前面插入提示
            const clearBtn = historyContainer.querySelector('button').parentNode;
            historyContainer.insertBefore(emptyMessage, clearBtn);
        }
    }

    /**
     * 获取相对时间描述
     * @param {Date} date - 日期对象
     * @returns {string} - 相对时间描述
     */
    getTimeAgo(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffSecs = Math.floor(diffMs / 1000);
        const diffMins = Math.floor(diffSecs / 60);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffDays > 30) {
            return date.toLocaleDateString();
        } else if (diffDays >= 1) {
            return `${diffDays}天前`;
        } else if (diffHours >= 1) {
            return `${diffHours}小时前`;
        } else if (diffMins >= 1) {
            return `${diffMins}分钟前`;
        } else {
            return '刚刚';
        }
    }

    /**
     * 保存搜索历史
     * @param {string} query - 搜索关键词
     */
    saveSearchHistory(query) {
        // 获取当前搜索历史
        let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        
        // 如果已存在，先移除
        searchHistory = searchHistory.filter(item => item.text !== query);
        
        // 添加到历史开头
        searchHistory.unshift({
            text: query,
            time: new Date().toISOString()
        });
        
        // 保持最多10条记录
        if (searchHistory.length > 10) {
            searchHistory = searchHistory.slice(0, 10);
        }
        
        // 保存回本地存储
        localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
        
        // 更新搜索历史显示
        this.loadSearchHistory();
    }

    /**
     * 处理语音搜索
     */
    handleVoiceSearch() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            this.showNotification('正在聆听...', 'info');
            
            // 创建语音识别对象
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();
            
            // 设置参数
            recognition.lang = 'zh-CN';
            recognition.continuous = false;
            recognition.interimResults = false;
            
            // 添加语音识别回调
            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                if (this.searchInput) {
                    this.searchInput.value = transcript;
                }
                this.showNotification('识别结果: ' + transcript, 'success');
                this.performSearch(transcript);
            };
            
            recognition.onerror = (event) => {
                this.showNotification('语音识别错误: ' + event.error, 'error');
            };
            
            // 开始识别
            recognition.start();
        } else {
            this.showNotification('您的浏览器不支持语音识别功能', 'error');
        }
    }

    /**
     * 处理问题提交
     */
    handleQuestionSubmit() {
        const titleInput = document.getElementById('questionTitle');
        const contentInput = document.getElementById('questionContent');
        
        if (!titleInput || !contentInput) return;
        
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();
        
        if (!title) {
            this.showNotification('请输入问题标题', 'warning');
            titleInput.focus();
            return;
        }
        
        if (!content) {
            this.showNotification('请输入问题详情', 'warning');
            contentInput.focus();
            return;
        }
        
        // 收集选中的标签
        const selectedTags = [];
        document.querySelectorAll('.tag-item.selected').forEach(tag => {
            selectedTags.push(tag.textContent.trim());
        });
        
        // 处理文件上传
        const fileInput = document.getElementById('fileUpload');
        const hasFiles = fileInput && fileInput.files.length > 0;
        
        // 收集表单数据
        const formData = new FormData();
        formData.append('title', title);
        formData.append('content', content);
        selectedTags.forEach(tag => {
            formData.append('tags[]', tag);
        });
        
        // 添加文件
        if (hasFiles) {
            for (let i = 0; i < fileInput.files.length; i++) {
                formData.append('files[]', fileInput.files[i]);
            }
        }
        
        // 添加隐私选项
        const isPrivate = document.getElementById('questionPrivate')?.checked || false;
        formData.append('isPrivate', isPrivate);
        
        // 实际应用中，这里会使用fetch或axios发送请求到后端
        // 示例中仅显示通知并模拟成功
        
        // 显示提交中状态
        this.showNotification('正在提交问题...', 'info');
        
        // 模拟提交延迟
        setTimeout(() => {
            // 关闭模态框
            const quickAskModal = bootstrap.Modal.getInstance(document.getElementById('quickAskModal'));
            if (quickAskModal) quickAskModal.hide();
            
            // 显示成功通知
            this.showNotification('问题提交成功！', 'success');
            
            // 重置表单
            if (titleInput) titleInput.value = '';
            if (contentInput) contentInput.value = '';
            if (fileInput) fileInput.value = '';
            document.querySelectorAll('.tag-item.selected').forEach(tag => {
                tag.classList.remove('selected');
            });
            
            // 重置文件上传预览
            const previewContainer = fileInput?.nextSibling;
            if (previewContainer && previewContainer.classList.contains('file-preview-container')) {
                previewContainer.innerHTML = '';
            }
            
            // 清除自动保存的草稿
            const form = titleInput.closest('form');
            if (form && form.id) {
                localStorage.removeItem(`autosave_${form.id}`);
            }
            
            // 延迟后重定向到问题页面
            // 这里使用模拟的问题ID
            setTimeout(() => {
                const questionId = Math.floor(Math.random() * 10000) + 1;
                window.location.href = `qa.html?id=${questionId}`;
            }, 1500);
        }, 800);
    }

    /**
     * 处理反馈提交
     */
    handleFeedbackSubmit() {
        const typeSelect = document.getElementById('feedbackType');
        const contentInput = document.getElementById('feedbackContent');
        
        if (!typeSelect || !contentInput) return;
        
        const type = typeSelect.value;
        const content = contentInput.value.trim();
        
        if (type === '请选择反馈类型') {
            this.showNotification('请选择反馈类型', 'warning');
            typeSelect.focus();
            return;
        }
        
        if (!content) {
            this.showNotification('请输入反馈内容', 'warning');
            contentInput.focus();
            return;
        }
        
        // 获取联系许可
        const contactConsent = document.getElementById('contactConsent');
        const allowContact = contactConsent && contactConsent.checked;
        
        // 实际应用中，这里会提交表单到后端
        // 示例中仅显示通知
        
        // 关闭模态框
        const quickFeedbackModal = bootstrap.Modal.getInstance(document.getElementById('quickFeedbackModal'));
        if (quickFeedbackModal) quickFeedbackModal.hide();
        
        // 显示成功通知
        this.showNotification('反馈提交成功！', 'success');
        
        // 重置表单
        if (typeSelect) typeSelect.selectedIndex = 0;
        if (contentInput) contentInput.value = '';
        if (contactConsent) contactConsent.checked = false;
    }

    /**
     * 处理回答提交
     */
    handleAnswerSubmit() {
        const contentInput = document.getElementById('answerContent');
        
        if (!contentInput) return;
        
        const content = contentInput.value.trim();
        
        if (!content) {
            this.showNotification('请输入回答内容', 'warning');
            contentInput.focus();
            return;
        }
        
        // 处理文件上传
        const fileInput = document.getElementById('answerAttachment');
        const hasFiles = fileInput && fileInput.files.length > 0;
        
        // 获取通知设置
        const notifyMe = document.getElementById('notifyMe');
        const wantNotifications = notifyMe && notifyMe.checked;
        
        // 实际应用中，这里会提交表单到后端
        // 示例中仅显示通知
        
        // 关闭模态框
        const quickAnswerModal = bootstrap.Modal.getInstance(document.getElementById('quickAnswerModal'));
        if (quickAnswerModal) quickAnswerModal.hide();
        
        // 显示成功通知
        this.showNotification('回答提交成功！', 'success');
        
        // 重置表单
        if (contentInput) contentInput.value = '';
        if (fileInput) fileInput.value = '';
        if (notifyMe) notifyMe.checked = false;
    }

    /**
     * 处理回答按钮点击
     * @param {Event} e - 点击事件
     */
    handleAnswerButtonClick(e) {
        const questionId = e.currentTarget.getAttribute('data-question-id');
        const questionTitle = e.currentTarget.getAttribute('data-question-title');
        
        const modalLabel = document.getElementById('quickAnswerModalLabel');
        if (modalLabel) {
            modalLabel.innerHTML = `<i class="fas fa-reply me-2"></i>回答问题: ${questionTitle || ''}`;
        }
        
        // 可以在这里添加更多逻辑，例如预填充一些内容
    }

    /**
     * 处理历史记录项点击
     * @param {Event} e - 点击事件
     */
    handleHistoryItemClick(e) {
        const historyText = e.currentTarget.querySelector('.history-text').textContent;
        
        if (this.searchInput) {
            this.searchInput.value = historyText;
        }
        
        // 隐藏历史记录
        const searchHistory = document.getElementById('searchHistory');
        const bsCollapse = bootstrap.Collapse.getInstance(searchHistory);
        if (bsCollapse) bsCollapse.hide();
        
        // 执行搜索
        this.performSearch(historyText);
    }

    /**
     * 处理清除历史记录
     */
    handleClearHistory() {
        if (confirm('确定要清除所有搜索历史吗？')) {
            localStorage.removeItem('searchHistory');
            
            // 更新搜索历史显示
            this.loadSearchHistory();
            
            this.showNotification('搜索历史已清除', 'success');
        }
    }

    /**
     * 处理标签选择
     * @param {Event} e - 点击事件
     */
    handleTagSelection(e) {
        e.currentTarget.classList.toggle('selected');
    }
    
    /**
     * 显示确认对话框
     * @param {string} message - 消息内容
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     */
    showConfirmDialog(message, onConfirm, onCancel = null) {
        // 检查是否已存在确认对话框
        let confirmModal = document.getElementById('confirmDialogModal');
        
        if (!confirmModal) {
            // 创建确认对话框
            const modalHtml = `
                <div class="modal fade" id="confirmDialogModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-question-circle me-2"></i>确认操作</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p id="confirmDialogMessage"></p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="confirmDialogCancel">取消</button>
                                <button type="button" class="btn btn-primary" id="confirmDialogConfirm">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            confirmModal = document.getElementById('confirmDialogModal');
        }
        
        // 设置消息内容
        const messageEl = document.getElementById('confirmDialogMessage');
        if (messageEl) {
            messageEl.textContent = message;
        }
        
        // 获取按钮
        const confirmBtn = document.getElementById('confirmDialogConfirm');
        const cancelBtn = document.getElementById('confirmDialogCancel');
        
        // 移除旧的事件监听器
        const newConfirmBtn = confirmBtn.cloneNode(true);
        const newCancelBtn = cancelBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
        
        // 添加新的事件监听器
        newConfirmBtn.addEventListener('click', () => {
            const bsModal = bootstrap.Modal.getInstance(confirmModal);
            bsModal.hide();
            if (typeof onConfirm === 'function') {
                onConfirm();
            }
        });
        
        newCancelBtn.addEventListener('click', () => {
            if (typeof onCancel === 'function') {
                onCancel();
            }
        });
        
        // 显示对话框
        const bsModal = new bootstrap.Modal(confirmModal);
        bsModal.show();
    }

    /**
     * 初始化文件上传预览组件
     */
    initFilePreviewComponents() {
        const fileInputs = document.querySelectorAll('input[type="file"]');
        
        fileInputs.forEach(input => {
            const previewContainer = document.createElement('div');
            previewContainer.className = 'file-preview-container mt-2';
            input.parentNode.insertBefore(previewContainer, input.nextSibling);
            
            input.addEventListener('change', () => {
                previewContainer.innerHTML = '';
                
                if (input.files.length === 0) return;
                
                const fileList = document.createElement('div');
                fileList.className = 'file-preview-list';
                
                for (let i = 0; i < input.files.length; i++) {
                    const file = input.files[i];
                    const filePreview = document.createElement('div');
                    filePreview.className = 'file-preview-item';
                    
                    if (file.type.startsWith('image/')) {
                        const img = document.createElement('img');
                        img.className = 'file-preview-image';
                        img.src = URL.createObjectURL(file);
                        img.onload = () => URL.revokeObjectURL(img.src);
                        filePreview.appendChild(img);
                    } else {
                        const icon = document.createElement('i');
                        icon.className = this.getFileIconClass(file.name);
                        filePreview.appendChild(icon);
                    }
                    
                    const fileName = document.createElement('span');
                    fileName.className = 'file-name';
                    fileName.textContent = file.name;
                    filePreview.appendChild(fileName);
                    
                    const fileSize = document.createElement('span');
                    fileSize.className = 'file-size';
                    fileSize.textContent = this.formatFileSize(file.size);
                    filePreview.appendChild(fileSize);
                    
                    const removeBtn = document.createElement('button');
                    removeBtn.type = 'button';
                    removeBtn.className = 'btn btn-sm btn-outline-danger remove-file';
                    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                    removeBtn.addEventListener('click', () => {
                        // 由于无法直接修改FileList，我们需要重置input
                        input.value = '';
                        previewContainer.innerHTML = '';
                    });
                    filePreview.appendChild(removeBtn);
                    
                    fileList.appendChild(filePreview);
                }
                
                previewContainer.appendChild(fileList);
            });
        });
    }
    
    /**
     * 获取文件图标类
     * @param {string} fileName - 文件名
     * @returns {string} - Font Awesome图标类名
     */
    getFileIconClass(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        
        const iconMap = {
            pdf: 'far fa-file-pdf text-danger',
            doc: 'far fa-file-word text-primary',
            docx: 'far fa-file-word text-primary',
            xls: 'far fa-file-excel text-success',
            xlsx: 'far fa-file-excel text-success',
            ppt: 'far fa-file-powerpoint text-warning',
            pptx: 'far fa-file-powerpoint text-warning',
            zip: 'far fa-file-archive text-secondary',
            rar: 'far fa-file-archive text-secondary',
            txt: 'far fa-file-alt text-info',
            jpg: 'far fa-file-image text-primary',
            jpeg: 'far fa-file-image text-primary',
            png: 'far fa-file-image text-primary',
            gif: 'far fa-file-image text-primary'
        };
        
        return iconMap[extension] || 'far fa-file text-secondary';
    }
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 文件字节数
     * @returns {string} - 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 设置Markdown编辑器
     */
    setupMarkdownEditors() {
        // 查找所有带有markdown-editor类的textarea
        const editors = document.querySelectorAll('textarea.markdown-editor');
        
        if (editors.length === 0) return;
        
        // 检查是否已加载SimpleMDE
        if (typeof SimpleMDE === 'undefined') {
            // 如果没有加载SimpleMDE，则创建加载脚本
            const loadScript = (src) => {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = src;
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            };
            
            const loadCss = (href) => {
                return new Promise((resolve, reject) => {
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = href;
                    link.onload = resolve;
                    link.onerror = reject;
                    document.head.appendChild(link);
                });
            };
            
            // 加载SimpleMDE的CSS和JS
            Promise.all([
                loadCss('https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css'),
                loadScript('https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js')
            ])
            .then(() => {
                this.initMarkdownEditors(editors);
            })
            .catch(error => {
                console.error('Failed to load SimpleMDE:', error);
            });
        } else {
            // 如果已经加载了SimpleMDE，直接初始化编辑器
            this.initMarkdownEditors(editors);
        }
    }
    
    /**
     * 初始化Markdown编辑器
     * @param {NodeList} editors - 编辑器元素列表
     */
    initMarkdownEditors(editors) {
        editors.forEach(editor => {
            new SimpleMDE({
                element: editor,
                spellChecker: false,
                placeholder: editor.placeholder || '请输入内容，支持Markdown格式...',
                toolbar: [
                    'bold', 'italic', 'heading', '|',
                    'code', 'quote', 'unordered-list', 'ordered-list', '|',
                    'link', 'image', 'table', '|',
                    'preview', 'guide'
                ],
                status: ['lines', 'words', 'cursor'],
                renderingConfig: {
                    singleLineBreaks: false,
                    codeSyntaxHighlighting: true
                }
            });
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.quickInteraction = new QuickInteraction();
}); 