package com.materialsstudio.qa.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用响应结果
 *
 * @param <T> 数据类型
 */
@Data
public class Result<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 数据
     */
    private T data;
    
    /**
     * 成功结果
     *
     * @param <T> 数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> success() {
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setCode(ResultCode.SUCCESS);
        result.setMessage("操作成功");
        return result;
    }
    
    /**
     * 成功结果
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setCode(ResultCode.SUCCESS);
        result.setMessage("操作成功");
        result.setData(data);
        return result;
    }
    
    /**
     * 成功结果
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setCode(ResultCode.SUCCESS);
        result.setMessage(message);
        result.setData(data);
        return result;
    }
    
    /**
     * 失败结果
     *
     * @param <T> 数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> failure() {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.FAILURE);
        result.setMessage("操作失败");
        return result;
    }
    
    /**
     * 失败结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> failure(String message) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.FAILURE);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 失败结果
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> failure(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 未授权结果
     *
     * @param <T> 数据类型
     * @return 未授权的响应结果
     */
    public static <T> Result<T> unauthorized() {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.UNAUTHORIZED);
        result.setMessage("未授权，请先登录");
        return result;
    }
    
    /**
     * 禁止访问结果
     *
     * @param <T> 数据类型
     * @return 禁止访问的响应结果
     */
    public static <T> Result<T> forbidden() {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.FORBIDDEN);
        result.setMessage("权限不足，禁止访问");
        return result;
    }
    
    /**
     * 参数错误结果
     *
     * @param <T> 数据类型
     * @return 参数错误的响应结果
     */
    public static <T> Result<T> badRequest() {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.BAD_REQUEST);
        result.setMessage("参数错误");
        return result;
    }
    
    /**
     * 参数错误结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 参数错误的响应结果
     */
    public static <T> Result<T> badRequest(String message) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.BAD_REQUEST);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 资源不存在结果
     *
     * @param <T> 数据类型
     * @return 资源不存在的响应结果
     */
    public static <T> Result<T> notFound() {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.NOT_FOUND);
        result.setMessage("资源不存在");
        return result;
    }
    
    /**
     * 资源不存在结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 资源不存在的响应结果
     */
    public static <T> Result<T> notFound(String message) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.NOT_FOUND);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 服务器错误结果
     *
     * @param <T> 数据类型
     * @return 服务器错误的响应结果
     */
    public static <T> Result<T> error() {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.ERROR);
        result.setMessage("服务器错误");
        return result;
    }
    
    /**
     * 服务器错误结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 服务器错误的响应结果
     */
    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCode.ERROR);
        result.setMessage(message);
        return result;
    }
} 