require('dotenv').config();

const config = {
  app: {
    name: process.env.APP_NAME || 'Materials Studio Platform',
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT) || 8000,
    url: process.env.APP_URL || 'http://localhost:8000'
  },

  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    name: process.env.DB_NAME || 'materials_studio_platform',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    charset: 'utf8mb4'
  },

  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '2h',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },

  mail: {
    host: process.env.MAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.MAIL_PORT) || 587,
    secure: false,
    auth: {
      user: process.env.MAIL_USERNAME,
      pass: process.env.MAIL_PASSWORD
    },
    from: {
      address: process.env.MAIL_FROM_ADDRESS || '<EMAIL>',
      name: process.env.MAIL_FROM_NAME || 'Materials Studio Platform'
    }
  },

  sms: {
    accessKeyId: process.env.SMS_ACCESS_KEY_ID,
    accessKeySecret: process.env.SMS_ACCESS_KEY_SECRET,
    signName: process.env.SMS_SIGN_NAME || 'Materials Studio Platform',
    templateCode: process.env.SMS_TEMPLATE_CODE
  },

  upload: {
    path: process.env.UPLOAD_PATH || 'uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar,7z,txt,md').split(',')
  },

  oss: {
    region: process.env.OSS_REGION || 'oss-cn-hangzhou',
    accessKeyId: process.env.OSS_ACCESS_KEY_ID,
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
    bucket: process.env.OSS_BUCKET || 'materials-studio-platform',
    endpoint: process.env.OSS_ENDPOINT
  },

  elasticsearch: {
    host: process.env.ES_HOST || 'localhost',
    port: parseInt(process.env.ES_PORT) || 9200,
    indexPrefix: process.env.ES_INDEX_PREFIX || 'ms_platform'
  },

  payment: {
    alipay: {
      appId: process.env.ALIPAY_APP_ID,
      privateKey: process.env.ALIPAY_PRIVATE_KEY,
      publicKey: process.env.ALIPAY_PUBLIC_KEY,
      gateway: process.env.ALIPAY_GATEWAY || 'https://openapi.alipay.com/gateway.do'
    },
    wechat: {
      appId: process.env.WECHAT_APP_ID,
      mchId: process.env.WECHAT_MCH_ID,
      apiKey: process.env.WECHAT_API_KEY,
      certPath: process.env.WECHAT_CERT_PATH,
      keyPath: process.env.WECHAT_KEY_PATH
    }
  },

  logging: {
    level: process.env.LOG_LEVEL || 'info',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d'
  },

  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    slowDownWindowMs: parseInt(process.env.SLOW_DOWN_WINDOW_MS) || 15 * 60 * 1000,
    slowDownDelayAfter: parseInt(process.env.SLOW_DOWN_DELAY_AFTER) || 50,
    slowDownDelayMs: parseInt(process.env.SLOW_DOWN_DELAY_MS) || 500
  },

  cache: {
    ttl: parseInt(process.env.CACHE_TTL) || 3600, // 1小时
    prefix: process.env.CACHE_PREFIX || 'ms_platform'
  },

  session: {
    secret: process.env.SESSION_SECRET || 'your-session-secret',
    maxAge: parseInt(process.env.SESSION_MAX_AGE) || 24 * 60 * 60 * 1000 // 24小时
  },

  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: process.env.CORS_CREDENTIALS === 'true'
  },

  monitoring: {
    enabled: process.env.ENABLE_METRICS === 'true',
    port: parseInt(process.env.METRICS_PORT) || 9090
  }
};

// 验证必需的配置
const requiredConfigs = [
  'JWT_SECRET',
  'DB_NAME'
];

if (config.app.env === 'production') {
  requiredConfigs.push(
    'DB_PASSWORD',
    'REDIS_PASSWORD',
    'JWT_REFRESH_SECRET',
    'SESSION_SECRET'
  );
}

const missingConfigs = requiredConfigs.filter(key => !process.env[key]);
if (missingConfigs.length > 0) {
  console.error(`缺少必需的环境变量: ${missingConfigs.join(', ')}`);
  if (config.app.env === 'production') {
    process.exit(1);
  }
}

module.exports = config;
