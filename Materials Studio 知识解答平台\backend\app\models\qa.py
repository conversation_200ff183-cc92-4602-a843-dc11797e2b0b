from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String, Table, Text
from sqlalchemy.orm import relationship

from app.db.session import Base


# 问题与标签的多对多关联表
question_tag = Table(
    "question_tag",
    Base.metadata,
    Column("question_id", Integer, ForeignKey("questions.id", ondelete="CASCADE")),
    Column("tag_id", Integer, ForeignKey("tags.id", ondelete="CASCADE")),
)


class Question(Base):
    """问题模型"""
    __tablename__ = "questions"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    author_id = Column(Integer, ForeignKey("users.id"))
    is_solved = Column(Boolean, default=False)
    is_premium = Column(Boolean, default=False)  # 是否为会员专属内容
    view_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    author = relationship("User", back_populates="questions")
    answers = relationship("Answer", back_populates="question", cascade="all, delete-orphan")
    attachments = relationship("QuestionAttachment", back_populates="question", cascade="all, delete-orphan")
    tags = relationship("Tag", secondary=question_tag, back_populates="questions")
    favorites = relationship("Favorite", back_populates="question")


class Answer(Base):
    """回答模型"""
    __tablename__ = "answers"
    
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    question_id = Column(Integer, ForeignKey("questions.id", ondelete="CASCADE"))
    author_id = Column(Integer, ForeignKey("users.id"))
    is_accepted = Column(Boolean, default=False)
    vote_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    question = relationship("Question", back_populates="answers")
    author = relationship("User", back_populates="answers")
    comments = relationship("Comment", back_populates="answer", cascade="all, delete-orphan")
    attachments = relationship("AnswerAttachment", back_populates="answer", cascade="all, delete-orphan")


class Comment(Base):
    """评论模型"""
    __tablename__ = "comments"
    
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    answer_id = Column(Integer, ForeignKey("answers.id", ondelete="CASCADE"))
    author_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    answer = relationship("Answer", back_populates="comments")
    author = relationship("User", back_populates="comments")


class Tag(Base):
    """标签模型"""
    __tablename__ = "tags"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True)
    color = Column(String(20), default="#6c757d")
    description = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    questions = relationship("Question", secondary=question_tag, back_populates="tags")


class QuestionAttachment(Base):
    """问题附件模型"""
    __tablename__ = "question_attachments"
    
    id = Column(Integer, primary_key=True, index=True)
    question_id = Column(Integer, ForeignKey("questions.id", ondelete="CASCADE"))
    filename = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_size = Column(Integer)
    file_type = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    question = relationship("Question", back_populates="attachments")


class AnswerAttachment(Base):
    """回答附件模型"""
    __tablename__ = "answer_attachments"
    
    id = Column(Integer, primary_key=True, index=True)
    answer_id = Column(Integer, ForeignKey("answers.id", ondelete="CASCADE"))
    filename = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_size = Column(Integer)
    file_type = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    answer = relationship("Answer", back_populates="attachments") 