package com.materialsstudio.qa.controller;

import com.materialsstudio.qa.common.Result;
import com.materialsstudio.qa.model.dto.AnswerPostDTO;
import com.materialsstudio.qa.model.vo.AnswerVO;
import com.materialsstudio.qa.service.AnswerService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 回答控制器
 */
@RestController
@RequestMapping("/answers")
public class AnswerController {
    
    @Resource
    private AnswerService answerService;
    
    /**
     * 根据问题ID获取回答列表
     *
     * @param questionId 问题ID
     * @return 回答列表
     */
    @GetMapping("/question/{questionId}")
    public Result<List<AnswerVO>> getAnswersByQuestionId(@PathVariable Long questionId) {
        try {
            List<AnswerVO> answers = answerService.getAnswersByQuestionId(questionId);
            return Result.success(answers);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 发布回答
     *
     * @param answerPostDTO 回答发布信息
     * @return 回答ID
     */
    @PostMapping
    public Result<Long> postAnswer(@Validated @RequestBody AnswerPostDTO answerPostDTO) {
        try {
            Long id = answerService.postAnswer(answerPostDTO);
            return Result.success(id);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 采纳回答
     *
     * @param id 回答ID
     * @return 是否成功
     */
    @PutMapping("/{id}/accept")
    public Result<Boolean> acceptAnswer(@PathVariable Long id) {
        try {
            boolean result = answerService.acceptAnswer(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
    
    /**
     * 点赞回答
     *
     * @param id 回答ID
     * @return 是否成功
     */
    @PutMapping("/{id}/vote")
    public Result<Boolean> voteAnswer(@PathVariable Long id) {
        try {
            boolean result = answerService.voteAnswer(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure(e.getMessage());
        }
    }
} 