# 应用配置
NODE_ENV=development
PORT=8000
APP_NAME=Materials Studio Platform
APP_URL=http://localhost:8000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=materials_studio_platform
DB_USER=root
DB_PASSWORD=root123456
DB_CONNECTION_LIMIT=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=2h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_REFRESH_EXPIRES_IN=7d

# 邮件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=Materials Studio Platform

# 短信配置
SMS_ACCESS_KEY_ID=your-aliyun-access-key-id
SMS_ACCESS_KEY_SECRET=your-aliyun-access-key-secret
SMS_SIGN_NAME=Materials Studio Platform
SMS_TEMPLATE_CODE=SMS_123456789

# 文件上传配置
UPLOAD_PATH=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar,7z,txt,md

# 阿里云OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your-oss-access-key-id
OSS_ACCESS_KEY_SECRET=your-oss-access-key-secret
OSS_BUCKET=materials-studio-platform
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com

# Elasticsearch配置
ES_HOST=localhost
ES_PORT=9200
ES_INDEX_PREFIX=ms_platform

# 支付配置
# 支付宝配置
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key
ALIPAY_GATEWAY=https://openapi.alipay.com/gateway.do

# 微信支付配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_MCH_ID=your-wechat-mch-id
WECHAT_API_KEY=your-wechat-api-key
WECHAT_CERT_PATH=certs/wechat/apiclient_cert.pem
WECHAT_KEY_PATH=certs/wechat/apiclient_key.pem

# 日志配置
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SLOW_DOWN_WINDOW_MS=900000
SLOW_DOWN_DELAY_AFTER=50
SLOW_DOWN_DELAY_MS=500

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=ms_platform

# 会话配置
SESSION_SECRET=your-session-secret-change-in-production
SESSION_MAX_AGE=86400000

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
