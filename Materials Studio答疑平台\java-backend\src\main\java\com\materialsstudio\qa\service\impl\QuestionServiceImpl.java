package com.materialsstudio.qa.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.materialsstudio.qa.dao.QuestionDao;
import com.materialsstudio.qa.dao.QuestionTagDao;
import com.materialsstudio.qa.dao.TagDao;
import com.materialsstudio.qa.dao.UserDao;
import com.materialsstudio.qa.entity.Question;
import com.materialsstudio.qa.entity.QuestionTag;
import com.materialsstudio.qa.entity.User;
import com.materialsstudio.qa.model.dto.QuestionPostDTO;
import com.materialsstudio.qa.model.dto.QuestionQueryDTO;
import com.materialsstudio.qa.model.vo.AnswerVO;
import com.materialsstudio.qa.model.vo.PageVO;
import com.materialsstudio.qa.model.vo.QuestionDetailVO;
import com.materialsstudio.qa.model.vo.QuestionVO;
import com.materialsstudio.qa.service.AnswerService;
import com.materialsstudio.qa.service.QuestionService;
import com.materialsstudio.qa.service.TagService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问题服务实现类
 */
@Service
public class QuestionServiceImpl implements QuestionService {
    
    @Resource
    private QuestionDao questionDao;
    
    @Resource
    private TagDao tagDao;
    
    @Resource
    private QuestionTagDao questionTagDao;
    
    @Resource
    private UserDao userDao;
    
    @Resource
    private AnswerService answerService;
    
    @Resource
    private TagService tagService;
    
    @Override
    public PageVO<QuestionVO> pageQuestions(QuestionQueryDTO queryDTO) {
        // 创建分页对象
        Page<QuestionVO> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        
        // 查询分页数据
        page = (Page<QuestionVO>) questionDao.pageQuestions(
                page,
                queryDTO.getKeyword(),
                queryDTO.getTagId(),
                queryDTO.getAuthorId(),
                queryDTO.getIsPremium(),
                queryDTO.getStatus(),
                queryDTO.getSortField(),
                queryDTO.getSortOrder()
        );
        
        // 查询每个问题的标签
        for (QuestionVO question : page.getRecords()) {
            question.setTags(tagService.getTagsByQuestionId(question.getId()));
            // 将Integer转换为Boolean
            question.setIsPremium(question.getIsPremium() != null && question.getIsPremium() == 1);
        }
        
        // 创建返回结果
        PageVO<QuestionVO> pageVO = new PageVO<>();
        pageVO.setTotal(page.getTotal());
        pageVO.setPages(page.getPages());
        pageVO.setCurrent(page.getCurrent());
        pageVO.setSize(page.getSize());
        pageVO.setRecords(page.getRecords());
        pageVO.setHasPrevious(page.hasPrevious());
        pageVO.setHasNext(page.hasNext());
        
        return pageVO;
    }
    
    @Override
    public QuestionDetailVO getQuestionDetail(Long id) {
        // 查询问题详情
        QuestionVO questionVO = questionDao.getQuestionDetail(id);
        if (questionVO == null) {
            return null;
        }
        
        // 将Integer转换为Boolean
        questionVO.setIsPremium(questionVO.getIsPremium() != null && questionVO.getIsPremium() == 1);
        
        // 创建详情对象
        QuestionDetailVO detailVO = new QuestionDetailVO();
        
        // 复制问题基本信息
        detailVO.setId(questionVO.getId());
        detailVO.setTitle(questionVO.getTitle());
        detailVO.setContent(questionVO.getContent());
        detailVO.setAuthorId(questionVO.getAuthorId());
        detailVO.setAuthorName(questionVO.getAuthorName());
        detailVO.setAuthorAvatar(questionVO.getAuthorAvatar());
        detailVO.setIsPremium(questionVO.getIsPremium());
        detailVO.setViewCount(questionVO.getViewCount());
        detailVO.setAnswerCount(questionVO.getAnswerCount());
        detailVO.setStatus(questionVO.getStatus());
        detailVO.setCreateTime(questionVO.getCreateTime());
        detailVO.setUpdateTime(questionVO.getUpdateTime());
        
        // 查询标签
        detailVO.setTags(tagService.getTagsByQuestionId(id));
        
        // 查询回答
        List<AnswerVO> answers = answerService.getAnswersByQuestionId(id);
        
        // 将Integer转换为Boolean
        for (AnswerVO answer : answers) {
            answer.setIsAccepted(answer.getIsAccepted() != null && answer.getIsAccepted() == 1);
            answer.setIsPremium(answer.getIsPremium() != null && answer.getIsPremium() == 1);
        }
        
        detailVO.setAnswers(answers);
        
        // 查询相关问题
        List<QuestionVO> relatedQuestions = questionDao.getRelatedQuestions(id, 5);
        
        // 将Integer转换为Boolean
        for (QuestionVO question : relatedQuestions) {
            question.setIsPremium(question.getIsPremium() != null && question.getIsPremium() == 1);
        }
        
        detailVO.setRelatedQuestions(relatedQuestions);
        
        return detailVO;
    }
    
    @Override
    @Transactional
    public Long postQuestion(QuestionPostDTO questionPostDTO) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RuntimeException("用户未登录");
        }
        
        // 获取当前用户信息
        String username = authentication.getName();
        User user = userDao.selectByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 创建问题对象
        Question question = new Question();
        question.setTitle(questionPostDTO.getTitle());
        question.setContent(questionPostDTO.getContent());
        question.setAuthorId(user.getId());
        question.setIsPremium(questionPostDTO.getIsPremium() ? 1 : 0);
        question.setViewCount(0);
        question.setStatus(0); // 未解决
        question.setCreateTime(LocalDateTime.now());
        question.setUpdateTime(LocalDateTime.now());
        question.setDeleted(0);
        
        // 保存问题
        questionDao.insert(question);
        
        // 保存问题标签关联
        if (questionPostDTO.getTagIds() != null && !questionPostDTO.getTagIds().isEmpty()) {
            for (Long tagId : questionPostDTO.getTagIds()) {
                // 创建问题标签关联对象
                QuestionTag questionTag = new QuestionTag();
                questionTag.setQuestionId(question.getId());
                questionTag.setTagId(tagId);
                
                // 保存问题标签关联
                questionTagDao.insert(questionTag);
                
                // 增加标签使用次数
                tagDao.incrementCount(tagId);
            }
        }
        
        return question.getId();
    }
    
    @Override
    public boolean incrementViewCount(Long id) {
        return questionDao.incrementViewCount(id) > 0;
    }
    
    @Override
    public boolean updateStatus(Long id, Integer status) {
        Question question = new Question();
        question.setId(id);
        question.setStatus(status);
        question.setUpdateTime(LocalDateTime.now());
        return questionDao.updateById(question) > 0;
    }
} 