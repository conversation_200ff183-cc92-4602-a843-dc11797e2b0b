const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');

// 创建Express应用
const app = express();
const PORT = 8000;

// JWT密钥
const JWT_SECRET = 'materials-studio-secret-key';

// 中间件
app.use(morgan('dev')); // 日志
app.use(cors()); // 启用CORS
app.use(express.json()); // 解析JSON请求体

// 确保数据目录存在
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir);
}

// 加载或创建数据文件
function loadData(filename, defaultData = {}) {
  const filePath = path.join(dataDir, `${filename}.json`);
  if (fs.existsSync(filePath)) {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  }
  fs.writeFileSync(filePath, JSON.stringify(defaultData, null, 2), 'utf8');
  return defaultData;
}

// 保存数据
function saveData(filename, data) {
  const filePath = path.join(dataDir, `${filename}.json`);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
}

// 初始化数据
let users = loadData('users', {
  users: [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123', // 实际应用中密码应该加密存储
      role: 'admin',
      created_at: '2023-01-01T00:00:00Z'
    },
    {
      id: 2,
      username: 'member',
      email: '<EMAIL>',
      password: 'member123',
      role: 'member',
      created_at: '2023-01-02T00:00:00Z'
    },
    {
      id: 3,
      username: 'user',
      email: '<EMAIL>',
      password: 'user123',
      role: 'user',
      created_at: '2023-01-03T00:00:00Z'
    }
  ]
});

let questions = loadData('questions', {
  questions: [
    {
      id: 1,
      title: 'Materials Studio中如何进行分子动力学模拟？',
      content: '我正在尝试使用Materials Studio进行分子动力学模拟，但不太清楚具体的步骤和参数设置。请问有谁能提供详细的操作指南吗？',
      author_id: 3,
      tags: [1, 3],
      is_premium: false,
      view_count: 120,
      created_at: '2023-05-15T08:30:00Z',
      updated_at: '2023-05-15T08:30:00Z'
    },
    {
      id: 2,
      title: '如何在Materials Studio中计算吸附能？',
      content: '我需要计算一个分子在表面上的吸附能，请问在Materials Studio中应该如何设置计算参数？',
      author_id: 2,
      tags: [2, 4],
      is_premium: true,
      view_count: 85,
      created_at: '2023-05-16T10:15:00Z',
      updated_at: '2023-05-16T10:15:00Z'
    },
    {
      id: 3,
      title: 'Materials Studio中CASTEP模块的使用问题',
      content: '在使用CASTEP模块进行DFT计算时遇到了收敛问题，请问应该如何调整参数以提高计算效率和准确性？',
      author_id: 3,
      tags: [1, 5],
      is_premium: false,
      view_count: 62,
      created_at: '2023-05-18T14:45:00Z',
      updated_at: '2023-05-18T14:45:00Z'
    }
  ]
});

let answers = loadData('answers', {
  answers: [
    {
      id: 1,
      question_id: 1,
      content: '分子动力学模拟的基本步骤如下：\n1. 创建或导入分子结构\n2. 设置力场参数\n3. 设置模拟条件（温度、压力等）\n4. 运行模拟\n5. 分析结果\n\n详细步骤可以参考官方文档...',
      author_id: 2,
      is_accepted: false,
      is_premium: false,
      votes: 5,
      created_at: '2023-05-15T09:45:00Z',
      updated_at: '2023-05-15T09:45:00Z'
    },
    {
      id: 2,
      question_id: 1,
      content: '补充一点，在进行分子动力学模拟时，选择合适的力场非常重要。对于不同类型的体系，推荐的力场如下：\n- 有机分子：COMPASS、PCFF\n- 生物分子：CHARMM、AMBER\n- 无机材料：CLAYFF、INTERFACE\n\n另外，模拟前的结构优化也是必不可少的步骤...',
      author_id: 1,
      is_accepted: true,
      is_premium: true,
      votes: 12,
      created_at: '2023-05-15T10:30:00Z',
      updated_at: '2023-05-15T10:30:00Z'
    },
    {
      id: 3,
      question_id: 2,
      content: '计算吸附能的步骤：\n1. 分别优化吸附剂和吸附质\n2. 构建吸附构型并优化\n3. 计算吸附能：E_ads = E_complex - (E_adsorbent + E_adsorbate)\n\n注意要使用相同的计算方法和参数...',
      author_id: 1,
      is_accepted: true,
      is_premium: true,
      votes: 8,
      created_at: '2023-05-16T11:20:00Z',
      updated_at: '2023-05-16T11:20:00Z'
    }
  ]
});

let tags = loadData('tags', {
  tags: [
    { id: 1, name: '分子动力学', count: 120 },
    { id: 2, name: '吸附', count: 85 },
    { id: 3, name: '模拟', count: 200 },
    { id: 4, name: '表面', count: 65 },
    { id: 5, name: 'DFT', count: 150 }
  ]
});

let memberships = loadData('memberships', {
  memberships: [
    {
      user_id: 1,
      level: 'premium',
      level_name: '高级会员',
      is_active: true,
      start_date: '2023-01-01T00:00:00Z',
      end_date: '2024-01-01T00:00:00Z'
    },
    {
      user_id: 2,
      level: 'standard',
      level_name: '标准会员',
      is_active: true,
      start_date: '2023-01-02T00:00:00Z',
      end_date: '2023-07-02T00:00:00Z'
    }
  ]
});

let categories = loadData('categories', {
  categories: [
    { id: 1, name: '教程', count: 25 },
    { id: 2, name: '实例', count: 18 },
    { id: 3, name: '问题解决', count: 42 },
    { id: 4, name: '新功能', count: 10 }
  ]
});

let resources = loadData('resources', {
  resources: [
    {
      id: 1,
      title: 'Materials Studio基础教程',
      description: '介绍Materials Studio的基本操作和功能',
      type: 'video',
      url: 'https://example.com/videos/ms-basics.mp4',
      category_id: 1,
      is_premium: false,
      author_id: 1,
      created_at: '2023-03-10T08:00:00Z'
    },
    {
      id: 2,
      title: '分子动力学高级教程',
      description: '深入讲解分子动力学模拟的原理和高级技巧',
      type: 'document',
      url: 'https://example.com/docs/md-advanced.pdf',
      category_id: 1,
      is_premium: true,
      author_id: 1,
      created_at: '2023-03-15T09:30:00Z'
    },
    {
      id: 3,
      title: '催化剂设计案例分析',
      description: '使用Materials Studio设计和优化催化剂的实际案例',
      type: 'video',
      url: 'https://example.com/videos/catalyst-design.mp4',
      category_id: 2,
      is_premium: true,
      author_id: 2,
      created_at: '2023-04-05T14:15:00Z'
    }
  ]
});

// 中间件：检查认证
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ detail: '未提供认证Token' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ detail: 'Token无效或已过期' });
    }
    req.user = user;
    next();
  });
}

// 中间件：检查会员权限
function checkMemberPermission(req, res, next) {
  const userId = req.user.id;
  const membership = memberships.memberships.find(m => m.user_id === userId && m.is_active);
  
  if (!membership) {
    return res.status(403).json({ detail: '需要会员权限' });
  }
  
  req.membership = membership;
  next();
}

// 健康检查接口
app.get('/api/v1/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// 认证相关接口
app.post('/api/v1/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  if (!username || !password) {
    return res.status(400).json({ detail: '用户名和密码不能为空' });
  }
  
  const user = users.users.find(
    u => (u.username === username || u.email === username) && u.password === password
  );
  
  if (!user) {
    return res.status(401).json({ detail: '用户名或密码错误' });
  }
  
  const token = jwt.sign(
    { id: user.id, username: user.username, role: user.role },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
  
  res.status(200).json({
    access_token: token,
    token_type: 'bearer',
    user_id: user.id,
    username: user.username,
    role: user.role
  });
});

app.get('/api/v1/auth/me', authenticateToken, (req, res) => {
  const user = users.users.find(u => u.id === req.user.id);
  
  if (!user) {
    return res.status(404).json({ detail: '用户不存在' });
  }
  
  // 排除密码字段
  const { password, ...userWithoutPassword } = user;
  
  res.status(200).json(userWithoutPassword);
});

// 问答相关接口
app.get('/api/v1/qa/questions', (req, res) => {
  let { skip = 0, limit = 10, tag_id, author_id, search, only_premium, only_solved } = req.query;
  
  skip = parseInt(skip) || 0;
  limit = parseInt(limit) || 10;
  
  // 过滤问题
  let filteredQuestions = [...questions.questions];
  
  if (tag_id) {
    const tagId = parseInt(tag_id);
    filteredQuestions = filteredQuestions.filter(q => q.tags.includes(tagId));
  }
  
  if (author_id) {
    const authorId = parseInt(author_id);
    filteredQuestions = filteredQuestions.filter(q => q.author_id === authorId);
  }
  
  if (search) {
    const searchLower = search.toLowerCase();
    filteredQuestions = filteredQuestions.filter(
      q => q.title.toLowerCase().includes(searchLower) || 
           q.content.toLowerCase().includes(searchLower)
    );
  }
  
  if (only_premium === 'true') {
    filteredQuestions = filteredQuestions.filter(q => q.is_premium);
  }
  
  if (only_solved === 'true') {
    const questionIds = answers.answers
      .filter(a => a.is_accepted)
      .map(a => a.question_id);
    
    filteredQuestions = filteredQuestions.filter(q => questionIds.includes(q.id));
  }
  
  // 对问题进行排序（按创建时间降序）
  filteredQuestions.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
  
  // 分页
  const paginatedQuestions = filteredQuestions.slice(skip, skip + limit);
  
  // 增加作者信息和标签信息
  const questionsWithDetails = paginatedQuestions.map(q => {
    const author = users.users.find(u => u.id === q.author_id);
    const questionTags = q.tags.map(tagId => tags.tags.find(t => t.id === tagId));
    const answerCount = answers.answers.filter(a => a.question_id === q.id).length;
    
    return {
      ...q,
      author: author ? { id: author.id, username: author.username } : null,
      tags: questionTags,
      answer_count: answerCount
    };
  });
  
  res.status(200).json({
    total: filteredQuestions.length,
    skip,
    limit,
    questions: questionsWithDetails
  });
});

app.get('/api/v1/qa/questions/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const question = questions.questions.find(q => q.id === id);
  
  if (!question) {
    return res.status(404).json({ detail: '问题不存在' });
  }
  
  // 获取作者信息
  const author = users.users.find(u => u.id === question.author_id);
  
  // 获取标签信息
  const questionTags = question.tags.map(tagId => tags.tags.find(t => t.id === tagId));
  
  // 获取问题的回答
  const questionAnswers = answers.answers
    .filter(a => a.question_id === id)
    .sort((a, b) => {
      // 已接受的回答排在前面
      if (a.is_accepted && !b.is_accepted) return -1;
      if (!a.is_accepted && b.is_accepted) return 1;
      // 然后按投票数排序
      return b.votes - a.votes;
    })
    .map(a => {
      const answerAuthor = users.users.find(u => u.id === a.author_id);
      return {
        ...a,
        author: answerAuthor ? { id: answerAuthor.id, username: answerAuthor.username } : null
      };
    });
  
  const result = {
    ...question,
    author: author ? { id: author.id, username: author.username } : null,
    tags: questionTags,
    answers: questionAnswers
  };
  
  res.status(200).json(result);
});

app.post('/api/v1/qa/questions', authenticateToken, (req, res) => {
  const { title, content, tags, is_premium } = req.body;
  
  if (!title || !content) {
    return res.status(400).json({ detail: '标题和内容不能为空' });
  }
  
  // 如果是会员内容，检查用户是否有会员权限
  if (is_premium) {
    const membership = memberships.memberships.find(
      m => m.user_id === req.user.id && m.is_active
    );
    
    if (!membership) {
      return res.status(403).json({ detail: '需要会员权限才能创建会员内容' });
    }
  }
  
  // 创建新问题
  const newId = Math.max(...questions.questions.map(q => q.id), 0) + 1;
  const now = new Date().toISOString();
  
  const newQuestion = {
    id: newId,
    title,
    content,
    author_id: req.user.id,
    tags: tags || [],
    is_premium: !!is_premium,
    view_count: 0,
    created_at: now,
    updated_at: now
  };
  
  questions.questions.push(newQuestion);
  saveData('questions', questions);
  
  res.status(201).json(newQuestion);
});

app.get('/api/v1/qa/tags', (req, res) => {
  res.status(200).json({ tags: tags.tags });
});

app.post('/api/v1/qa/questions/:id/answers', authenticateToken, (req, res) => {
  const questionId = parseInt(req.params.id);
  const { content, is_premium } = req.body;
  
  if (!content) {
    return res.status(400).json({ detail: '回答内容不能为空' });
  }
  
  const question = questions.questions.find(q => q.id === questionId);
  
  if (!question) {
    return res.status(404).json({ detail: '问题不存在' });
  }
  
  // 如果是会员内容，检查用户是否有会员权限
  if (is_premium) {
    const membership = memberships.memberships.find(
      m => m.user_id === req.user.id && m.is_active
    );
    
    if (!membership) {
      return res.status(403).json({ detail: '需要会员权限才能创建会员内容' });
    }
  }
  
  // 创建新回答
  const newId = Math.max(...answers.answers.map(a => a.id), 0) + 1;
  const now = new Date().toISOString();
  
  const newAnswer = {
    id: newId,
    question_id: questionId,
    content,
    author_id: req.user.id,
    is_accepted: false,
    is_premium: !!is_premium,
    votes: 0,
    created_at: now,
    updated_at: now
  };
  
  answers.answers.push(newAnswer);
  saveData('answers', answers);
  
  // 获取作者信息
  const author = users.users.find(u => u.id === req.user.id);
  
  res.status(201).json({
    ...newAnswer,
    author: author ? { id: author.id, username: author.username } : null
  });
});

// 用户相关接口
app.get('/api/v1/users/membership', authenticateToken, (req, res) => {
  const membership = memberships.memberships.find(m => m.user_id === req.user.id);
  
  if (!membership) {
    return res.status(200).json({
      is_active: false,
      level: 'free',
      level_name: '免费用户'
    });
  }
  
  res.status(200).json(membership);
});

app.get('/api/v1/users/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const user = users.users.find(u => u.id === id);
  
  if (!user) {
    return res.status(404).json({ detail: '用户不存在' });
  }
  
  // 排除密码字段
  const { password, ...userWithoutPassword } = user;
  
  // 获取用户的会员信息
  const membership = memberships.memberships.find(m => m.user_id === id);
  
  // 获取用户提问数量
  const questionCount = questions.questions.filter(q => q.author_id === id).length;
  
  // 获取用户回答数量
  const answerCount = answers.answers.filter(a => a.author_id === id).length;
  
  const result = {
    ...userWithoutPassword,
    membership: membership || { is_active: false, level: 'free', level_name: '免费用户' },
    stats: {
      question_count: questionCount,
      answer_count: answerCount
    }
  };
  
  res.status(200).json(result);
});

// 内容相关接口
app.get('/api/v1/content/resources', (req, res) => {
  let { skip = 0, limit = 10, category_id } = req.query;
  
  skip = parseInt(skip) || 0;
  limit = parseInt(limit) || 10;
  
  // 过滤资源
  let filteredResources = [...resources.resources];
  
  if (category_id) {
    const categoryId = parseInt(category_id);
    filteredResources = filteredResources.filter(r => r.category_id === categoryId);
  }
  
  // 分页
  const paginatedResources = filteredResources.slice(skip, skip + limit);
  
  // 增加分类和作者信息
  const resourcesWithDetails = paginatedResources.map(r => {
    const category = categories.categories.find(c => c.id === r.category_id);
    const author = users.users.find(u => u.id === r.author_id);
    
    return {
      ...r,
      category: category ? { id: category.id, name: category.name } : null,
      author: author ? { id: author.id, username: author.username } : null
    };
  });
  
  res.status(200).json({
    total: filteredResources.length,
    skip,
    limit,
    resources: resourcesWithDetails
  });
});

app.get('/api/v1/content/categories', (req, res) => {
  res.status(200).json({ categories: categories.categories });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`模拟服务器运行在 http://localhost:${PORT}`);
  console.log(`API基础路径: http://localhost:${PORT}/api/v1`);
  console.log('可用测试账号:');
  console.log('- 管理员: admin / admin123');
  console.log('- 会员: member / member123');
  console.log('- 普通用户: user / user123');
}); 